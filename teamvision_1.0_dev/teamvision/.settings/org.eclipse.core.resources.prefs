eclipse.preferences.version=1
encoding//agent/business/driver/business_driver_loader.py=utf-8
encoding//agent/request_handlers/driver_pool.py=utf-8
encoding//agent/request_handlers/socket_handler.py=utf-8
encoding//business/auth_user/system_role_service.py=utf-8
encoding//business/auth_user/user_service.py=utf-8
encoding//business/automationtesting/autoagentservice.py=utf8
encoding//business/automationtesting/automationtaskservice.py=utf8
encoding//business/automationtesting/automobiledeviceservice.py=utf8
encoding//business/automationtesting/autorunresultservice.py=utf8
encoding//business/automationtesting/autotestconfigservice.py=utf8
encoding//business/ci/ci_agent_service.py=utf-8
encoding//business/ci/ci_credential_service.py=utf-8
encoding//business/ci/ci_deploy_server_service.py=utf-8
encoding//business/ci/ci_task_config_service.py=utf-8
encoding//business/ci/ci_task_parameter_service.py=utf-8
encoding//business/ci/ci_task_queue_service.py=utf-8
encoding//business/ci/ci_task_service.py=utf-8
encoding//business/ci/ci_testing_history_service.py=utf-8
encoding//business/common/emailservice.py=utf-8
encoding//business/common/excel_file_service.py=utf-8
encoding//business/common/file_info_service.py=utf-8
encoding//business/common/mongodb_service.py=utf-8
encoding//business/common/redis_service.py=utf-8
encoding//business/logcat/logger_service.py=utf-8
encoding//business/productquality/codequalityservice.py=utf-8
encoding//business/project/fortesting_service.py=utf-8
encoding//business/project/issue_service.py=utf-8
encoding//business/project/issue_statistics_service.py=utf-8
encoding//business/project/module_service.py=utf-8
encoding//business/project/project_service.py=utf-8
encoding//business/project/tag_service.py=utf-8
encoding//business/project/task_service.py=utf-8
encoding//business/project/version_service.py=utf-8
encoding//business/testjob/codecommitlogservice.py=utf-8
encoding//business/testjob/projectversionservice.py=utf8
encoding//business/testjob/testbuildservice.py=utf8
encoding//business/testjob/testjobservice.py=utf8
encoding//business/testjob/testprojectservice.py=utf8
encoding//business/testjob/testsubmitionservice.py=utf8
encoding//dataaccess/common/dal_dictvalue.py=utf-8
encoding//deploy_script/settings.py=utf-8
encoding//deploy_script/test_settings.py=utf-8
encoding//teamvision/administrate/config.py=utf-8
encoding//teamvision/administrate/datamodels/testjobdbrouter.py=utf-8
encoding//teamvision/administrate/pagefactory/admin_system_role_pageworker.py=utf-8
encoding//teamvision/administrate/pagefactory/admin_template_path.py=utf-8
encoding//teamvision/administrate/urlrouter/admin_device_urls.py=utf-8
encoding//teamvision/administrate/urlrouter/admin_permission_urls.py=utf-8
encoding//teamvision/administrate/urlrouter/admin_project_urls.py=utf-8
encoding//teamvision/administrate/urlrouter/admin_system_role_urls.py=utf-8
encoding//teamvision/administrate/urlrouter/admin_urls.py=utf-8
encoding//teamvision/administrate/urlrouter/admin_user_urls.py=utf-8
encoding//teamvision/administrate/urlrouter/admin_usergroup_urls.py=utf-8
encoding//teamvision/administrate/viewmodels/admin_left_nav_bar.py=utf-8
encoding//teamvision/administrate/viewmodels/admin_sub_nav_bar.py=utf-8
encoding//teamvision/administrate/viewmodels/vm_admin_permission.py=utf-8
encoding//teamvision/administrate/viewmodels/vm_admin_system_role.py=utf-8
encoding//teamvision/administrate/views/admin_system_role_view.py=utf-8
encoding//teamvision/administrate/views/admin_user_group_view.py=utf-8
encoding//teamvision/administrate/views/admin_user_view.py=utf-8
encoding//teamvision/api/auth/urlrouter/auth_urls.py=utf-8
encoding//teamvision/api/auth/urlrouter/userurls.py=utf-8
encoding//teamvision/api/auth/views/userview.py=utf-8
encoding//teamvision/api/ci/filters/auto_case_filter.py=utf-8
encoding//teamvision/api/ci/filters/auto_case_result_filter.py=utf-8
encoding//teamvision/api/ci/filters/ci_pagination.py=utf-8
encoding//teamvision/api/ci/filters/ci_task_filter.py=utf-8
encoding//teamvision/api/ci/json_factory/ci_factory.py=utf-8
encoding//teamvision/api/ci/models.py=utf-8
encoding//teamvision/api/ci/serializer/autotesting_serializer.py=utf-8
encoding//teamvision/api/ci/serializer/ci_serializer.py=utf-8
encoding//teamvision/api/ci/urlrouter/auto_testing_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/case_tag_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_agent_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_crendentials_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_server_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_service_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_task_basic_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_task_plugin_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_task_urls.py=utf-8
encoding//teamvision/api/ci/urlrouter/ci_urls.py=utf-8
encoding//teamvision/api/ci/views/auto_case_result_view.py=utf-8
encoding//teamvision/api/ci/views/auto_case_view.py=utf-8
encoding//teamvision/api/ci/views/auto_testcase_view.py=utf-8
encoding//teamvision/api/ci/views/auto_testing_result_view.py=utf-8
encoding//teamvision/api/ci/views/case_tag_view.py=utf-8
encoding//teamvision/api/ci/views/ci_agent_view.py=utf-8
encoding//teamvision/api/ci/views/ci_deploy_server_view.py=utf-8
encoding//teamvision/api/ci/views/ci_task_basic_view.py=utf-8
encoding//teamvision/api/ci/views/ci_task_history_view.py=utf-8
encoding//teamvision/api/ci/views/ci_task_parameter_view.py=utf-8
encoding//teamvision/api/ci/views/ci_task_view.py=utf-8
encoding//teamvision/api/ci/views/service_host_view.py=utf-8
encoding//teamvision/api/common/filters/agent_filter.py=utf-8
encoding//teamvision/api/common/filters/taskqueue_filter.py=utf-8
encoding//teamvision/api/common/models.py=utf-8
encoding//teamvision/api/common/serializer/agent_serializer.py=utf-8
encoding//teamvision/api/common/serializer/dic_config_serializer.py=utf-8
encoding//teamvision/api/common/serializer/simple_mq_serializer.py=utf-8
encoding//teamvision/api/common/serializer/task_queue_serializer.py=utf-8
encoding//teamvision/api/common/urlrouter/agent_urls.py=utf-8
encoding//teamvision/api/common/urlrouter/common_urls.py=utf-8
encoding//teamvision/api/common/urlrouter/dicconfig_urls.py=utf-8
encoding//teamvision/api/common/urlrouter/file_info_urls.py=utf-8
encoding//teamvision/api/common/urlrouter/simple_mq_urls.py=utf-8
encoding//teamvision/api/common/urlrouter/task_queue_urls.py=utf-8
encoding//teamvision/api/common/views/agent_view.py=utf-8
encoding//teamvision/api/common/views/dic_config_view.py=utf-8
encoding//teamvision/api/common/views/file_archive_view.py=utf-8
encoding//teamvision/api/common/views/simple_mq_view.py=utf-8
encoding//teamvision/api/common/views/task_queue_view.py=utf-8
encoding//teamvision/api/logcat/urlrouter/logcat_logger_urls.py=utf-8
encoding//teamvision/api/logcat/urlrouter/logcat_story_log_urls.py=utf-8
encoding//teamvision/api/logcat/urlrouter/logcat_urls.py=utf-8
encoding//teamvision/api/logcat/views/logcat_business_log_view.py=utf-8
encoding//teamvision/api/logcat/views/logcat_logger_view.py=utf-8
encoding//teamvision/api/mongo_models.py=utf-8
encoding//teamvision/api/project/filters/issue_daily_statistics_filter.py=utf-8
encoding//teamvision/api/project/filters/project_issue_filter.py=utf-8
encoding//teamvision/api/project/models.py=utf-8
encoding//teamvision/api/project/render/project_version_render.py=utf-8
encoding//teamvision/api/project/serializer/project_issue_statistics_serializer.py=utf-8
encoding//teamvision/api/project/serializer/project_serializer.py=utf-8
encoding//teamvision/api/project/urlrouter/project_fortesting_urls.py=utf-8
encoding//teamvision/api/project/urlrouter/project_issue_statistics_urls.py=utf-8
encoding//teamvision/api/project/urlrouter/project_issue_urls.py=utf-8
encoding//teamvision/api/project/urlrouter/project_member_urls.py=utf-8
encoding//teamvision/api/project/urlrouter/project_urls.py=utf-8
encoding//teamvision/api/project/urlrouter/project_version_urls.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_highchart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_issue_category_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_issue_property_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_issue_resolve_result_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_issue_severity_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_module_issue_column_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_new_issue_trend_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_total_issue_trend_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_unclosed_issue_column_chart.py=utf-8
encoding//teamvision/api/project/viewmodel/project_statistics_charts/vm_version_issue_column_chart.py=utf-8
encoding//teamvision/api/project/views/issue_statistics_view.py=utf-8
encoding//teamvision/api/project/views/project_fortesting_view.py=utf-8
encoding//teamvision/api/project/views/project_issue_view.py=utf-8
encoding//teamvision/api/project/views/project_memeber_view.py=utf-8
encoding//teamvision/api/project/views/project_version_view.py=utf-8
encoding//teamvision/auth_extend/user/datamodels/testjobdbrouter.py=utf-8
encoding//teamvision/auth_extend/user/models.py=utf-8
encoding//teamvision/auth_extend/user/pagefactory/user_registration_pageworker.py=utf-8
encoding//teamvision/auth_extend/user/templatetags/auth_required.py=utf-8
encoding//teamvision/auth_extend/user/templatetags/auth_required_node.py=utf-8
encoding//teamvision/auth_extend/user/templatetags/project_role_required_node.py=utf-8
encoding//teamvision/auth_extend/user/urlrouter/user_urls.py=utf-8
encoding//teamvision/auth_extend/user/views/auth_extend_user_view.py=utf-8
encoding//teamvision/automationtesting/datamodels/automationtaskdbrouter.py=utf-8
encoding//teamvision/automationtesting/urlrouter/autoagenturls.py=utf-8
encoding//teamvision/automationtesting/urlrouter/automobiledeviceurls.py=utf-8
encoding//teamvision/automationtesting/urlrouter/autorunresulturls.py=utf-8
encoding//teamvision/automationtesting/urlrouter/autotaskurls.py=utf-8
encoding//teamvision/automationtesting/urlrouter/autotestconfigurls.py=utf-8
encoding//teamvision/automationtesting/urls.py=utf-8
encoding//teamvision/automationtesting/viewmodels/vm_autoagent.py=utf-8
encoding//teamvision/automationtesting/viewmodels/vm_automationtask.py=utf-8
encoding//teamvision/automationtesting/viewmodels/vm_automobiledevice.py=utf-8
encoding//teamvision/automationtesting/viewmodels/vm_autorunresult.py=utf-8
encoding//teamvision/automationtesting/viewmodels/vm_autotestconfig.py=utf-8
encoding//teamvision/automationtesting/views/autoagentview.py=utf-8
encoding//teamvision/automationtesting/views/automationtaskview.py=utf-8
encoding//teamvision/automationtesting/views/automobiledeviceview.py=utf-8
encoding//teamvision/automationtesting/views/autorunresultview.py=utf-8
encoding//teamvision/automationtesting/views/autotestconfigview.py=utf-8
encoding//teamvision/automationtesting/views/commonview.py=utf-8
encoding//teamvision/ci/__init__.py=utf-8
encoding//teamvision/ci/admin.py=utf-8
encoding//teamvision/ci/datamodels/testjobdbrouter.py=utf-8
encoding//teamvision/ci/migrations/0001_initial.py=utf-8
encoding//teamvision/ci/migrations/0002_autocase_autocaseresult_autotestingtaskresult_cicredentials_cideployservice_ciserver_citask_citaskhi.py=utf-8
encoding//teamvision/ci/models.py=utf-8
encoding//teamvision/ci/pagefactory/ci_build_pageworker.py=utf-8
encoding//teamvision/ci/pagefactory/ci_common_pageworker.py=utf-8
encoding//teamvision/ci/pagefactory/ci_dashboard_pageworker.py=utf-8
encoding//teamvision/ci/pagefactory/ci_pageworker.py=utf-8
encoding//teamvision/ci/pagefactory/ci_plugin_pageworker.py=utf-8
encoding//teamvision/ci/pagefactory/ci_settings_pageworker.py=utf-8
encoding//teamvision/ci/pagefactory/ci_task_pageworker.py=utf-8
encoding//teamvision/ci/pagefactory/ci_template_path.py=utf-8
encoding//teamvision/ci/pagefactory/ci_testing_pageworker.py=utf-8
encoding//teamvision/ci/urlrouter/ci_build_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_dashboard_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_deploy_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_history_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_plugin_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_service_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_settings_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_task_parameter_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_task_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_testing_urls.py=utf-8
encoding//teamvision/ci/urlrouter/ci_urls.py=utf-8
encoding//teamvision/ci/viewmodels/ci_left_nav_bar.py=utf-8
encoding//teamvision/ci/viewmodels/ci_sub_nav_bar.py=utf-8
encoding//teamvision/ci/viewmodels/ci_task_property_nav_bar.py=utf-8
encoding//teamvision/ci/viewmodels/plugins/vm_auto_apitesting.py=utf-8
encoding//teamvision/ci/viewmodels/plugins/vm_auto_webui_testing.py=utf-8
encoding//teamvision/ci/viewmodels/plugins/vm_ci_build.py=utf-8
encoding//teamvision/ci/viewmodels/plugins/vm_ci_plugin.py=utf-8
encoding//teamvision/ci/viewmodels/plugins/vm_ci_shell_build.py=utf-8
encoding//teamvision/ci/viewmodels/plugins/vm_ci_shell_command.py=utf-8
encoding//teamvision/ci/viewmodels/plugins/vm_ci_xctest.py=utf-8
encoding//teamvision/ci/viewmodels/vm_auto_case_result.py=utf-8
encoding//teamvision/ci/viewmodels/vm_ci_agent.py=utf-8
encoding//teamvision/ci/viewmodels/vm_ci_casetag.py=utf-8
encoding//teamvision/ci/viewmodels/vm_ci_task.py=utf-8
encoding//teamvision/ci/viewmodels/vm_ci_task_basic_section.py=utf-8
encoding//teamvision/ci/viewmodels/vm_ci_task_config.py=utf-8
encoding//teamvision/ci/viewmodels/vm_task_parameter_group.py=utf-8
encoding//teamvision/ci/views/ci_build_view.py=utf-8
encoding//teamvision/ci/views/ci_dashboard_view.py=utf-8
encoding//teamvision/ci/views/ci_deploy_view.py=utf-8
encoding//teamvision/ci/views/ci_plugin_view.py=utf-8
encoding//teamvision/ci/views/ci_service_view.py=utf-8
encoding//teamvision/ci/views/ci_settings_view.py=utf-8
encoding//teamvision/ci/views/ci_task_history_view.py=utf-8
encoding//teamvision/ci/views/ci_task_parameter_view.py=utf-8
encoding//teamvision/ci/views/ci_task_view.py=utf-8
encoding//teamvision/ci/views/ci_testing_view.py=utf-8
encoding//teamvision/decorators/administrate.py=utf-8
encoding//teamvision/device/models.py=utf-8
encoding//teamvision/device/urlrouter/device_borrowor.py=utf-8
encoding//teamvision/device/urlrouter/device_urls.py=utf-8
encoding//teamvision/device/views/device_view.py=utf-8
encoding//teamvision/env/config.py=utf-8
encoding//teamvision/env/models.py=utf-8
encoding//teamvision/env/pagefactory/env_dashboard_pageworker.py=utf-8
encoding//teamvision/env/pagefactory/env_portal_pageworker.py=utf-8
encoding//teamvision/env/urlrouter/env_dashboard_urls.py=utf-8
encoding//teamvision/env/urlrouter/env_portal_urls.py=utf-8
encoding//teamvision/env/urlrouter/env_urls.py=utf-8
encoding//teamvision/env/viewmodels/vm_env.py=utf-8
encoding//teamvision/env/views/dashboard_view.py=utf-8
encoding//teamvision/env/views/portal_view.py=utf-8
encoding//teamvision/gatesidelib/githelper.py=utf-8
encoding//teamvision/home/<USER>
encoding//teamvision/home/<USER>/testjobdbrouter.py=utf-8
encoding//teamvision/home/<USER>
encoding//teamvision/home/<USER>/home_dashboard_pageworker.py=utf-8
encoding//teamvision/home/<USER>/home_fortesting_pageworker.py=utf-8
encoding//teamvision/home/<USER>/home_issue_pageworker.py=utf-8
encoding//teamvision/home/<USER>/home_task_pageworker.py=utf-8
encoding//teamvision/home/<USER>/home_template_path.py=utf-8
encoding//teamvision/home/<USER>/home_unlogin_pageworker.py=utf-8
encoding//teamvision/home/<USER>/home_webapps_pageworker.py=utf-8
encoding//teamvision/home/<USER>/home_autotask_urls.py=utf-8
encoding//teamvision/home/<USER>/home_dashboard_urls.py=utf-8
encoding//teamvision/home/<USER>/home_device_urls.py=utf-8
encoding//teamvision/home/<USER>/home_fortesting_urls.py=utf-8
encoding//teamvision/home/<USER>/home_issue_urls.py=utf-8
encoding//teamvision/home/<USER>/home_page_urls.py=utf-8
encoding//teamvision/home/<USER>/home_project_urls.py=utf-8
encoding//teamvision/home/<USER>/home_task_urls.py=utf-8
encoding//teamvision/home/<USER>/home_testjob_urls.py=utf-8
encoding//teamvision/home/<USER>/home_urls.py=utf-8
encoding//teamvision/home/<USER>/home_webapps_urls.py=utf-8
encoding//teamvision/home/<USER>/home_left_nav_bar.py=utf-8
encoding//teamvision/home/<USER>/home_sub_nav_bar.py=utf-8
encoding//teamvision/home/<USER>/vm_home.py=utf-8
encoding//teamvision/home/<USER>/home_autotask_view.py=utf-8
encoding//teamvision/home/<USER>/home_dashboard_view.py=utf-8
encoding//teamvision/home/<USER>/home_device_view.py=utf-8
encoding//teamvision/home/<USER>/home_fortesting_view.py=utf-8
encoding//teamvision/home/<USER>/home_issue_view.py=utf-8
encoding//teamvision/home/<USER>/home_page_view.py=utf-8
encoding//teamvision/home/<USER>/home_project_view.py=utf-8
encoding//teamvision/home/<USER>/home_task_view.py=utf-8
encoding//teamvision/home/<USER>/home_testjob_view.py=utf-8
encoding//teamvision/home/<USER>/home_webapps_view.py=utf-8
encoding//teamvision/issue/urlrouter/issue_urls.py=utf-8
encoding//teamvision/logcat/urlrouter/logcat_home.py=utf-8
encoding//teamvision/logcat/urlrouter/logcat_urls.py=utf-8
encoding//teamvision/logcat/views/logger_view.py=utf-8
encoding//teamvision/pagefactory/worker.py=utf-8
encoding//teamvision/productquality/config.py=utf-8
encoding//teamvision/productquality/datamodels/productqualitydbrouter.py=utf-8
encoding//teamvision/productquality/urlrouter/bugreporturls.py=utf-8
encoding//teamvision/productquality/urlrouter/codequalityurls.py=utf-8
encoding//teamvision/productquality/views/bugreportview.py=utf-8
encoding//teamvision/productquality/views/codequalityview.py=utf-8
encoding//teamvision/productquality/views/commonview.py=utf-8
encoding//teamvision/project/datamodels/testjobdbrouter.py=utf-8
encoding//teamvision/project/migrations/0002_auto_20180312_1525.py=utf-8
encoding//teamvision/project/models.py=utf-8
encoding//teamvision/project/mongo_models.py=utf-8
encoding//teamvision/project/pagefactory/project_common_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_dashboard_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_fortesting_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_issue_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_portal_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_settings_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_statistics_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_task_pageworker.py=utf-8
encoding//teamvision/project/pagefactory/project_template_path.py=utf-8
encoding//teamvision/project/pagefactory/project_version_pageworker.py=utf-8
encoding//teamvision/project/urlrouter/project_archive_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_common_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_dashboard_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_fortesting_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_issue_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_member_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_portal_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_settings_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_statistics_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_task_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_version_urls.py=utf-8
encoding//teamvision/project/urlrouter/project_webhook_urls.py=utf-8
encoding//teamvision/project/viewmodels/project_left_nav_bar.py=utf-8
encoding//teamvision/project/viewmodels/project_statistics_charts/vm_issue_status_statistics_chart.py=utf-8
encoding//teamvision/project/viewmodels/project_sub_nav_bar.py=utf-8
encoding//teamvision/project/viewmodels/vm_issue_activity.py=utf-8
encoding//teamvision/project/viewmodels/vm_platform.py=utf-8
encoding//teamvision/project/viewmodels/vm_product.py=utf-8
encoding//teamvision/project/viewmodels/vm_project.py=utf-8
encoding//teamvision/project/viewmodels/vm_project_fortesting.py=utf-8
encoding//teamvision/project/viewmodels/vm_project_issue.py=utf-8
encoding//teamvision/project/viewmodels/vm_project_issue_field.py=utf-8
encoding//teamvision/project/viewmodels/vm_project_issue_filter.py=utf-8
encoding//teamvision/project/viewmodels/vm_project_member.py=utf-8
encoding//teamvision/project/viewmodels/vm_project_module.py=utf-8
encoding//teamvision/project/viewmodels/vm_project_version.py=utf-8
encoding//teamvision/project/viewmodels/vm_tag.py=utf-8
encoding//teamvision/project/views/autotask_view.py=utf-8
encoding//teamvision/project/views/portal_view.py=utf-8
encoding//teamvision/project/views/project_archive_view.py=utf-8
encoding//teamvision/project/views/project_common_view.py=utf-8
encoding//teamvision/project/views/project_dashboard_view.py=utf-8
encoding//teamvision/project/views/project_fortesting_view.py=utf-8
encoding//teamvision/project/views/project_issue_view.py=utf-8
encoding//teamvision/project/views/project_member_view.py=utf-8
encoding//teamvision/project/views/project_settings_view.py=utf-8
encoding//teamvision/project/views/project_statistics_view.py=utf-8
encoding//teamvision/project/views/project_task_view.py=utf-8
encoding//teamvision/project/views/project_version_view.py=utf-8
encoding//teamvision/project/views/project_webhook_view.py=utf-8
encoding//teamvision/project/views/testjob_view.py=utf-8
encoding//teamvision/resources/project/resource_string.py=utf-8
encoding//teamvision/settings.py=utf-8
encoding//teamvision/toolbox/urlrouter/toolboxurls.py=utf-8
encoding//teamvision/toolbox/views/commonview.py=utf-8
encoding//teamvision/toolbox/views/toolboxview.py=utf-8
encoding//teamvision/user_center/urlrouter/ucenter_account_urls.py=utf-8
encoding//teamvision/user_center/urlrouter/ucenter_urls.py=utf-8
encoding//teamvision/userauthority/urls.py=utf-8
encoding//teamvision/userauthority/viewmodels/vm_testsubmition.py=utf-8
encoding//teamvision/userauthority/views/commonview.py=utf-8
encoding//teamvision/web_wsgi.py=utf-8
encoding//teamvision/websocket_wsgi.py=utf-8
encoding//gatesidelib/color_helper.py=utf-8
encoding//gatesidelib/common/simplelogger.py=utf-8
encoding//gatesidelib/datetimehelper.py=utf-8
encoding//gatesidelib/filehelper.py=utf-8
encoding//gatesidelib/githelper.py=utf-8
encoding//gatesidelib/mongodb_helper.py=utf-8
encoding//gatesidelib/redis_helper.py=utf-8
encoding//gatesidelib/svnhelper.py=utf-8
encoding//model_managers/ci_model_manager.py=utf-8
encoding//model_managers/home_model_manager.py=utf-8
encoding//model_managers/model_manager.py=utf-8
encoding//model_managers/mongo_model/mongo_model_manager.py=utf-8
encoding//model_managers/project_model_manager.py=utf-8
encoding/manage.py=utf-8
