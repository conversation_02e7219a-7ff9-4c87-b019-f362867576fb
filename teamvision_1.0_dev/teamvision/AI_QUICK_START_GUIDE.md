# TeamVision AI测试助手 - 快速开始指南

## 🚀 快速部署

### 1. 自动部署（推荐）

运行自动部署脚本：

```bash
cd teamvision_1.0_dev/teamvision
./deploy_ai_features.sh
```

脚本会自动完成：
- ✅ 环境检查
- ✅ 依赖安装
- ✅ 数据库迁移
- ✅ AI数据初始化
- ✅ 配置检查
- ✅ 集成测试

### 2. 手动部署

如果自动部署失败，可以手动执行以下步骤：

#### 步骤1: 安装Python依赖
```bash
pip install requests==2.31.0
pip install openai==1.3.0
pip install httpx==0.25.0
pip install pydantic==2.5.0
pip install xlwt==1.3.0
```

#### 步骤2: 配置Django设置
在 `teamvision/settings.py` 中添加：

```python
INSTALLED_APPS = [
    # ... 其他应用
    'teamvision.ai',
]

# RAGFlow配置
RAGFLOW_BASE_URL = 'http://your-ragflow-server:9380'
RAGFLOW_API_KEY = 'your_api_key_here'
RAGFLOW_AGENT_ID = 'your_agent_id_here'
RAGFLOW_TIMEOUT = 30

# Redis缓存配置（可选）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

#### 步骤3: 数据库迁移
```bash
python manage.py makemigrations ai
python manage.py migrate
```

#### 步骤4: 初始化AI数据
```bash
python manage.py init_ai_data --create-default-config --test-ragflow-connection
```

#### 步骤5: 测试集成
```bash
python test_ai_integration.py
```

## 🎯 功能使用指南

### 1. AI测试用例生成

#### 前端使用
在测试用例管理页面，点击"AI助手"按钮：

1. **选择需求**: 从项目需求列表中选择要生成测试用例的需求
2. **配置参数**: 
   - 生成类型：功能测试、边界测试、异常测试等
   - 生成数量：1-50个
   - 优先级：高、中、低
3. **生成用例**: 点击"生成测试用例"按钮
4. **预览结果**: 查看AI生成的测试用例
5. **选择采纳**: 勾选需要的用例，添加到测试库

#### API调用
```javascript
// 生成测试用例
const response = await aiApi.quickGenerateTestCasesApi(
    projectId, 
    "用户登录功能需求描述", 
    {
        module_name: "用户管理",
        generation_type: "functional",
        case_count: 10,
        priority_level: "medium"
    }
)

// 采纳测试用例
await aiApi.batchAcceptTestCasesApi([1, 2, 3], parentId, moduleId)
```

### 2. AI对话交互

#### 使用场景
- 咨询测试策略建议
- 优化现有测试用例
- 分析测试覆盖度
- 获取测试最佳实践

#### 对话示例
```
用户: "请帮我分析用户登录功能的测试覆盖度"
AI: "我来为您分析用户登录功能的测试覆盖度...

📊 覆盖度分析结果：
- 正常登录场景：✅ 已覆盖
- 错误密码场景：✅ 已覆盖  
- 用户名不存在：❌ 缺失
- 账户锁定场景：❌ 缺失
- 验证码验证：❌ 缺失

💡 建议补充以下测试用例：
1. 用户名不存在时的错误提示
2. 连续错误登录导致账户锁定
3. 验证码错误的处理逻辑"
```

### 3. 测试用例优化

#### 自动优化
```javascript
// 优化单个测试用例
const response = await aiApi.smartOptimizeTestCaseApi(caseId, 'quality')

// 批量优化
const response = await aiApi.batchOptimizeTestCasesApi([1, 2, 3], 'completeness')
```

#### 优化维度
- **质量优化**: 提升用例描述的清晰度和准确性
- **完整性优化**: 补充缺失的测试步骤和预期结果
- **可执行性优化**: 增强用例的可操作性
- **覆盖度优化**: 扩展测试场景覆盖范围

### 4. 使用统计与监控

#### 查看统计
```javascript
// 获取使用统计
const stats = await aiApi.getAIUsageStatisticsApi({
    project_id: projectId,
    date_from: '2024-01-01',
    date_to: '2024-01-31'
})

// 健康检查
const health = await aiApi.checkAIServiceStatusApi()
```

#### 统计指标
- 生成次数和成功率
- 用例采纳率
- 用户活跃度
- 服务响应时间

## 🔧 配置管理

### 1. 提示词模板配置

创建自定义提示词模板：

```python
from teamvision.ai.models import AIConfiguration

# 创建UI测试模板
AIConfiguration.objects.create(
    config_type='prompt',
    config_key='ui_test_template',
    config_value={
        'name': 'UI测试用例模板',
        'template': '''
请为以下UI功能生成测试用例：

功能描述：{requirement_description}
页面/组件：{module_name}

请重点关注：
1. 界面元素显示
2. 用户交互操作  
3. 响应式布局
4. 浏览器兼容性

请生成{case_count}个UI测试用例。
        '''
    },
    description='UI测试用例生成模板',
    is_active=True
)
```

### 2. 生成限制配置

```python
# 设置生成限制
AIConfiguration.objects.create(
    config_type='ragflow',
    config_key='generation_limits',
    config_value={
        'max_cases_per_request': 50,
        'max_requests_per_hour': 20,
        'max_requests_per_day': 100
    },
    description='生成限制配置',
    is_active=True
)
```

## 🐛 故障排除

### 常见问题

#### 1. RAGFlow连接失败
**症状**: AI生成功能不可用，提示连接失败

**解决方案**:
```bash
# 检查RAGFlow服务状态
curl -X GET http://your-ragflow-server:9380/health

# 验证API密钥
python manage.py shell -c "
from teamvision.ai.ragflow_client import ragflow_client
print(ragflow_client.test_connection())
"

# 检查配置
python manage.py shell -c "
from django.conf import settings
print('RAGFlow URL:', getattr(settings, 'RAGFLOW_BASE_URL', 'NOT_SET'))
print('API Key:', getattr(settings, 'RAGFLOW_API_KEY', 'NOT_SET')[:10] + '...')
"
```

#### 2. 数据库迁移失败
**症状**: 运行migrate命令时出错

**解决方案**:
```bash
# 检查迁移状态
python manage.py showmigrations ai

# 重置迁移（谨慎使用）
python manage.py migrate ai zero
python manage.py makemigrations ai
python manage.py migrate ai

# 手动创建表（最后手段）
python manage.py dbshell
# 然后手动执行SQL创建语句
```

#### 3. 前端API调用失败
**症状**: 前端无法调用AI接口

**解决方案**:
```javascript
// 检查API路径
console.log('API Base URL:', process.env.VUE_APP_API_BASE_URL)

// 检查认证状态
console.log('User Token:', localStorage.getItem('token'))

// 测试API连接
fetch('/api/ai/status/')
  .then(response => response.json())
  .then(data => console.log('AI Status:', data))
  .catch(error => console.error('API Error:', error))
```

### 调试模式

启用详细日志：

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'ai_debug.log',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'teamvision.ai': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

## 📞 技术支持

### 健康检查命令
```bash
# 完整健康检查
python manage.py shell -c "
from teamvision.ai.health_check import perform_ai_health_check
import json
print(json.dumps(perform_ai_health_check(), indent=2, ensure_ascii=False))
"

# 快速状态检查
python quick_ai_test.py
```

### 性能监控
```bash
# 查看AI使用统计
python manage.py shell -c "
from teamvision.ai.models import AIUsageStatistics
from django.db.models import Sum
stats = AIUsageStatistics.objects.aggregate(
    total_generations=Sum('generation_count'),
    total_cases=Sum('generated_cases_count'),
    total_accepted=Sum('accepted_cases_count')
)
print('总生成次数:', stats['total_generations'])
print('总生成用例:', stats['total_cases'])
print('总采纳用例:', stats['total_accepted'])
"
```

### 数据备份
```bash
# 导出AI数据
python manage.py dumpdata teamvision.ai > ai_data_backup.json

# 恢复AI数据
python manage.py loaddata ai_data_backup.json
```

## 🎉 开始使用

完成部署后，您可以：

1. **启动Django服务器**:
   ```bash
   python manage.py runserver
   ```

2. **访问前端页面**: 进入项目测试用例管理页面

3. **点击AI助手按钮**: 开始体验AI测试用例生成功能

4. **探索更多功能**: 尝试AI对话、用例优化、覆盖度分析等功能

祝您使用愉快！如有问题，请参考故障排除部分或联系技术支持。
