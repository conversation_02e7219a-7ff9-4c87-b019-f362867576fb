# coding=utf-8
'''
Created on 2015-11-17

@author: zhang<PERSON>de
'''

from django.contrib.auth.models import User, Group
from django.contrib.auth import logout as auth_logout
from django.contrib.auth import authenticate, logout, login as auth_login
from teamvision.auth_extend.user.models import UserExtend
from teamvision.project.models import ProductSpaceUser, ProductSpace
from teamvision.resources.user_service.resource_string import UserService as RUserService
from django.contrib.auth import update_session_auth_hash
from gatesidelib.common.simplelogger import SimpleLogger
import random
from django.core.cache import cache


class UserService(object):
    '''
        classdocs
    '''

    @staticmethod
    def all_users():
        return User.objects.filter(is_active=1).order_by('username')

    @staticmethod
    def get_user(user_id):
        user = None
        try:
            user = User.objects.get(id=user_id)
        except Exception as ex:
            SimpleLogger.warning("user_id=" + str(user_id) + ":" + str(ex))
        return user

    @staticmethod
    def get_name_by_id(user_id):
        name = "无"
        if user_id is None:
            return name
        if user_id == 0:
            return name

        name_dict_key = "user_name_dict"
        username_dict_cache = cache.get(name_dict_key)
        if username_dict_cache is None:
            project_task_status_desc_list = User.objects.all().values_list('id', 'first_name', 'last_name')
            name_dict = {}
            for _id, _first_name, _last_name in project_task_status_desc_list:
                name_dict[_id] = _last_name + _first_name
            cache.set(name_dict_key, name_dict, timeout=120)

        try:
            task_name_dict = cache.get(name_dict_key)
            name = task_name_dict[user_id]
            return name
        except Exception as e:
            SimpleLogger.warning(e)
            return name

    @staticmethod
    def get_user_list_by_ids(id_list):
        user_list = User.objects.filter(id__in=id_list)
        return user_list

    @staticmethod
    def is_admin(user_id):
        result = False
        user = UserService.get_user(user_id)
        if user is not None:
            for user_group in user.groups.get_queryset():
                group = Group.objects.get(id=user_group.id)
                if group.name == "Admin":
                    result = True
        return result

    @staticmethod
    def get_space_users(user_id):
        result = list()
        self_user = UserService.get_user(user_id)
        system_permission = UserService.get_system_permission(user_id)
        if system_permission < 2:
            result = UserService.all_users()
        elif self_user is not None:
            space_users = ProductSpaceUser.objects.filter(ProductSpace=self_user.extend_info.default_space)
            space_user_ids = [user.User for user in space_users]
            result = UserService.all_users().filter(id__in=space_user_ids)
        return result

    @staticmethod
    def get_system_permission(user_id):
        result = 99
        user = UserService.get_user(user_id)
        if user:
            for user_group in user.groups.get_queryset():
                group = Group.objects.get(id=user_group.id)
                tmp = group.extend_info.group_priority
                if result > tmp:
                    result = tmp
        return result

    @staticmethod
    def is_space_admin(user_id):
        result = False
        user = UserService.get_user(user_id)
        system_permission = UserService.get_system_permission(user_id)
        if system_permission < 2:
            result = True
        elif user is not None:
            if not user.is_anonymous and user.extend_info is not None:
                space = ProductSpace.objects.get(user.extend_info.default_space)
                if space is not None:
                    if space.SpaceAdmin == user.id:
                        result = True
        return result

    @staticmethod
    def login(request):
        result = {'login': False, 'message': '', 'sessionid': ''}
        useremail = request.data['email']
        password = request.data['password']
        user = None
        try:
            user = User.objects.get(email=useremail)
        except Exception as ex:
            result['message'] = RUserService.user_not_exists
            SimpleLogger.exception(ex)
        if user is not None:
            authed_user = authenticate(username=user.username, password=password)
            if authed_user is not None:
                if authed_user.is_active:
                    auth_login(request, authed_user)
                    UserService.add_user_extendinfo(authed_user, user.extend_info.default_space)
                    result['login'] = True
                    result['sessionid'] = authed_user.get_session_auth_hash()
                else:
                    result['message'] = RUserService.user_not_active
            else:
                result['message'] = RUserService.user_password_incorrect
        return result

    @staticmethod
    def logout(request):
        logout(request)

    @staticmethod
    def change_password(request):
        message = {"code": 1, "message": "修改密码成功"}
        old_password = request.data.get('oldPassword')
        new_password = request.data.get('password')
        if request.user.check_password(old_password):
            request.user.set_password(new_password)
            request.user.save()
            update_session_auth_hash(request, request.user)
        else:
            message["code"] = 0
            message["message"] = RUserService.user_old_password_incorrect
        return message

    @staticmethod
    def add_user_extendinfo(user, default_space):
        try:
            extend_info = user.extend_info
        except Exception as ex:
            user_extend = UserExtend()
            user_extend.avatar = "/static/global/images/fruit-avatar/Fruit-" + str(random.randint(1, 20)) + ".png"
            user_extend.user_id = user.id
            user_extend.default_space = default_space
            user_extend.save()

    @staticmethod
    def create_user(request):
        result = UserService.reactive_user(request)
        space = request.data.get("space")
        if not result:
            email = request.data.get("email")
            if not UserService.user_exists(email):
                new_user = User()
                result = UserService.init_user(request, new_user)
                UserService.init_space_user(result.id, space)
        else:
            if not UserService.space_user_exists(result.email):
                UserService.init_space_user(result.id, space)
        return result

    @staticmethod
    def reactive_user(request):
        email = request.data.get("email")
        deleted_users = User.objects.all().filter(email=email).filter(is_active=0)
        reactive_user = None
        if len(deleted_users) > 0:
            reactive_user = deleted_users[0]
            reactive_user = UserService.init_user(request, reactive_user)
        return reactive_user

    @staticmethod
    def init_user(request, new_user):
        new_user.email = request.data.get("email")
        default_space = request.data.get("space")
        new_user.first_name = request.data.get("first_name")
        new_user.last_name = request.data.get("last_name")
        new_user.is_active = 1
        new_user.is_staff = 1
        new_user.username = new_user.email[0:new_user.email.find("@")]
        new_user.set_password(request.data.get("password"))
        new_user.save()
        new_user.groups.add(Group.objects.get(id=29))
        UserService.add_user_extendinfo(new_user, default_space)
        return new_user

    @staticmethod
    def init_space_user(user_id, space):
        space_user = ProductSpaceUser()
        space_user.ProductSpace = space
        space_user.User = user_id
        space_user.save()
        return space_user

    @staticmethod
    def delete_space_user(user):
        space_users = ProductSpaceUser.objects.get_space_users(user.extend_info.default_space).filter(User=user.id)
        space_users.delete()

    @staticmethod
    def delete_user(user_id):
        user = User.objects.get(id=int(user_id))
        UserService.delete_space_user(user)
        if not UserService.space_user_exists(user.email):
            user.is_active = 0
            user.groups.clear()
        user.save()

    @staticmethod
    def check_email_exists(request):
        result = False
        value = request.POST.get("value", "")
        user = User.objects.get(email=value)
        if user and user.is_active:
            result = True
        return result

    @staticmethod
    def user_exists(email):
        result = False
        user = None
        try:
            user = User.objects.get(email=email)
        except Exception as ex:
            SimpleLogger.exception(ex)
        if user and user.is_active:
            result = True
        return result

    @staticmethod
    def space_user_exists(email):
        result = False
        user = User.objects.get(email=email)
        space_users = ProductSpaceUser.objects.get_space_users(user.extend_info.default_space).filter(User=user.id)
        if len(space_users) > 0:
            result = True
        return result

    @staticmethod
    def edit_user(request, userid):
        user = UserService.get_user(userid)
        user.email = request.data.get("email")
        user.first_name = request.data.get("first_name")
        user.last_name = request.data.get("last_name")
        user.save()
        return user

    @staticmethod
    def update_user_group(request, userid):
        user = UserService.get_user(userid)
        user_group_id = request.data.get("userGroup")
        user.groups.clear()
        user.groups.add(Group.objects.get(id=int(user_group_id)))
        user.save()
        return user

    @staticmethod
    def reset_user_password(request, userid):
        user = UserService.get_user(userid)
        user.set_password(request.data.get("password"))
        user.save()
        return None

    @staticmethod
    def get_default_space_id(user_id):
        try:
            user_extend = UserExtend.objects.get(user_id=user_id)
            # print(user_extend)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return user_extend.default_space
