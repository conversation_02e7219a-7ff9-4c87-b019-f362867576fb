# coding=utf-8
'''
Created on 2015-10-23

@author: <PERSON><PERSON><PERSON><PERSON>
'''
from business.business_service import BusinessService
from business.common.system_config_service import SystemConfigService
from business.common.git_service import GitService
from business.common.git_api_path import projects_get_api
from business.ci.ci_task_service import CITaskService
from business.ci.ci_taskflow_service import CITask<PERSON>lowService
from teamvision.ci.models import ProjectGitRepo, BranchAction, BranchActionTaskMap
from django.contrib.admin.models import DELETION, CHANGE, ADDITION

from json.decoder import JSONDecoder


class CIBranchService(BusinessService):
    '''
    classdocs
    '''

    @staticmethod
    def get_my_git_projects():
        result = list()
        git_server = SystemConfigService.get_git_config()["GitLabHost"]
        private_token = SystemConfigService.get_git_config()["Token"]
        projects_get_path = git_server + projects_get_api + "?private_token=" + private_token + "&membership=true"
        project_list = GitService.git_get_method(projects_get_path)
        for project in project_list:
            temp = dict()
            temp["id"] = project["id"]
            temp["name"] = project["name"]
            temp["ssh_url_to_repo"] = project["ssh_url_to_repo"]
            temp["http_url_to_repo"] = project["http_url_to_repo"]
            temp["web_url"] = project["web_url"]
            temp["links"] = project["_links"]
            temp["owner"] = project["owner"]
            temp["permissions"] = project["permissions"]
            result.append(temp)
        return result

    @staticmethod
    def git_project_branches(project_git_repo_id):
        result = dict()
        git_repo = ProjectGitRepo.objects.get(project_git_repo_id)
        if git_repo is not None:
            private_token = SystemConfigService.get_git_config()["Token"]
            branch_get_path = git_repo.GitBranchLink + "?private_token=" + private_token
            tag_get_path = git_repo.GitTagsLink + "?private_token=" + private_token
            result["branch_list"] = GitService.git_get_method(branch_get_path)[0:10]
            result["tag_list"] = GitService.git_get_method(tag_get_path)[0:10]
        return result

    @staticmethod
    def create_repo_link(form_data, user):
        result = {"result": 1, "message": "添加新Git项目成功", "link_object": None}
        member_link = form_data.get("GitProjectInfo").get("links").get("members")
        git_project_name = form_data.get("GitProjectInfo").get("name")
        private_token = SystemConfigService.get_git_config()["Token"]
        git_project_members = GitService.git_get_method(member_link + "?private_token=" + private_token)
        git_project_member_names = [member["username"] for member in git_project_members if member["state"] == "active"]
        git_user_name = form_data.get("GitUserName")
        project_id = int(form_data.get("ProjectID"))
        git_project_id = form_data.get("GitProjectID")
        project_git_repos = ProjectGitRepo.objects.all().filter(ProjectID=project_id).filter(
            GitProjectID=git_project_id)
        if len(project_git_repos) == 0:
            if git_user_name in git_project_member_names:
                project_repo = ProjectGitRepo()
                CIBranchService.init_project_repo(form_data, project_repo)
                project_repo.save()
                result["link_object"] = project_repo
                CITaskFlowService.create_taskflow(git_project_name, project_id, project_repo.id, user)
                CIBranchService.log_create_activity(user, project_repo)
            else:
                result = {"result": 0, "message": "你没有项目[" + git_project_name + "]的权限，添加失败",
                          "link_object": None}
        else:
            result = {"result": 0, "message": "你要添加的项目已经存在,请勿重复添加。", "link_object": None}
        return result

    @staticmethod
    def build_action(action_history, user):
        action = BranchAction.objects.get(action_history.Action)
        if action is not None:
            action_tasks = BranchActionTaskMap.objects.all().filter(ActionID=action.id)
            for task in action_tasks:
                CITaskService.start_ci_task(user, task.CITaskID, task.CITaskParameter, action_history.Version,
                                            action_history.id)
            CIBranchService.log_buildaction_activity(user, action)

    @staticmethod
    def init_project_repo(form_data, project_repo):
        temp_project_repo = project_repo
        temp_project_repo.ProjectID = form_data.get("ProjectID")
        temp_project_repo.GitBranchLink = form_data.get("GitProjectInfo").get("links").get("repo_branches")
        temp_project_repo.GitMembersLink = form_data.get("GitProjectInfo").get("links").get("members")
        temp_project_repo.GitProjectID = form_data.get("GitProjectID")
        temp_project_repo.GitProjectName = form_data.get("GitProjectInfo").get("name")
        temp_project_repo.GitTagsLink = temp_project_repo.GitBranchLink.replace("branches", "tags")
        temp_project_repo.GitWebLink = form_data.get("GitProjectInfo").get("web_url")
        temp_project_repo.HttpGitUrl = form_data.get("GitProjectInfo").get("http_url_to_repo")
        temp_project_repo.SSHGitUrl = form_data.get("GitProjectInfo").get("ssh_url_to_repo")
        return temp_project_repo

    @staticmethod
    def log_create_activity(user, link):
        ProjectGitRepo.objects.log_action(user.id, link.id, link.GitProjectName, ADDITION, "添加了新代码库",
                                          link.ProjectID, CIBranchService.ActionLogType.CI)

    @staticmethod
    def log_buildaction_activity(user, action):
        BranchAction.objects.log_action(user.id, action.id, action.Title, ADDITION, "执行了Action ", action.ProjectID,
                                        CIBranchService.ActionLogType.CI)

    @staticmethod
    def log_delete_activity(user, link):
        ProjectGitRepo.objects.log_action(user.id, link.id, link.GitProjectName, DELETION, "删除了代码库",
                                          link.ProjectID, CIBranchService.ActionLogType.CI)

    @staticmethod
    def log_change_activity(user, link):
        ProjectGitRepo.objects.log_action(user.id, link.id, link.GitProjectName, CHANGE, "更新了了代码库信息",
                                          link.ProjectID, CIBranchService.ActionLogType.CI)
