# coding=utf-8
'''
Created on 2015-10-23

@author: <PERSON><PERSON><PERSON><PERSON>
'''
from business.business_service import BusinessService
from teamvision.ci.models import CIDeployService
from gatesidelib.common.simplelogger import SimpleLogger
from django.contrib.admin.models import DELETION, CHANGE, ADDI<PERSON>ON
from teamvision.project.models import ProductSpace, Project, Tag
from business.project.project_service import ProjectService
from business.common.mongodb_service import MongoDBService
from teamvision.home.models import FileInfo
from bson import ObjectId
import requests, ssl
from requests.packages.urllib3.exceptions import InsecureRequestWarning


class CIBranchService(BusinessService):
    '''
    classdocs
    '''

    @staticmethod
    def get_git_projects():
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
        context = ssl._create_unverified_context()
        response = requests.get("https://gitlab.com/api/v4/projects?private_token=********************")
        # print(response.content)
