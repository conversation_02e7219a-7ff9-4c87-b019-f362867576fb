# coding=utf-8
'''
Created on 2015-10-23

@author: z<PERSON><PERSON><PERSON>
'''
from business.business_service import BusinessService
from gatesidelib.common.simplelogger import SimpleLogger
from business.common.system_config_service import SystemConfigService
from business.ci.ci_task_service import CITaskService
from teamvision.ci.models import CIRunStatisticsDaily, CIPassRateStatisticsDaily, AutoTestingTaskResult
from django.db.models import Sum
from gatesidelib.datetimehelper import DateTimeHelper

from bson import ObjectId


class CIStatisticsService(BusinessService):
    '''
    classdocs
    '''

    @staticmethod
    def run_trend_last30days(project_id, date_range):
        if str(project_id) != "0":
            result = CIRunStatisticsDaily.objects.all().filter(ProjectID=int(project_id))
        else:
            result = CIRunStatisticsDaily.objects.all()

        if date_range is not None:
            statistics_range = date_range.split(',')
            result = result.filter(StatisticsDate__range=statistics_range)

        result = result.values('StatisticsDate').annotate(TotalToday=Sum('Total'),
                                                          SuccessToday=Sum('Success'),
                                                          FailToday=Sum('Fail'))
        end_index = len(result)
        start_index = end_index - 15
        if start_index <= 0:
            start_index = 0
        return result.order_by('StatisticsDate')[start_index:end_index]

    @staticmethod
    def step_passrate_trend(step_id):
        result = list()
        if step_id is not None and str(step_id) != "0":
            result = CIPassRateStatisticsDaily.objects.all().filter(StepID=step_id).order_by('StatisticsDate')
        end_index = len(result)
        start_index = end_index - 15
        if start_index <= 0:
            start_index = 0
        return result[start_index:end_index]

    @staticmethod
    def task_lowrate_trend(request):
        result = list()
        my_ci_tasks = CITaskService.get_product_ci_tasks(request, 0, "all")
        ci_tasks_ids = [task.id for task in my_ci_tasks]
        three_day_ago = DateTimeHelper.add_day(DateTimeHelper.get_local_now()[0:10], -1000)
        low_rate_history = AutoTestingTaskResult.objects.all().filter(Total__gt=0).filter(CreateTime__gte=three_day_ago)
        history_ids = [history.id for history in low_rate_history]

    @staticmethod
    def updateStatisticsData(form_data):
        result = None
        project_id = form_data.get("ProjectID", 0)
        statistics_date = form_data.get("StatisticsDate", None)
        if project_id != 0 and statistics_date is not None:
            statistics_datas = CIRunStatisticsDaily.objects.filter(ProjectID=int(project_id)).filter(
                StatisticsDate=statistics_date).order_by('-id')
            if len(statistics_datas) > 0:
                result = statistics_datas[0]
                result.Total = result.Total + 1
                result.Success = result.Success + int(form_data.get("Success", 0))
                result.Fail = result.Fail + int(form_data.get("Fail", 0))
            else:
                result = CIRunStatisticsDaily()
                result.ProjectID = int(project_id)
                result.StatisticsDate = statistics_date
                result.Total = result.Total + 1
                result.Success = result.Success + int(form_data.get("Success", 0))
                result.Fail = result.Fail + int(form_data.get("Fail", 0))
            result.save()
        return result
