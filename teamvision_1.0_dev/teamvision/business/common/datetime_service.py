# coding=utf-8
"""
Created on 2016-12-5
@author: zhangpeng
"""

from datetime import timedelta
from django.utils import timezone


class DateTimeService(object):

    @staticmethod
    def get_starttime_endtime(days):
        now = timezone.now()
        today = timezone.now().date()
        start_date = today - timedelta(days=int(days) - 1)
        start_time = str(start_date) + ' 00:00:00.000000'
        return start_time, now

    @staticmethod
    def get_startdate_enddate(days):
        today = timezone.now().date()
        start_date = today - timedelta(days=int(days))
        return start_date, today