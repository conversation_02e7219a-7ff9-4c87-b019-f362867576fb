# coding=utf-8
"""
Created on 2014-11-2
@author: <PERSON><PERSON><PERSON>
"""
from gatesidelib.common.simplelogger import SimpleLogger
from gatesidelib.emailhelper import EmailHelper
from business.business_service import BusinessService
from business.common.system_config_service import SystemConfigService
import threading


class EmailService(BusinessService):
    '''
    Email Service for doraemon
    '''

    @staticmethod
    def send_email(emailconfig, emaillist, emailmessage, subject):
        email_config = SystemConfigService.get_email_config()
        emailSender = EmailHelper(email_config['Host'], email_config['User'], email_config['Password'],
                                  email_config['Postfix'], email_config['Port'])
        message = emailSender.generatetextmessage(emailmessage, subject, ','.join(emaillist), 'html')
        if email_config['Password'].strip() != "":
            worker = threading.Thread(target=emailSender.sendemaillogin, args=(emaillist, subject, message.as_string()))
            worker.start()
        else:
            worker = threading.Thread(target=emailSender.sendmail_nologin,
                                      args=(emaillist, subject, message.as_string()))
            worker.start()
        SimpleLogger.info("Send Email:" + str(subject) + ":" + str(emaillist))

    @staticmethod
    def send_email_multi(subject, message, email_list, cc_email_list=None, image_list=None, file_list=None):
        email_config = SystemConfigService.get_email_config()
        email_sender = EmailHelper(email_config['Host'], email_config['User'], email_config['Password'],
                                   email_config['Postfix'], email_config['Port'])
        # debug
        # email_sender.send_email_multipart(subject, message, email_list, cc_email_list, image_list, file_list)
        worker = threading.Thread(target=email_sender.send_email_multipart,
                                  args=(subject, message, email_list, cc_email_list, image_list, file_list))
        worker.start()
        SimpleLogger.info("Send Email:" + str(subject) + ":" + str(email_list))
