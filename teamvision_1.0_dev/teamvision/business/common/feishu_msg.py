import requests

from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.home.models import DicD<PERSON>, DicType


class FeiShuMsg:
    def __init__(self, message_base_url, auth_base_url, app_id, app_secret, chat_id):
        """
        :param message_base_url
        :param auth_base_url
        :param app_id
        :param app_secret
        :param chat_id
        """
        self.message_base_url = message_base_url
        self.auth_base_url = auth_base_url
        self.app_id = app_id
        self.app_secret = app_secret
        self.chat_id = chat_id
        self.auth = self.get_authorization()

    def get_tenant_access_token(self):
        data = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        url = self.auth_base_url + "/tenant_access_token/internal/"
        rsp = requests.post(url=url, json=data, verify=False)
        return rsp.json()["tenant_access_token"], rsp.json()["expire"]

    def get_authorization(self):
        tenant_access_token, expire = self.get_tenant_access_token()
        return "Bearer {}".format(tenant_access_token)

    def send(self, msg, at_users=[], msg_type="text"):
        """
        :param msg:
        :param at_users:
        :param msg_type:
        :return: response
        """
        headers = {
            "Authorization": self.auth,
            "Content-Type": "application/json"
        }
        url = self.message_base_url + "/send/"

        if at_users:
            at_str = ""
            for user in at_users:
                at_str += "<at user_id=\"{}\"></at>".format(user)
            msg = "{} {}".format(at_str, msg)
        data = {
            "msg_type": msg_type,
            "content": {"text": msg},
            "chat_id": self.chat_id
        }

        resp = requests.post(url=url, json=data, headers=headers, verify=False)
        SimpleLogger.info('FeiShu request:' + str(url) + '; request_body:' + str(data) + '; resp:' + str(resp))
        return resp


if __name__ == '__main__':
    feishu_msg = FeiShuMsg()
    feishu_msg.send("test", "oc_2b7234c08f4e21ee85b9296a0ba0e6e1")
