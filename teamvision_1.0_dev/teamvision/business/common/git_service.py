#coding=utf-8
'''
Created on 2014-10-8

@author: tiande.zhang
'''
import requests,os
from teamvision.settings import BASE_DIR
from requests.packages.urllib3.exceptions import InsecureRequestWarning
import requests,ssl,json

class GitService():
    
    @staticmethod
    def git_get_method(api):
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
        context = ssl._create_unverified_context()
        response = requests.get(api)
        return json.loads(str(response.content, "utf-8"), encoding="utf-8")