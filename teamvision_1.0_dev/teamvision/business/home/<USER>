# coding=utf-8
'''
Created on 2015-10-23

@author: <PERSON><PERSON><PERSON><PERSON>
'''
import datetime
from datetime import date, timedelta

from django.db.models import Count, F
from business.project.fortesting_service import ForTestingService
from teamvision.project.models import Version, TestApplication
from teamvision.crontab.models import CrontabTestCaseRepeatRate
from teamvision.ci.models import AutoCase

from teamvision.project import models


class HomeStatisticsService(object):
    '''
    classdocs
    '''

    @staticmethod
    def fortesting_count_bystatus(request):
        # project_all_fortestings = ForTestingService.get_my_fortestings(request).exclude(Status__in=[4,5])
        project_all_fortestings = TestApplication.objects.all().exclude(Status__in=[5])
        result = project_all_fortestings.values('Status').annotate(TotalCount=Count('id')).order_by('Status')
        HomeStatisticsService.project_version_counts()
        return result

    @staticmethod
    def project_version_counts():
        all_versions = Version.objects.all()
        result = all_versions.values('VProjectID').annotate(TotalCount=Count('id')).order_by('-TotalCount')
        return result[0:10]

    @staticmethod
    def issue_count_byproject(request):
        project_all_fortestings = ForTestingService.get_my_fortestings(request).exclude(Status__in=[4, 5])
        result = project_all_fortestings.values('Status').annotate(TotalCount=Count('id')).order_by('Status')
        HomeStatisticsService.project_version_counts()
        return result

    @staticmethod
    def testpoint_count_byproject(request):
        result, project_sum_dt = list(), dict()
        productspaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = models.Project.objects.filter(ProductSpace=productspaces).filter(IsActive=1).values_list('id',
                                                                                                               flat=True)
        test_case_all = models.ProjectTestCase.objects.filter(IsActive=1).filter(IsGroup=0).values('Project')
        groupByProjectInfo = test_case_all.filter(Status=0).annotate(Count('id'))
        groupByProjectInfo_archive = test_case_all.filter(Status=1).annotate(Count('id'))

        project_archive_sum_dt = dict()
        for _temp in groupByProjectInfo:
            project_sum_dt[_temp.get('Project')] = _temp.get('id__count')

        for _temp in groupByProjectInfo_archive:
            project_archive_sum_dt[_temp.get('Project')] = _temp.get('id__count')

        for project_id in project_ids:
            # count = models.ProjectTestCase.objects.filter(Project=project_id).filter(IsActive=1).filter(IsGroup=0).count()
            temp = dict()
            temp["Project"] = project_id
            temp["TotalCount"] = project_sum_dt.get(project_id, 0)
            temp["ArchiveCount"] = project_archive_sum_dt.get(project_id, 0)
            result.append(temp)

        def sort_by_totalcount(elem):
            return elem["TotalCount"]

        result.sort(reverse=True, key=sort_by_totalcount)
        return result

    @staticmethod
    def auto_case_counts(project_ids):
        all_autocases = AutoCase.objects.all().filter(IsActive=1).filter(ProjectID__in=project_ids)
        result = all_autocases.values('ProjectID').annotate(TotalCount=Count('id')).order_by('-TotalCount')
        return result[0:10]

    @staticmethod
    def testpoint_testplan_count_byproject_id(request):
        productspaces = request.META.get("HTTP_PRODUCT_SPACE")
        # productspaces = 1
        project_ids = models.Project.objects.filter(ProductSpace=productspaces).filter(IsActive=1)
        return project_ids

    @staticmethod
    def get_start_day(type):
        day = 7
        if str(type) == '3':
            day = 15
        elif str(type) == '4':
            day = 30
        now_time = datetime.datetime.now()
        time_period = now_time.strftime('%Y%m%d')
        this_week_start = now_time - datetime.timedelta(days=now_time.weekday())
        this_month_start = datetime.datetime(now_time.year, now_time.month, 1)
        if str(type) == '2' and now_time.strftime('%Y%m%d') > this_week_start.strftime('%Y%m%d'):
            time_period = this_week_start.strftime('%Y%m%d')
        elif str(type) == '5' and now_time.strftime('%Y%m%d') > this_month_start.strftime('%Y%m%d'):
            temp_day = int(now_time.strftime('%Y%m%d')) - int(this_month_start.strftime('%Y%m%d'))
            time_period = now_time + datetime.timedelta(days=-1 * (temp_day))
            time_period = time_period.strftime('%Y%m%d')
        elif str(type) in ('1', '3', '4'):
            time_period = now_time + datetime.timedelta(days=-1 * (day))
            time_period = time_period.strftime('%Y%m%d')
        # print("time_period=%s" % (time_period))
        return time_period

    @staticmethod
    def testpoint_testplan_count_byproject(request, type, project_ids):
        result, time_period = list(), None
        time_period = HomeStatisticsService.get_start_day(type)
        data = models.ProjectTestPlan.objects.all()
        for project_id in project_ids:
            project_time_status_dt, require_num = dict(), dict()
            for _info in data:
                CreationTime = _info.__dict__.get('CreationTime').strftime('%Y%m%d')
                Status = _info.__dict__.get('Status')
                requireNum = _info.__dict__.get('requireNum')

                key = str(project_id) + '_' + str(CreationTime) + '_' + str(Status)
                if CreationTime >= time_period:
                    if project_id == _info.__dict__.get('Project'):
                        project_time_status_dt[key] = project_time_status_dt.get(key, 0) + 1
                        # 需求数
                        require_num[key] = require_num.get(key, 0) + requireNum

            temp = dict()
            temp["Project"] = project_id
            temp["TotalCount"] = project_time_status_dt
            temp['require_num'] = require_num
            if temp["TotalCount"]:
                result.append(temp)
        return result

    @staticmethod
    def testpoint_testcase_count_byproject(request, type, project_ids):
        result, time_period = list(), None
        time_period = HomeStatisticsService.get_start_day(type)
        time_period = datetime.datetime.strptime(time_period, '%Y%m%d')
        data = models.ProjectTestCase.objects.filter(CreationTime__gte=time_period).filter(IsActive=1).filter(IsGroup=0)
        for project_id in project_ids:
            project_time_status_dt = dict()
            for _info in data:
                CreationTime = _info.__dict__.get('CreationTime').strftime('%Y%m%d')
                key = str(project_id) + '_' + str(CreationTime)
                if project_id == _info.__dict__.get('Project'):
                    project_time_status_dt[key] = project_time_status_dt.get(key, 0) + 1
            temp = dict()
            temp["Project"] = project_id
            temp["TotalCount"] = project_time_status_dt
            if temp["TotalCount"]:
                result.append(temp)
        return result

    @staticmethod
    def testpoint_autocase_count_byproject(request, type, project_ids):
        result, time_period = list(), None
        time_period = HomeStatisticsService.get_start_day(type)
        time_period = datetime.datetime.strptime(time_period, '%Y%m%d')
        data = AutoCase.objects.filter(CreateTime__gte=time_period).filter(IsActive=1)
        for project_id in project_ids:
            project_time_status_dt = dict()
            for _info in data:
                CreationTime = _info.__dict__.get('CreateTime').strftime('%Y%m%d')
                key = str(project_id) + '_' + str(CreationTime)
                if project_id == _info.__dict__.get('ProjectID'):
                    project_time_status_dt[key] = project_time_status_dt.get(key, 0) + 1
            temp = dict()
            temp["Project"] = project_id
            temp["TotalCount"] = project_time_status_dt
            if temp["TotalCount"]:
                result.append(temp)
        return result

    @staticmethod
    def project_testcase_repeatrate(request):
        productspaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = models.Project.objects.filter(ProductSpace=productspaces).filter(IsActive=1).values_list('id',
                                                                                                               flat=True)
        start_time = (date.today() + timedelta(days=-90)).strftime("%Y-%m-%d")
        end_time = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
        result = CrontabTestCaseRepeatRate.objects.filter(Project__in=project_ids).filter(
            CreateDate__range=(start_time, end_time)).values_list('Project', 'CreateDate', 'RepeatRate')
        return result
