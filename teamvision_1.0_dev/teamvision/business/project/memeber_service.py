# coding=utf-8
"""
Created on 2015-11-17

@author: Dev<PERSON>
"""

from business.auth_user.user_service import UserService
from teamvision.project.models import ProjectMember, Project
from business.project.project_service import ProjectService
from django.contrib.admin.models import DELETION, CHANGE, ADDI<PERSON>ON
from django.contrib.auth.models import User


class MemberService(object):
    """
    classdocs
    """

    @staticmethod
    def get_my_project_members(request):
        my_project_list = ProjectService.get_projects_include_me(request)
        my_project_ids = [project.id for project in my_project_list]
        my_project_members = ProjectMember.objects.all().filter(PMProjectID__in=my_project_ids)
        my_project_members_ids = list()
        distinct_members = list()
        for member in my_project_members:
            if member.PMMember not in my_project_members_ids:
                distinct_members.append(member)
                my_project_members_ids.append(member.PMMember)
        return distinct_members

    @staticmethod
    def get_member_users(project_id):
        if isinstance(project_id, list):
            result = ProjectMember.objects.filter(PMProjectID__in=project_id)
        else:
            result = ProjectMember.objects.get_members(project_id)
        user_ids = [member.PMMember for member in result]
        return UserService.all_users().filter(id__in=user_ids).order_by("email")

    @staticmethod
    def add_member(member_id, projectid, operator):
        result = None
        member = ProjectMember.objects.get_member(int(projectid), int(member_id))
        if member is None:
            project_member = ProjectMember()
            project_member.PMProjectID = projectid
            project_member.PMMember = member_id
            project_member.PMRoleID = 1
            project_member.PMRoleType = 1
            project_member.save()
            result = project_member
            MemberService.log_create_activity(operator, project_member)
        return result

    @staticmethod
    def remove_member(request, projectid):
        user_id = request.POST.get("PMMember")
        project_member = ProjectMember.objects.get_member(projectid, user_id)
        project_member.IsActive = 0
        project_member.save()
        MemberService.log_delete_activity(request.user, project_member)

    @staticmethod
    def update_role(request, projectid, userid):
        member_id = ProjectMember.objects.get_member(projectid, userid).id
        project_member = ProjectMember.objects.get(member_id)
        project_member.PMRoleID = request.POST.get("PMRoleID", 1)
        project_member.save()

    @staticmethod
    def log_create_activity(user, target):
        member = User.objects.get(id=target.PMMember)
        ProjectMember.objects.log_action(user.id, target.id, member.username, ADDITION, "添加了成员", target.PMProjectID)

    @staticmethod
    def log_delete_activity(user, target):
        member = User.objects.get(id=target.PMMember)
        ProjectMember.objects.log_action(user.id, target.id, member.username, DELETION, "删除了成员", target.PMProjectID)

    @staticmethod
    def get_project_member_emails(project_id):
        email_list = []
        member_list = MemberService.get_member_users(project_id)
        for member in member_list:
            email_list.append(member.email)
        return email_list
