# coding=utf-8
"""
Created on 2015-10-23

@author: <PERSON><PERSON><PERSON><PERSON>
"""
from teamvision.project.models import Project, ProjectModule
from gatesidelib.common.simplelogger import SimpleLogger
from django.contrib.admin.models import DELE<PERSON>ON, CHANGE, ADDITION
from django.core.cache import cache


class ModuleService(object):
    '''
    classdocs
    '''

    @staticmethod
    def create_module(request, projectid):
        try:
            module = ProjectModule()
            module.ProjectID = projectid
            module.Name = request.POST.get('Name')
            module.IsActive = 1
            module.save()
            ModuleService.log_create_activity(request.user, module)
        except Exception as ex:
            SimpleLogger.error(ex)

    @staticmethod
    def delete_module(request, module_id):
        module = ProjectModule.objects.get(module_id)
        module.IsActive = 0
        module.save()
        ModuleService.log_delete_activity(request.user, module)

    @staticmethod
    def update_module(module_id, field_name, value, user):
        module = ProjectModule.objects.get(module_id)
        module.__dict__[field_name] = value
        module.IsActive = 1
        module.save()
        ModuleService.log_change_activity(user, module)

    @staticmethod
    def log_create_activity(user, target):
        ProjectModule.objects.log_action(user.id, target.id, target.Name, ADDITION, "创建了新模块", target.ProjectID)

    @staticmethod
    def log_delete_activity(user, target):
        ProjectModule.objects.log_action(user.id, target.id, target.Name, ADDITION, "删除了模块", target.ProjectID)

    @staticmethod
    def log_change_activity(user, target):
        ProjectModule.objects.log_action(user.id, target.id, target.Name, ADDITION, "修改了模块", target.ProjectID)

    @staticmethod
    def get_first_module(project_id):
        result = 0
        try:
            project_project_modules = ProjectModule.objects.all().filter(ProjectID=project_id)
            if project_project_modules:
                result = project_project_modules[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    @staticmethod
    def get_module_name_by_id(module_id):
        if module_id == 0:
            return "--"
        cache_key = "module_id_" + str(module_id)
        module_name = cache.get(cache_key)
        if module_name:
            return module_name
        else:
            module = ProjectModule.objects.get(module_id)
            if module:
                cache.set(cache_key, module.Name, 60)
                return module.Name
            return "--"
