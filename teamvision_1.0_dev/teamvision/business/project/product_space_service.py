# coding=utf-8
'''
Created on 2015-10-23

@author: z<PERSON><PERSON><PERSON>
'''
from teamvision.project.models import ProductSpace, ProductSpaceUser
from business.auth_user.user_service import UserService
from django.contrib.admin.models import DELETION, CHANGE, ADDITION


class ProductSpaceService(object):
    '''
    classdocs
    '''

    @staticmethod
    def get_space_list(user_id):
        system_permission = UserService.get_system_permission(user_id)
        if system_permission < 2:
            return ProductSpace.objects.all()
        else:
            space_users = ProductSpaceUser.objects.filter(User=user_id)
            return ProductSpace.objects.all().filter(id__in=[space_user.ProductSpace for space_user in space_users])

    @staticmethod
    def add_user_to_space(space_list, user_id):
        my_space_list = ProductSpaceUser.objects.filter(User=user_id)
        my_space_ids = [space.ProductSpace for space in my_space_list]
        for space_id in space_list:
            if space_id not in my_space_ids:
                '''
                add space user
                '''
                ProductSpaceService.add_space_user(user_id, space_id)
        for space_id in my_space_ids:
            if space_id not in space_list:
                '''
                remove space user
                '''
                ProductSpaceUser.objects.filter(ProductSpace=space_id).filter(User=user_id).delete()

    @staticmethod
    def add_space_user(user_id, space_id):
        space_user = ProductSpaceUser()
        space_user.ProductSpace = space_id
        space_user.User = user_id
        space_user.save()
        return space_user

    @staticmethod
    def my_default_space(user_id):
        user = UserService.get_user(user_id)
        product_space = None
        if user.extend_info.default_space is None:
            my_space_list = ProductSpaceService.get_space_list(user_id)
            if len(my_space_list) > 0:
                product_space = my_space_list[0].ProductSpace
        else:
            product_space = user.extend_info.default_space
        return product_space
