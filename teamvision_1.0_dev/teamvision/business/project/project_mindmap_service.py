#coding=utf-8
'''
Created on 2015-11-17

@author: Devuser
'''

from business.auth_user.user_service import UserService
from teamvision.project import models
from teamvision.ci.models import CaseTag
from django.contrib.auth.models import User
import uuid,copy
from  business.project.topic_tree_node import TopicTreeNode
from teamvision.project.mongo_models import FortestingMongoFile
from business.common.file_info_service import  FileInfoService
from json.decoder import JSONDecoder
import xmind,os
from xmind.core.markerref import MarkerRefElement
from teamvision.settings import  BASE_DIR
from gatesidelib.common.simplelogger import SimpleLogger


class MindmapService(object):
    '''
    classdocs
    '''

    @staticmethod
    def save_mind_file(file_id,mind_tree):
        result = dict()
        mind_map = models.ProjectXmindFile.objects.get(int(file_id))
        case_tags = CaseTag.objects.filter(ProjectID__in=[mind_map.ProjectID,0])
        name_idmaps = dict()
        testpoint_count = 0
        for tag in case_tags:
            name_idmaps[tag.TagName] = tag.id
        if mind_tree is not None:
            json_decoder = JSONDecoder()
            validate_data = json_decoder.decode(mind_tree)
            MindmapService.save_mind_topic(0,mind_map,validate_data['root'],name_idmaps,result)
            latest_topic_ids = list()
            for value in result.values():
                value_array = value.split(":")
                latest_topic_ids.append(int(value_array[0]))
            all_topics = models.ProjectXmindTopic.objects.filter(MindFileID=int(file_id))
            all_parent_ids = list()
            for topic in all_topics:
                if topic.id not in latest_topic_ids:
                    MindmapService.delete_child(topic)
                all_parent_ids.append(topic.Parent)

            for topic_id in latest_topic_ids:
                if topic_id not in all_parent_ids:
                    testpoint_count = testpoint_count + 1
            mind_map.TestPointCount = testpoint_count
            mind_map.save()
        return result


    @staticmethod
    def save_mind_topic(parent_id,mind_file,data,name_idmaps,result):
        id = data['data'].get("id",None)
        resource = data['data'].get("resource", None)
        topic = None
        if id is not None:
            if str(id).isdigit():
                '''更新节点信息'''
                topic = models.ProjectXmindTopic.objects.get(id)
                if topic is not None:
                    topic = MindmapService.init_topic(data['data'],topic)
                    topic.Parent = parent_id
                    topic.Project = mind_file.ProjectID
                    topic.save()
                    MindmapService.delete_tags(topic)
                    MindmapService.create_topic_tag(topic,resource,name_idmaps)
                    result[str(id)] = str(topic.id) + ":" + topic.OriginalID
                else:
                    topic = models.ProjectXmindTopic()
                    topic.OriginalID = data['data'].get("OriginalID",None)
                    topic.MindFileID = mind_file.id
                    topic = MindmapService.init_topic(data['data'], topic)
                    topic.Parent = parent_id
                    topic.Project = mind_file.ProjectID
                    topic.save()
                    MindmapService.create_topic_tag(topic, resource, name_idmaps)
                    result[str(id)] = str(topic.id) + ":" + topic.OriginalID
            if not str(id).isdigit():
                '''全新插入节点'''
                topic = models.ProjectXmindTopic()
                topic.OriginalID = id
                topic.MindFileID = mind_file.id
                topic = MindmapService.init_topic(data['data'],topic)
                topic.Parent = parent_id
                topic.Project = mind_file.ProjectID
                topic.save()
                MindmapService.create_topic_tag(topic, resource, name_idmaps)
                result[str(id)] = str(topic.id) + ":" + topic.OriginalID

            if len(data['children'])>0:
                topic.IsLeaf = False
                topic.save()
                for child in data['children']:
                    MindmapService.save_mind_topic(topic.id,mind_file,child,name_idmaps,result)
            else:
                topic.IsLeaf = True
                topic.save()


    @staticmethod
    def init_topic(data,topic):
        if topic is not None:
            topic.Priority = data.get("priority",None)
            topic.Text = data.get("text","分支主题")
            topic.Progress = data.get("progress",None)
            topic.Note = data.get("note", None)
            topic.Image = data.get("image", None)
            topic.BackgroundColor = data.get("background", None)
            topic.FontColor = data.get("color", None)
            topic.FontFamily = data.get("font-family", None)
            topic.FontSize = data.get("font-size",14)
            topic.Link = data.get("link", None)
        return topic


    @staticmethod
    def create_topic_tag(topic,tagname_list,tag_map):
        if tagname_list is not None:
            for tag in tagname_list:
                temp_tags = models.ProjectXmindTopicTagMap.objects.filter(TopicID=topic.id).filter(TagID=int(tag_map[tag]))
                if len(temp_tags) == 0:
                    temp_tag = models.ProjectXmindTopicTagMap()
                    temp_tag.OriginalID = topic.OriginalID
                    temp_tag.TopicID = topic.id
                    temp_tag.TagID = int(tag_map[tag])
                    temp_tag.save()


    @staticmethod
    def export_xmind(file_id,user):
        result = None
        mind_file = models.ProjectXmindFile.objects.get(int(file_id))
        if mind_file is not None:
            try:
                root_node = TopicTreeNode()
                root_topic = models.ProjectXmindTopic.objects.get(mind_file.RootTopic)
                MindmapService.get_topic_tree(0, root_topic, root_node)
                temp_file_path = BASE_DIR+'/static/temp_files/' + mind_file.FileName+'_' + str(mind_file.id) + "_" + str(user.id) + "_.xmind"
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                workbook = xmind.load(temp_file_path)
                sheet1 = workbook.getPrimarySheet()
                sheet1.setTitle("测试点一")  # 设置画布名称
                xmind_root_topic = sheet1.getRootTopic()
                MindmapService.gen_xmind_sheet(root_node,xmind_root_topic,sheet1)
                xmind.save(workbook)
                result = temp_file_path
            except Exception as ex:
                SimpleLogger.exception(ex)
        return result

    @staticmethod
    def import_xmind(project_id,version_id,file_id, user):
        result = list()
        temp_file_path = BASE_DIR + '/static/temp_files/' + str(file_id) + "_" + str(
            user.id) + "testpoint_.xmind"
        try:
            FileInfoService.write_file(file_id,FortestingMongoFile,temp_file_path)
            workbook = xmind.load(temp_file_path)
            workbook.getPrimarySheet().getRootTopic().getSubTopics()
            worksheets = workbook.getSheets()
            for sheet in worksheets:
                kity_mind_file = MindmapService.convert_xmind_sheet2_kitymind(sheet,project_id,version_id,user)
                tv_root = models.ProjectXmindTopic.objects.get(kity_mind_file.RootTopic)
                MindmapService.convert_xmind_topic2_kitymind(kity_mind_file,tv_root,sheet.getRootTopic(),project_id)
                all_leaf_points = models.ProjectXmindTopic.objects.filter(MindFileID=kity_mind_file.id).filter(IsLeaf=True)
                kity_mind_file.TestPointCount = len(all_leaf_points)
                kity_mind_file.save()
                result.append(kity_mind_file)
        except Exception as ex:
            SimpleLogger.exception(ex)
        finally:
            os.remove(temp_file_path)
        return result

    @staticmethod
    def convert_xmind_sheet2_kitymind(xmind_sheet,project_id,version_id,user):
        new_topic = MindmapService.create_new_topic()
        mindmap_file = models.ProjectXmindFile()
        mindmap_file.FileName = xmind_sheet.getTitle()
        mindmap_file.RootTopic = new_topic.id
        mindmap_file.Owner = user.id
        mindmap_file.ProjectID = int(project_id)
        mindmap_file.VersionID = int(version_id)
        mindmap_file.FileType = 1
        mindmap_file.Theme = "fresh-blue"
        mindmap_file.Template = "default"
        mindmap_file.save()
        new_topic.MindFileID = mindmap_file.id
        new_topic.Project = project_id
        new_topic.Parent = 0
        new_topic.save()
        return mindmap_file

    @staticmethod
    def convert_xmind_topic2_kitymind(mind_file,tv_topic,xm_topic,project_id):
        try:
            MindmapService.gen_tv_topic(tv_topic, xm_topic)
            tv_topic.save()
            if len(xm_topic.getSubTopics()) > 0:
                tv_topic.IsLeaf = False
                for topic in xm_topic.getSubTopics():
                    temp_tv_topic = models.ProjectXmindTopic()
                    temp_tv_topic.MindFileID = mind_file.id
                    temp_tv_topic.Project = project_id
                    temp_tv_topic.Parent = tv_topic.id
                    MindmapService.convert_xmind_topic2_kitymind(mind_file,temp_tv_topic,topic,project_id)
            else:
                tv_topic.IsLeaf = True
        except Exception as ex:
            SimpleLogger.exception(ex)
        tv_topic.save()


    @staticmethod
    def gen_tv_topic(tv_topic,xmind_topic):
        try:
            tv_topic.Text = str(xmind_topic.getTitle())
            tv_topic.OriginalID = str(xmind_topic.getID())[0:12]
            if xmind_topic.getHyperlink() is not None:
                tv_topic.Link = str(xmind_topic.getHyperlink())
            if xmind_topic.getComments() is not None:
                tv_topic.Note = str(xmind_topic.getComments())
            for marker in xmind_topic.getMarkers():
                if str(marker.getMarkerId()).startswith('priority'):
                    tv_topic.Priority = int(str(marker.getMarkerId())[9:10])
        except Exception as ex:
            SimpleLogger.exception(ex)
        return tv_topic

    @staticmethod
    def gen_xmind_sheet(tv_topic,xmind_parent_topic,sheet):
        MindmapService.gen_xmind_topic(tv_topic.node,xmind_parent_topic)
        if len(tv_topic.children) >0:
            for tv_topic in tv_topic.children:
                sub_topic = xmind_parent_topic.addSubTopic()
                MindmapService.gen_xmind_topic(tv_topic.node,sub_topic)
                MindmapService.gen_xmind_sheet(tv_topic,sub_topic,sheet)
        else:
            return

    @staticmethod
    def gen_xmind_topic(tv_topic,xmind_topic):
        try:
            xmind_topic.setTitle(tv_topic.Text)
            if tv_topic.Link is not None and ":" in tv_topic.Link:
                xmind_topic.setURLHyperlink(tv_topic.Link)
            if tv_topic.Image is not None and ":" in tv_topic.Image:
                xmind_topic.setURLHyperlink(tv_topic.Image)
            if tv_topic.Note is not None and tv_topic.Note != '':
                xmind_topic.setPlainNotes(tv_topic.Note)
            if tv_topic.Priority is not None and tv_topic.Priority:
                xmind_topic.addMarker("priority-" + str(tv_topic.Priority))
            topic_tag_maps = models.ProjectXmindTopicTagMap.objects.get_topic_tags(tv_topic.id)
            for topic_tag in topic_tag_maps:
                tag = CaseTag.objects.get(topic_tag.TagID)
                if tag is not None:
                    xmind_topic.addLabel(tag.TagName)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return xmind_topic





    @staticmethod
    def delete_child(parent_topic):
        old_topic_id = parent_topic.id
        if parent_topic is not None:
            MindmapService.delete_tags(parent_topic)
            children = models.ProjectXmindTopic.objects.get_children(old_topic_id,parent_topic.MindFileID).filter(IsActive=1)
            if len(children) > 0:
                for child_topic in children:
                    MindmapService.delete_child(child_topic)
                if str(parent_topic.Parent) != "0":
                    parent_topic.delete()
            else:
                if str(parent_topic.Parent) != "0":
                    parent_topic.delete()
        else:
            return

    @staticmethod
    def copy_mindmap_file(form_data,file_id, user):
        result = None
        old_file = models.ProjectXmindFile.objects.get(int(file_id))
        if old_file:
            temp_file = old_file
            temp_file.id = None
            temp_file.RootTopic = 0
            temp_file.Owner = user.id
            temp_file.ProjectID = form_data.get("ProjectID")
            temp_file.VersionID = form_data.get("VersionID")
            temp_file.FileType = form_data.get("FileType")
            temp_file.FileName = form_data.get("FileName", "新建测试点")
            temp_file.save()
            priority = form_data.get("Priority",[0])
            if 0 in priority:
                priority = [1,2,3,4,5,6,7,8,9,0,None]
            old_file = models.ProjectXmindFile.objects.get(int(file_id))
            old_root_node = models.ProjectXmindTopic.objects.get(old_file.RootTopic)
            MindmapService.copy_topic_tree(old_root_node,temp_file.id,priority)
        return temp_file


    @staticmethod
    def copy_topic_tree(root_topic,file_id,priority):
        root_node = TopicTreeNode()
        MindmapService.get_topic_tree(0,root_topic,root_node)
        mind_file = models.ProjectXmindFile.objects.get(int(file_id))
        MindmapService.save_topic_tree(0,root_node,mind_file,priority)




    @staticmethod
    def create_mindmap_file(form_data,user):
        new_topic = MindmapService.create_new_topic()
        mindmap_file = models.ProjectXmindFile()
        mindmap_file.FileName = form_data.get("FileName","新建测试点")
        mindmap_file.RootTopic = new_topic.id
        mindmap_file.Owner = user.id
        mindmap_file.ProjectID = form_data.get("ProjectID")
        mindmap_file.VersionID = form_data.get("VersionID")
        mindmap_file.FileType = form_data.get("FileType")
        mindmap_file.Theme = "fresh-blue"
        mindmap_file.Template = "default"
        mindmap_file.save()
        new_topic.MindFileID = mindmap_file.id
        new_topic.save()
        return mindmap_file


    @staticmethod
    def create_new_topic():
        new_topic = models.ProjectXmindTopic()
        new_topic.OriginalID = str(uuid.uuid1())[:12]
        new_topic.MindFileID = 0
        new_topic.Parent = 0
        new_topic.Text = "分支主题"
        new_topic.save()
        return new_topic

    @staticmethod
    def get_topic_tree(parent,node,tree_node):
        if node is not None:
            tree_node.node = node
            tree_node.parent = parent
            tree_node.priority = node.Priority
            child_nodes = models.ProjectXmindTopic.objects.get_children(node.id,node.MindFileID).filter(IsActive=1)
            if len(child_nodes) > 0:
                for child_node in child_nodes:
                    temp_tree_node = TopicTreeNode()
                    MindmapService.get_topic_tree(node.id,child_node,temp_tree_node)
                    tree_node.children.append(temp_tree_node)
            else:
                tree_node.leaf = True
                return tree_node
        else:
            return tree_node

    @staticmethod
    def save_topic_tree(parent,root_node,mind_file,priority):
        result = False
        if root_node.node is not None:
            new_node = copy.deepcopy(root_node.node)
            new_node.id = None
            new_node.Parent = parent
            new_node.MindFileID = mind_file.id
            if new_node.Project != mind_file.ProjectID:
                new_node.OriginalID = str(uuid.uuid1())[0:12]
            new_node.Project = mind_file.ProjectID
            new_node.save()
            MindmapService.copy_tags(root_node.node,new_node)
            if parent == 0:
                new_file = models.ProjectXmindFile.objects.get(int(mind_file.id))
                new_file.RootTopic = new_node.id
                new_file.save()
            if len(root_node.children) > 0:
                for child_node in root_node.children:
                    temp_flag = MindmapService.save_topic_tree(new_node.id,child_node,mind_file,priority)
                    if temp_flag:
                        result = True

                if not result and parent !=0:
                    MindmapService.delete_tags(new_node)
                    new_node.delete()

            else:
                if new_node.Priority in priority:
                    result = True

                if new_node.Priority not in priority and not result and parent != 0:
                    MindmapService.delete_tags(new_node)
                    new_node.delete()
                    result = False

        return result

    @staticmethod
    def get_leaf_node(root_node,leaf_list):
        if root_node:
            if root_node.leaf:
                leaf_list.append(root_node)
                return
            else:
                for node in root_node.children:
                    MindmapService.get_leaf_node(node,leaf_list)


    @staticmethod
    def copy_tags(source_topic,target_topic):
        copyed_tags = models.ProjectXmindTopicTagMap.objects.get_topic_tags(source_topic.id)
        for tag in copyed_tags:
            temp_tag = copy.deepcopy(tag)
            temp_tag.id = None
            temp_tag.TopicID = target_topic.id
            temp_tag.save()

    @staticmethod
    def delete_tags(target_topic):
        try:
            copyed_tags = models.ProjectXmindTopicTagMap.objects.get_topic_tags(target_topic.id)
            copyed_tags.delete()
        except Exception as ex:
            SimpleLogger.exception(ex)

    @staticmethod
    def add_auto_tag_by_import_case(auto_case):
        try:
            if auto_case is not None:
                if auto_case.TestCaseKey:
                    all_topic_bykey = models.ProjectXmindTopic.objects.all().filter(OriginalID=auto_case.TestCaseKey)
                    all_tag_bykey = models.ProjectXmindTopicTagMap.objects.all().filter(OriginalID=auto_case.TestCaseKey)
                    all_topic_ids = [tag.TopicID for tag in all_tag_bykey]
                    for topic in all_topic_bykey:
                        if topic.id not in all_topic_ids:
                            temp_tag = models.ProjectXmindTopicTagMap()
                            temp_tag.OriginalID = auto_case.TestCaseKey
                            temp_tag.TopicID = topic.id
                            temp_tag.TagID = 10
                            temp_tag.save()
        except Exception as ex:
            SimpleLogger.exception(ex)