# coding=utf-8
"""
Created on 2015-10-23

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from business.project.module_service import ModuleService
from teamvision.project.models import Project, ProjectMember, ProductSpace, ProjectModule, Version, ProductSpaceUser
from gatesidelib.common.simplelogger import SimpleLogger
from django.contrib.admin.models import DELETION, CHANGE, ADDITION
from business.project.version_service import VersionService
from business.auth_user.user_service import UserService
from business.project.product_space_service import ProductSpaceService
import random, datetime
from django.core.cache import cache


class ProjectService(object):
    """
    classdocs
    """

    @staticmethod
    def get_latest_projects_include_me(request):
        result = list()
        latest_projects = ProjectService.get_latest_projects(request)
        my_projects = ProjectService.get_projects_include_me(request)
        my_project_ids = [project.id for project in my_projects]
        for project in latest_projects:
            if project and project.id in my_project_ids:
                result.append(project)
        return result[0:6]

    @staticmethod
    def get_latest_projects(request):
        result = list()
        latest_project_ids = VersionService.get_latests_project_ids(request)
        for project_id in latest_project_ids:
            temp_project = Project.objects.get(project_id)
            result.append(temp_project)
        return result

    @staticmethod
    def get_projects_include_me(request):
        space_id = request.GET.get('space', 0)
        if space_id == 0:
            my_default_space = ProductSpaceService.my_default_space(request.user.id)
            space_project = ProjectService.get_projects_by_space(my_default_space)
        else:
            space_project = ProjectService.get_projects_by_space(space_id)

        my_permission = UserService.get_system_permission(request.user.id)
        if my_permission > 3:
            member_list = ProjectMember.objects.all().filter(PMMember=request.user.id)
            project_ids = [member.PMProjectID for member in member_list]
            result = space_project.filter(id__in=project_ids)
        else:
            result = space_project
        return result

    @staticmethod
    def get_projects_by_space(space_id):
        cache_key = "project_list_space_" + str(space_id)
        project_list = cache.get(cache_key)
        if project_list is not None:
            return project_list
        else:
            project_list = Project.objects.filter(ProductSpace=space_id).filter(IsActive=1)
            cache.set(cache_key, project_list, timeout=30)
        return project_list

    @staticmethod
    def get_project_ids_by_space(space_id):
        cache_key = "project_ids_space_" + str(space_id)
        project_ids = cache.get(cache_key)
        if project_ids is not None:
            return project_ids
        else:
            project_ids = list(
                Project.objects.filter(ProductSpace=space_id).filter(IsActive=1).values_list('id', flat=True))
            cache.set(cache_key, project_ids, timeout=60)
        return project_ids

    @staticmethod
    def get_space_project_dict(space_id):
        cache_key = "project_id_name_dict_space_" + str(space_id)
        project_id_name_dict = cache.get(cache_key)
        if project_id_name_dict is not None:
            return project_id_name_dict
        else:
            project_id_name_list = Project.objects.filter(ProductSpace=space_id).filter(IsActive=1).values_list('id', 'PBTitle')
            project_id_name_dict = {}
            for key, value in project_id_name_list:
                project_id_name_dict[key] = value
            cache.set(cache_key, project_id_name_dict, timeout=60)
        return project_id_name_dict

    @staticmethod
    def get_project_name_by_id(project_id):
        project_dict = cache.get("project_dict")
        if project_dict is not None:
            try:
                p_name = project_dict[project_id]
            except Exception as e:
                p_name = "无"
            return p_name
        else:
            project_id_name_list = Project.objects.values_list('id', 'PBTitle')
            project_id_name_dict = {}
            for key, value in project_id_name_list:
                project_id_name_dict[key] = value
            cache.set("project_dict", project_id_name_dict, timeout=60)
            return project_id_name_dict[project_id]

    @staticmethod
    def get_project_key(project_id):
        project_key_dict = cache.get("project_key_dict")
        if project_key_dict is None:
            project_id_key_list = Project.objects.filter(IsActive=1).values_list('id', 'PBKey')
            project_id_key_dict = {}
            for key, value in project_id_key_list:
                project_id_key_dict[key] = value
            cache.set("project_key_dict", project_id_key_dict, timeout=60)
            return project_id_key_dict[project_id]
        return project_key_dict[project_id]

    @staticmethod
    def get_product_space_include_me(user_id):
        space_user_list = ProductSpaceUser.objects.all().filter(User=user_id)
        space_ids = [space_user.ProductSpace for space_user in space_user_list]
        return ProductSpace.objects.filter(id__in=space_ids)

    @staticmethod
    def get_project_modules(project_id):
        return ProjectModule.objects.project_modules(int(project_id))

    @staticmethod
    def get_project_lead(project_id):
        project = Project.objects.get(project_id)
        user = UserService.get_user(project.PBLead)
        return user

    @staticmethod
    def create_project(request):
        result = None
        try:
            project = Project()
            project = ProjectService.init_project(request.data, project)
            if project == 0:
                return 0
            project.PBCreator = request.user.id
            project.save()
            if str(request.user.id) != str(project.PBLead):
                ProjectService.add_member(request.user.id, project.id, 5)
                ProjectService.add_member(project.PBLead, project.id, 4)
            else:
                ProjectService.add_member(request.user.id, project.id, 4)
            ProjectService.create_version(project, request.user)
            ProjectService.create_module(project, request.user)
            ProjectService.log_create_activity(request.user, project)
            result = project
        except Exception as ex:
            SimpleLogger.error(ex)
        return result

    @staticmethod
    def create_version(project, user):
        version = Version()
        version.VProjectID = project.id
        version.VVersion = '1.0.0'
        version.CFTCommitor = user.id
        version.VStartDate = str(datetime.date.today())
        version.VReleaseDate = str(datetime.date.today())
        version.save()
        VersionService.log_create_activity(user, version)

    @staticmethod
    def create_module(project, user):
        project_module = ProjectModule()
        project_module.Name = '需求池'
        project_module.VProjectID = project.id
        project_module.save()
        ModuleService.log_create_activity(user, project_module)

    @staticmethod
    def edit_project(request, projectid):
        temp_project = Project.objects.get(projectid)
        project = ProjectService.init_project(request.POST, temp_project)
        project.save()
        ProjectService.log_change_activity(request.user, project)

    @staticmethod
    def delete_project(request, projectid):
        project = Project.objects.get(projectid)
        project.IsActive = 0
        project.save()
        ProjectService.log_delete_activity(request.user, project)

    @staticmethod
    def init_project(validate_data, project):
        tmp_project = project
        if Project.objects.filter(PBTitle=validate_data.get('PBTitle')):
            return 0
        tmp_project.PBTitle = validate_data.get('PBTitle')
        tmp_project.PBDescription = validate_data.get('PBDescription')
        if Project.objects.filter(PBKey=validate_data.get('PBKey')):
            return 0
        tmp_project.PBKey = validate_data.get('PBKey')
        tmp_project.PBPlatform = validate_data.get('PBPlatform')
        tmp_project.PBVisiableLevel = validate_data.get('PBVisiableLevel')
        tmp_project.PBLead = validate_data.get('PBLead')
        tmp_project.ProductSpace = validate_data.get('ProductSpace')
        tmp_project.PBHttpUrl = validate_data.get('PBHttpUrl')
        tmp_project.PBAvatar = "/static/global/images/project-icon/scenery-" + str(random.randint(1, 24)) + ".png"
        return tmp_project

    @staticmethod
    def add_member(user, projectid, Role):
        project_member = ProjectMember()
        project_member.PMProjectID = projectid
        project_member.PMMember = user
        project_member.PMRoleID = Role
        project_member.PMRoleType = 1
        project_member.save()

    @staticmethod
    def log_create_activity(user, project):
        Project.objects.log_action(user.id, project.id, project.PBTitle, ADDITION, "创建了项目", project.id)

    @staticmethod
    def log_delete_activity(user, project):
        Project.objects.log_action(user.id, project.id, project.PBTitle, DELETION, "删除了项目", project.id)

    @staticmethod
    def log_change_activity(user, project):
        Project.objects.log_action(user.id, project.id, project.PBTitle, CHANGE, "修改了项目", project.id)
