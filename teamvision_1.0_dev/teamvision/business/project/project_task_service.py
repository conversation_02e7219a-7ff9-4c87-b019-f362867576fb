# coding=utf-8
"""
@Create: 2024/8/15 15:18
@Author: zhangpeng
"""
from django.core.cache import cache
from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.project.models import ProjectTaskStatus


class ProjectTaskStatusService(object):

    @staticmethod
    def get_status_name(status_type, status):
        status_name = "--"
        task_name_dict_key = "project_task_name_dict_type_" + str(status_type)
        task_name_dict = cache.get(task_name_dict_key)
        if task_name_dict is None:
            project_task_status_desc_list = ProjectTaskStatus.objects.filter(Type=status_type).values_list('Status', 'Desc')
            project_task_name_dict = {}
            for status_, desc in project_task_status_desc_list:
                project_task_name_dict[status_] = desc
            cache.set(task_name_dict_key, project_task_name_dict, timeout=30)
            task_name_dict = cache.get(task_name_dict_key)
        
        try:
            status_name = task_name_dict[status]
            return status_name
        except Exception as e:
            SimpleLogger.info(e)
            return status_name
