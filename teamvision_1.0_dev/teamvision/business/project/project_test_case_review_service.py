from teamvision.project import models
from teamvision.ci.models import CaseTag
import uuid, copy
from business.project.topic_tree_node import TopicTreeNode
from teamvision.project.mongo_models import FortestingMongoFile
from business.common.file_info_service import FileInfoService
import xmind, os
from teamvision.settings import BASE_DIR
from gatesidelib.common.simplelogger import SimpleLogger
from business.auth_user.user_service import UserService
from business.common.system_config_service import SystemConfigService
from teamvision.settings import WEB_HOST
from teamvision.settings import EMAIL_TEMPLATES
from business.business_service import BusinessService
from business.common.emailservice import EmailService
from business.project.memeber_service import MemberService
from teamvision.project.models import Project
from json.decoder import J<PERSON>NDecoder
from business.project.project_testcase_service import TestCaseService


class CaseReviewService(object):
    '''
    classdocs
    '''

    @staticmethod
    def create_case_review(form_data, user):
        case_review = models.ProjectCaseReview()
        CaseReviewService.init_casereview(form_data, case_review, user)
        return case_review

    @staticmethod
    def init_casereview(form_data, case_review, user):
        case_review_field_name = [field.name for field in models.ProjectCaseReview._meta.fields]
        CaseReviewService.init_model_instance(case_review, case_review_field_name, form_data)
        case_review.save()
        case_review_reviewers = form_data.get("Reviewer", [])
        # CaseReviewService.send_notification_email(case_review, case_review_reviewers, EMAIL_TEMPLATES['CaseReview'])
        CaseReviewService.create_case_review_reviewer(case_review, case_review_reviewers)
        case_review_requirement_ids = form_data.get("Requirement", [])
        CaseReviewService.create_case_review_requirement(case_review.id, case_review_requirement_ids)
        checked_group = form_data.get("SelectCaseGroup", [])
        for group in checked_group:
            testcase = models.ProjectTestCase.objects.get(id=group)
            if testcase.IsGroup:
                CaseReviewService.create_case_review_testcase(case_review.id, group, user.id, True, True)
            else:
                CaseReviewService.create_case_review_testcase(case_review.id, group, user.id, False, False)
        half_check_group = form_data.get("HalfCaseGroup", [])
        for group in half_check_group:
            CaseReviewService.create_case_review_testcase(case_review.id, group, user.id, False, True)
        return case_review

    @staticmethod
    def create_case_review_testcase(case_review_id, case_id, user_id, is_checked, is_group):
        """
        :param case_review_id:
        :param case_id:
        :param user_id:
        :param is_checked:
        :param is_group:
        :return:
        """
        case_review_testcase = models.ProjectCaseReviewTestcase()
        case_review_testcase.CaseReview = case_review_id
        case_review_testcase.TestCase = case_id
        case_review_testcase.Creator = user_id
        case_review_testcase.IsGroup = is_group
        case_review_testcase.IsChecked = is_checked
        case_review_testcase.save()

    @staticmethod
    def init_model_instance(model_instance, model_fields, form_data):
        for field in model_fields:
            if field in form_data.keys():
                value = form_data.get(field)
                model_instance.__setattr__(field, value)
        return model_instance

    @staticmethod
    def create_case_review_reviewer(case_review, case_review_reviewers):
        for reviewer in case_review_reviewers:
            case_reviewer = models.ProjectCaseReviewReviewer()
            case_reviewer.CaseReview = case_review.id
            case_reviewer.Reviewer = reviewer
            case_reviewer.save()

    @staticmethod
    def create_case_review_requirement(case_review_id, requirement_ids):
        for require_id in requirement_ids:
            review_require = models.ProjectCaseReviewRequirement()
            review_require.case_review_id = case_review_id
            review_require.requirement_id = require_id
            review_require.save()

    @staticmethod
    def send_notification_email(case_review, case_review_reviewers, email_tempalte_path):
        creator = UserService.get_user(case_review.Creator)
        email_list = [creator.email]
        for case_reviewer in case_review_reviewers:
            reviewer = UserService.get_user(case_reviewer)
            email_list.append(reviewer.email)
        email_config = SystemConfigService.get_email_config()
        email_message = CaseReviewService.create_email_message(case_review, case_review_reviewers, email_tempalte_path)
        project_name = Project.objects.get(case_review.Project).PBTitle
        subject = "[用例评审通知]" + project_name + "-" + case_review.Title
        # BusinessService.send_email(email_config, email_list, email_message, subject)
        file_path = CaseReviewService.export_casereview_xmind(case_review)
        file_list = [{'name': 'testcase.xmind', 'path': file_path}]
        project_member = MemberService.get_project_member_emails(case_review.Project)
        BusinessService.send_email_file(email_list, email_message, subject, cc=project_member, file_list=file_list)

    @staticmethod
    def create_email_message(case_review, case_review_reviewers, email_template_path):
        email_templates = open(email_template_path, 'rb').read().decode()
        case_review_adress = WEB_HOST + '/project/' + str(case_review.Project) + '/test/case-reviews/' + str(
            case_review.id)
        tester = UserService.get_user(case_review.Creator)
        tester_name = tester.last_name + tester.first_name
        reviewer_names = ""
        for case_review_reviewer in case_review_reviewers:
            reviewer = UserService.get_user(case_review_reviewer)
            reviewer_name = reviewer.last_name + reviewer.first_name
            reviewer_names = reviewer_names + reviewer_name + ","
        email_templates = email_templates.replace("${CASEREVIEWTITLE}", case_review.Title)
        email_templates = email_templates.replace("${CASEREVIEWNAME}", case_review.Desc)
        email_templates = email_templates.replace("${CASEREVIEWREVIRWER}", reviewer_names)
        email_templates = email_templates.replace("${TESTER}", tester_name)
        email_templates = email_templates.replace("${CASEADDRESS}", case_review_adress)
        email_templates = email_templates.replace("${CASEREVIEWRESULT}", case_review.CaseReviewResult)
        return email_templates

    @staticmethod
    def export_casereview_xmind(case_review):
        try:
            temp_file_path = BASE_DIR + '/static/temp_files/' + case_review.Title + '_' + str(case_review.id) + '.xmind'
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

            workbook = xmind.load(temp_file_path)
            sheet1 = workbook.getPrimarySheet()
            sheet1.setTitle(case_review.Title)  # 设置画布名称
            root_topic = sheet1.getRootTopic()
            root_topic.setTitle(case_review.Title)  # 设置主题名称

            CaseReviewService.get_case_tree(case_review, root_topic)
            xmind.save(workbook=workbook)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return temp_file_path

    @staticmethod
    def get_case_tree(case_review, root_topic):
        root_node = list()
        casereview_id = case_review.id
        caseewview = models.ProjectCaseReview.objects.get(casereview_id)
        casereview_cases = models.ProjectCaseReviewTestcase.objects.get_case_review_cases(casereview_id)
        casereview_case_ids = [case.TestCase for case in casereview_cases]

        if caseewview is not None:
            root_node = models.ProjectTestCase.objects.filter(Project=caseewview.Project).filter(Parent=0).filter(
                id__in=casereview_case_ids)

        try:
            for node in root_node:
                sub_topic = root_topic.addSubTopic()
                sub_topic.setTitle(node.Title)
                CaseReviewService.get_child_node(node, sub_topic, casereview_case_ids)

        except Exception as ex:
            SimpleLogger.exception(ex)

    @staticmethod
    def get_child_node(node, node_topic, casereview_case_ids):
        children_case = models.ProjectTestCase.objects.get_children(node.id).filter(IsActive=1).filter(
            id__in=casereview_case_ids)

        try:
            if children_case is not None:
                for children in children_case:
                    sub_topic = node_topic.addSubTopic()
                    sub_topic.setTitle(children.Title)

                    CaseReviewService.get_child_node(children, sub_topic, casereview_case_ids)
        except Exception as ex:
            SimpleLogger.exception(ex)

    @staticmethod
    def update_casereview_case(casereview_id, case_id, checked, is_group, user_id):
        result = models.ProjectCaseReviewTestcase.objects.filter(CaseReview=casereview_id).filter(
            TestCase=case_id).exists()
        if result == False:
            review_case = models.ProjectCaseReviewTestcase()
            review_case.IsGroup = is_group
            review_case.IsChecked = checked
            review_case.Creator = user_id
            review_case.TestCase = case_id
            review_case.CaseReview = casereview_id
            review_case.save()

        case_review_case_count = models.ProjectCaseReviewTestcase.objects.filter(CaseReview=casereview_id).filter(
            IsGroup=False).count()
        models.ProjectCaseReview.objects.filter(id=casereview_id).update(CaseCount=case_review_case_count)

    @staticmethod
    def delete_test_review_case(case_review_id, case_id):
        test_case = models.ProjectCaseReviewTestcase.objects.filter(CaseReview=case_review_id).filter(
            TestCase=case_id).first()
        if test_case is not None:
            test_case.delete()
            if test_case.IsGroup is True:
                test_case_id_list = models.ProjectTestCase.objects.filter(Parent=test_case.TestCase).values_list('id',
                                                                                                                 flat=True)
                test_review_case_id_list = models.ProjectCaseReviewTestcase.objects.filter(
                    CaseReview=case_review_id).filter(TestCase__in=test_case_id_list).values_list('id', flat=True)
                for _id in test_review_case_id_list:
                    CaseReviewService.delete_test_review_case(case_review_id, _id)
        case_review_case_count = models.ProjectCaseReviewTestcase.objects.filter(CaseReview=case_review_id).filter(
            IsGroup=False).count()
        models.ProjectCaseReview.objects.filter(id=case_review_id).update(CaseCount=case_review_case_count)

    @staticmethod
    def save_review_case_mindmap(user_id, review_id, mind_tree):
        mind_tree_case = mind_tree["children"]
        latest_topic_ids = list()
        # print(mind_tree)
        result = dict()
        for test_case in mind_tree_case:
            case_group_instance = models.ProjectTestCase.objects.get(test_case['data']['id'])
            if case_group_instance is not None:
                if case_group_instance.Parent == 0:
                    case_group_instance.Title = mind_tree['data']['Title']
                    latest_topic_ids.append(case_group_instance.id)
                    for children_case in test_case['children']:
                        TestCaseService.save_mind_topic(case_group_instance.id, children_case, result, user_id)
                        for value in result.values():
                            latest_topic_ids.append(int(value))
                else:
                    TestCaseService.save_mind_topic(case_group_instance.Parent, test_case, result, user_id)
                    for value in result.values():
                        latest_topic_ids.append(int(value))

        models.ProjectCaseReviewTestcase.objects.filter(CaseReview=review_id).delete()

        latest_topic_ids = list(set(latest_topic_ids))
        # print(latest_topic_ids)
        for latest_test_case_id in latest_topic_ids:
            testcase = models.ProjectTestCase.objects.get(id=latest_test_case_id)
            if testcase.IsGroup:
                CaseReviewService.create_case_review_testcase(review_id, latest_test_case_id, user_id, True, True)
            else:
                CaseReviewService.create_case_review_testcase(review_id, latest_test_case_id, user_id, False, False)

        return latest_topic_ids
