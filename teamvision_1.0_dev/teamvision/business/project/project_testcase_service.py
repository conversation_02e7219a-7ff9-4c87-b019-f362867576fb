# coding=utf-8
"""
Created on 2015-11-17
@author: <PERSON><PERSON>
"""
from datetime import datetime

from business.auth_user.user_service import UserService
from teamvision.api.project.serializer.project_testcase_serializer import ProjectTestCaseSerializer
from teamvision.project import models
from teamvision.ci.models import CaseTag
import uuid, copy
from business.project.topic_tree_node import TopicTreeNode
from teamvision.project.mongo_models import FortestingMongoFile
from business.common.file_info_service import FileInfoService
import xmind, os
from teamvision.settings import BASE_DIR
from gatesidelib.common.simplelogger import SimpleLogger
from json.decoder import J<PERSON>NDecoder
from collections import defaultdict
from django.db import connection
from django.core.cache import cache
from django.core import serializers
from django.utils.timezone import localtime


class TestCaseService(object):
    """
        Test Case Service
    """

    @staticmethod
    def save_mind_case(group_id, mind_tree, user_id):
        result = dict()
        case_group = models.ProjectTestCase.objects.get(group_id)
        if case_group is not None:
            json_decoder = JSONDecoder()
            validate_data = json_decoder.decode(mind_tree)
            TestCaseService.save_mind_topic(group_id, validate_data['root'], result, user_id)
            latest_topic_ids = list()
            for value in result.values():
                latest_topic_ids.append(int(value))
            all_cases = models.ProjectTestCase.objects.get_descendant(str(group_id))
            for topic in all_cases:
                if topic.id not in latest_topic_ids:
                    TestCaseService.delete_child(topic)
        return result

    @staticmethod
    def save_mind_case_diff(user_id, tree_added, tree_deleted, tree_modified):
        """先添加，再修改，再删除
        """
        del_failed = []
        del_success = []
        for del_case in tree_deleted:
            try:
                topic = models.ProjectTestCase.objects.get(del_case['id'])
                del_list = TestCaseService.delete_child(topic)
                for case_id in del_list:
                    del_success.append(case_id)
            except Exception as e:
                SimpleLogger.exception(e)
                del_failed.append(
                    {
                        'id': del_case['id'],
                        'failed': str(e)
                    }
                )

        add_failed = []
        add_success = dict()
        for add_case in tree_added:
            try:
                TestCaseService.save_mind_topic(add_case['data']['Parent'], add_case, add_success, user_id)
            except Exception as e:
                SimpleLogger.exception(e)
                add_failed.append({
                    'case': add_case,
                    'failed': str(e)
                }
                )

        update_failed = []
        update_success = []
        for modified_case in tree_modified:
            try:
                topic = models.ProjectTestCase.objects.get(modified_case['id'])
                topic.Priority = modified_case.get("priority", None)
                #    if datetime.strftime(localtime(topic.UpdateTime), '%Y-%m-%d %H:%M:%S') == modified_case.get("UpdateTime", None):
                topic.save()
                modified_case['Title'] = modified_case['text']
                serializer = ProjectTestCaseSerializer(topic, data=modified_case, partial=True)
                serializer.is_valid()
                serializer.save()
                update_success.append(modified_case['id'])
            #    else:
            #        update_failed.append(modified_case['id'])
            except Exception as e:
                SimpleLogger.exception(e)
                update_failed.append(
                    {
                        'id': modified_case['id'],
                        'failed': str(e)
                    }
                )

        result = {
            'add': {
                'success': add_success,
                'failed': add_failed
            },
            'del': {
                'success': del_success,
                'failed': del_failed
            },
            'update': {
                'success': update_success,
                'failed': update_failed
            }
        }
        return result

    @staticmethod
    def delete_child(parent_topic):
        del_list = [parent_topic.id]
        all_cases = models.ProjectTestCase.objects.get_descendant(parent_topic.id)
        for topic in all_cases:
            topic.delete()
            del_list.append(topic.id)
        parent_topic.delete()
        return del_list

    @staticmethod
    def save_mind_topic(parent_id, data, result, user_id):
        id = data['data'].get("id", None)
        parent = models.ProjectTestCase.objects.get(parent_id)
        parent.IsGroup = True
        parent.save()
        topic = None
        if id is not None:
            if str(id).isdigit():
                '''更新节点信息'''
                topic = models.ProjectTestCase.objects.get(id)
                if topic is not None:
                    topic = TestCaseService.init_topic(data['data'], topic, user_id, parent.Project)
                    if topic.id != parent.id:
                        topic.Parent = parent_id
                    topic.save()
                    result[str(id)] = str(topic.id)
                    # result[str(id)] = str(topic.id) + ":" + topic.OriginalID
                else:
                    topic = models.ProjectTestCase()
                    topic = TestCaseService.init_topic(data['data'], topic, user_id, parent.Project)
                    topic.Parent = parent_id
                    topic.save()
                    # TestCaseService.create_topic_tag(topic, resource, name_idmaps)
                    result[str(id)] = str(topic.id)

            if not str(id).isdigit():
                '''全新插入节点'''
                topic = models.ProjectTestCase()
                topic = TestCaseService.init_topic(data['data'], topic, user_id, parent.Project)
                topic.Parent = parent_id
                topic.save()
                result[str(id)] = str(topic.id)

            if len(data['children']) > 0:
                topic.IsGroup = True
                topic.save()
                for child in data['children']:
                    TestCaseService.save_mind_topic(topic.id, child, result, user_id)
            else:
                if topic.id != parent_id:
                    topic.IsGroup = False
                else:
                    topic.IsGroup = True
                topic.save()

    @staticmethod
    def init_topic(data, topic, user_id, project_id):
        if topic is not None:
            topic.Creator = user_id
            topic.Project = project_id
            topic.Priority = data.get("priority", None)
            topic.Title = data.get("text", "分支主题")
            topic.Desc = data.get("Desc", None)
            topic.Precondition = data.get("Precondition", None)
            topic.ExpectResult = data.get("ExpectResult", None)
        return topic

    @staticmethod
    def export_xmind(group_id, user):
        result = None
        try:
            root_node = TopicTreeNode()
            root_case = models.ProjectTestCase.objects.get(group_id)
            TestCaseService.get_case_tree(root_case.Parent, root_case, root_node)
            temp_file_path = BASE_DIR + '/static/temp_files/' + root_case.Title + '_' + str(root_case.id) + "_" + str(
                user.id) + "_.xmind"
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            workbook = xmind.load(temp_file_path)
            sheet1 = workbook.getPrimarySheet()
            sheet1.setTitle(root_case.Title)  # 设置画布名称
            xmind_root_topic = sheet1.getRootTopic()
            TestCaseService.gen_xmind_sheet(root_node, xmind_root_topic, sheet1)
            xmind.save(workbook)
            result = temp_file_path
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    @staticmethod
    def gen_tv_topic(tv_topic, xmind_topic):
        try:
            tv_topic.Text = str(xmind_topic.getTitle())
            tv_topic.OriginalID = str(xmind_topic.getID())[0:12]
            if xmind_topic.getHyperlink() is not None:
                tv_topic.Link = str(xmind_topic.getHyperlink())
            if xmind_topic.getComments() is not None:
                tv_topic.Note = str(xmind_topic.getComments())
            for marker in xmind_topic.getMarkers():
                if str(marker.getMarkerId()).startswith('priority'):
                    tv_topic.Priority = int(str(marker.getMarkerId())[9:10])
        except Exception as ex:
            SimpleLogger.exception(ex)
        return tv_topic

    # 设置主题并设置名称 add by rain 20201027 start
    @staticmethod
    def set_topic_title(main_topic, sub_topic, title, key):
        if sub_topic.get(key):
            temp_topic = main_topic.addSubTopic()
            temp_topic.setTitle(title)
            temp_topic_sub = temp_topic.addSubTopic()
            temp_topic_sub.setTitle(sub_topic.get(key))

    @staticmethod
    def gen_xmind_sheet(tv_topic, xmind_parent_topic, sheet):
        TestCaseService.gen_xmind_topic(tv_topic.node, xmind_parent_topic)
        if len(tv_topic.children) > 0:
            for tv_topic in tv_topic.children:
                sub_topic = xmind_parent_topic.addSubTopic()
                # 设置前置条件-用例描述-预期结果  add by rain 20201027 start
                _case_info = tv_topic.__dict__
                if _case_info.get('node'):
                    _temp_tv_topic = _case_info.get('node').__dict__
                    TestCaseService.set_topic_title(sub_topic, _temp_tv_topic, '前置条件', 'Precondition')
                    TestCaseService.set_topic_title(sub_topic, _temp_tv_topic, '用例描述', 'Desc')
                    TestCaseService.set_topic_title(sub_topic, _temp_tv_topic, '预期结果', 'ExpectResult')
                # 设置前置条件-用例描述-预期结果  add by rain 20201027 tail
                TestCaseService.gen_xmind_topic(tv_topic.node, sub_topic)
                TestCaseService.gen_xmind_sheet(tv_topic, sub_topic, sheet)
        else:
            return

    @staticmethod
    def gen_xmind_topic(tv_topic, xmind_topic):
        try:
            xmind_topic.setTitle(tv_topic.Title)
            if tv_topic.Priority is not None and tv_topic.Priority:
                if tv_topic.IsGroup:
                    pass
                else:
                    xmind_topic.addMarker("priority-" + str(tv_topic.Priority))
        except Exception as ex:
            SimpleLogger.exception(ex)
        return xmind_topic

    @staticmethod
    def get_product_space_repeat_rate(project_ids):
        sql = """select count(DISTINCT CONCAT(t1.Title,t2.Title,t3.Title)) / count(CONCAT(t1.Title,t2.Title,t3.Title)) from project_test_case t1
                 inner join (select id,Title,Parent from project_test_case) t2 on t1.Parent = t2.id
                 inner join (select id,Title from project_test_case) t3 on t2.Parent = t3.id
                 where t1.IsGroup=0 and t1.Project in ({project_id_list})"""
        sql = sql.replace("{project_id_list}", project_ids)
        try:
            cursor = connection.cursor()
            cursor.execute(sql)
            repeat_rate = cursor.fetchall()
            result = repeat_rate
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    # @staticmethod
    # def delete_child(parent_topic):
    #     old_topic_id = parent_topic.id
    #     if parent_topic is not None:
    #         MindmapService.delete_tags(parent_topic)
    #         children = models.ProjectXmindTopic.objects.get_children(old_topic_id,parent_topic.MindFileID).filter(IsActive=1)
    #         if len(children) > 0:
    #             for child_topic in children:
    #                 MindmapService.delete_child(child_topic)
    #             if str(parent_topic.Parent) != "0":
    #                 parent_topic.delete()
    #         else:
    #             if str(parent_topic.Parent) != "0":
    #                 parent_topic.delete()
    #     else:
    #         return

    # @staticmethod
    # def copy_mindmap_file(form_data,file_id, user):
    #     result = None
    #     old_file = models.ProjectXmindFile.objects.get(int(file_id))
    #     if old_file:
    #         temp_file = old_file
    #         temp_file.id = None
    #         temp_file.RootTopic = 0
    #         temp_file.Owner = user.id
    #         temp_file.ProjectID = form_data.get("ProjectID")
    #         temp_file.VersionID = form_data.get("VersionID")
    #         temp_file.FileType = form_data.get("FileType")
    #         temp_file.FileName = form_data.get("FileName", "新建测试点")
    #         temp_file.save()
    #         priority = form_data.get("Priority",[0])
    #         if 0 in priority:
    #             priority = [1,2,3,4,5,6,7,8,9,0,None]
    #         old_file = models.ProjectXmindFile.objects.get(int(file_id))
    #         old_root_node = models.ProjectXmindTopic.objects.get(old_file.RootTopic)
    #         MindmapService.copy_topic_tree(old_root_node,temp_file.id,priority)
    #     return temp_file
    #
    #
    # @staticmethod
    # def copy_topic_tree(root_topic,file_id,priority):
    #     root_node = TopicTreeNode()
    #     MindmapService.get_topic_tree(0,root_topic,root_node)
    #     mind_file = models.ProjectXmindFile.objects.get(int(file_id))
    #     MindmapService.save_topic_tree(0,root_node,mind_file,priority)

    @staticmethod
    def get_case_tree(parent, node, tree_node):
        if node is not None:
            tree_node.node = node
            tree_node.parent = parent
            tree_node.priority = node.Priority
            child_nodes = models.ProjectTestCase.objects.get_children(node.id).filter(IsActive=1)
            if len(child_nodes) > 0:
                for child_node in child_nodes:
                    temp_tree_node = TopicTreeNode()
                    TestCaseService.get_case_tree(node.id, child_node, temp_tree_node)
                    tree_node.children.append(temp_tree_node)
            else:
                tree_node.leaf = True
                return tree_node
        else:
            return tree_node

    # @staticmethod
    # def save_topic_tree(parent,root_node,mind_file,priority):
    #     result = False
    #     if root_node.node is not None:
    #         new_node = copy.deepcopy(root_node.node)
    #         new_node.id = None
    #         new_node.Parent = parent
    #         new_node.MindFileID = mind_file.id
    #         if new_node.Project != mind_file.ProjectID:
    #             new_node.OriginalID = str(uuid.uuid1())[0:12]
    #         new_node.Project = mind_file.ProjectID
    #         new_node.save()
    #         MindmapService.copy_tags(root_node.node,new_node)
    #         if parent == 0:
    #             new_file = models.ProjectXmindFile.objects.get(int(mind_file.id))
    #             new_file.RootTopic = new_node.id
    #             new_file.save()
    #         if len(root_node.children) > 0:
    #             for child_node in root_node.children:
    #                 temp_flag = MindmapService.save_topic_tree(new_node.id,child_node,mind_file,priority)
    #                 if temp_flag:
    #                     result = True
    #
    #             if not result and parent !=0:
    #                 MindmapService.delete_tags(new_node)
    #                 new_node.delete()
    #
    #         else:
    #             if new_node.Priority in priority:
    #                 result = True
    #
    #             if new_node.Priority not in priority and not result and parent != 0:
    #                 MindmapService.delete_tags(new_node)
    #                 new_node.delete()
    #                 result = False
    #
    #     return result
    #
    # @staticmethod
    # def get_leaf_node(root_node,leaf_list):
    #     if root_node:
    #         if root_node.leaf:
    #             leaf_list.append(root_node)
    #             return
    #         else:
    #             for node in root_node.children:
    #                 MindmapService.get_leaf_node(node,leaf_list)
    #
    #
    # @staticmethod
    # def copy_tags(source_topic,target_topic):
    #     copyed_tags = models.ProjectXmindTopicTagMap.objects.get_topic_tags(source_topic.id)
    #     for tag in copyed_tags:
    #         temp_tag = copy.deepcopy(tag)
    #         temp_tag.id = None
    #         temp_tag.TopicID = target_topic.id
    #         temp_tag.save()
    #
    # @staticmethod
    # def delete_tags(target_topic):
    #     try:
    #         copyed_tags = models.ProjectXmindTopicTagMap.objects.get_topic_tags(target_topic.id)
    #         copyed_tags.delete()
    #     except Exception as ex:
    #         SimpleLogger.exception(ex)
    #
    # @staticmethod
    # def add_auto_tag_by_import_case(auto_case):
    #     try:
    #         if auto_case is not None:
    #             if auto_case.TestCaseKey:
    #                 all_topic_bykey = models.ProjectXmindTopic.objects.all().filter(OriginalID=auto_case.TestCaseKey)
    #                 all_tag_bykey = models.ProjectXmindTopicTagMap.objects.all().filter(OriginalID=auto_case.TestCaseKey)
    #                 all_topic_ids = [tag.TopicID for tag in all_tag_bykey]
    #                 for topic in all_topic_bykey:
    #                     if topic.id not in all_topic_ids:
    #                         temp_tag = models.ProjectXmindTopicTagMap()
    #                         temp_tag.OriginalID = auto_case.TestCaseKey
    #                         temp_tag.TopicID = topic.id
    #                         temp_tag.TagID = 10
    #                         temp_tag.save()
    #     except Exception as ex:
    #         SimpleLogger.exception(ex)

    @staticmethod
    # 效率慢废弃
    def get_repeat_rate(case_list):
        case_count = case_list.count()
        project_case_repeat_num = 0
        case_2parent_title_list = []

        case_title_parent = defaultdict(list)
        for testcase in case_list:
            case_title_parent[testcase['id']] = [testcase['Title'], testcase['Parent']]

        for testcase in case_list:
            case_parent_id = testcase['Parent']
            case_title = testcase['Title']
            case_Isgroup = testcase['IsGroup']
            if case_Isgroup == 0:
                if case_parent_id == 0:
                    case_2parent_title_name = case_title
                elif case_title_parent[case_parent_id][1] == 0:
                    case_parent_title = case_title_parent[case_parent_id][0]
                    case_2parent_title_name = "%s:%s" % (case_title, case_parent_title)
                else:
                    case_parent_title = case_title_parent[case_parent_id][0]
                    case_2parent_id = case_title_parent[case_parent_id][1]
                    case_2parent_title = case_title_parent[case_2parent_id][0]
                    case_2parent_title_name = "%s:%s:%s" % (case_title, case_parent_title, case_2parent_title)

                if case_2parent_title_name in case_2parent_title_list:
                    project_case_repeat_num += 1
                else:
                    case_2parent_title_list.append(case_2parent_title_name)

        project_case_repeat_rate = project_case_repeat_num / case_count

        return project_case_repeat_rate

    @staticmethod
    def get_project_test_case_count(project_id):
        return models.ProjectTestCase.objects.filter(Project=project_id).filter(IsGroup=0).count()

    @staticmethod
    def set_opened_testcase_cache(user_id, group_id):
        group_id = int(group_id)
        all_cases = models.ProjectTestCase.objects.get_descendant(group_id)
        all_cases_ids = list()
        all_cases_ids.append(group_id)
        for case in all_cases:
            all_cases_ids.append(case.id)
        opened_cases = {
            'group_id': group_id,
            'uid': user_id,
            'cases': all_cases_ids
        }
        cache_key = 'testcase_tree_opened_groups'
        opened_cases_cache = cache.get(cache_key)
        if opened_cases_cache is None:
            cache_value = list()
            cache_value.append(opened_cases)
            cache.set(cache_key, cache_value, 60 * 60 * 12)
        else:
            for opened_testcase in opened_cases_cache:
                if opened_testcase['uid'] == user_id and opened_testcase['group_id'] == group_id:
                    break
                else:
                    opened_cases_cache.append(opened_cases)
                    cache.set(cache_key, opened_cases_cache, 60 * 60 * 6)

    @staticmethod
    def get_opened_testcase_cache():
        cache_key = 'testcase_tree_opened_groups'
        opened_cases_cache = cache.get(cache_key)
        return opened_cases_cache

    @staticmethod
    def del_opened_testcase_cache(user_id, group_id):
        group_id = int(group_id)
        cache_key = 'testcase_tree_opened_groups'
        opened_cases_cache = cache.get(cache_key)
        if opened_cases_cache is not None:
            for opened_case in opened_cases_cache:
                if opened_case['uid'] == user_id and opened_case['group_id'] == group_id:
                    opened_cases_cache.remove(opened_case)
                    cache.set(cache_key, opened_cases_cache, 60 * 60 * 12)

    @staticmethod
    def is_opened_testcase(user_id, group_id):
        group_id = int(group_id)
        opened_testcase_list = TestCaseService.get_opened_testcase_cache()
        open_user_id = list()
        if opened_testcase_list is not None:
            for opened_testcase in opened_testcase_list:
                if group_id == opened_testcase['group_id'] and opened_testcase['uid'] != user_id:
                    if opened_testcase['uid'] != user_id:
                        open_user_id.append(opened_testcase['uid'])
                        break
                if group_id in opened_testcase['cases']:
                    if opened_testcase['uid'] != user_id:
                        open_user_id.append(opened_testcase['uid'])

                # if opened_testcase['uid'] != user_id and opened_testcase['group_id'] != group_id:
                #     if group_id in opened_testcase['cases']:
                #         open_user_id.append(opened_testcase['uid'])

        all_cases = models.ProjectTestCase.objects.get_descendant(group_id)
        all_cases_ids = list()
        all_cases_ids.append(group_id)
        for case in all_cases:
            if opened_testcase_list is not None:
                for opened_testcase in opened_testcase_list:
                    if case.id in opened_testcase['cases']:
                        if opened_testcase['uid'] != user_id:
                            open_user_id.append(opened_testcase['uid'])

        if open_user_id == []:
            return None
        else:
            u_name = list()
            for u_id in open_user_id:
                u_name.append(UserService.get_name_by_id(u_id))
            return list(dict.fromkeys(u_name))
