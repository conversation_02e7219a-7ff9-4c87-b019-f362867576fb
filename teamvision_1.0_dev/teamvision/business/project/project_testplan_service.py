# coding=utf-8
"""
Created on 2015-11-17

@author: Dev<PERSON>
"""
from datetime import datetime

from asgiref.sync import sync_to_async, async_to_sync

from business.common.emailservice import EmailService
from business.project.fortesting_service import ForTestingService
from business.project.memeber_service import MemberService
from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.api.project.serializer.project_testplan_serializer import ProjectTestPlanSerializer
from teamvision.project import models
from teamvision.ci.models import CaseTag
import uuid, copy
from business.project.topic_tree_node import TopicTreeNode
from teamvision.project.models import TestApplication, ProjectTestPlan, Requirement, ProjectTestPlanCase
from teamvision.project.mongo_models import FortestingMongoFile
from business.common.file_info_service import FileInfoService
import xmind, os
from teamvision.settings import BASE_DIR, EMAIL_TEMPLATES
from django.db.models.functions import Concat
from django.db.models import Value


class TestPlanService(object):
    """
    classdocs
    """

    @staticmethod
    def create_testplan(form_data, user):
        test_plan = models.ProjectTestPlan()
        TestPlanService.init_testplan(form_data, test_plan, user)
        return test_plan

    @staticmethod
    def init_testplan(form_data, test_plan, user):
        test_plan_field_name = [field.name for field in models.ProjectTestPlan._meta.fields]
        TestPlanService.init_model_instance(test_plan, test_plan_field_name, form_data)
        owner = form_data.get("Owner", 0)
        if owner == 0:
            owner = user.id
        test_plan.save()
        include_all = form_data.get("IncludeAllCase", False)
        case_review_id = form_data.get("case_review_id", 0)
        fortestings = form_data.get("fortestings", [])
        TestPlanService.create_testplan_fortesting(test_plan, fortestings)
        if include_all:
            test_case_list = models.ProjectTestCase.objects.all().filter(Project=test_plan.Project)
            for case in test_case_list:
                TestPlanService.create_testplan_case(test_plan, case, case.IsGroup, case.IsGroup, user.id, owner)
        elif case_review_id != 0:
            review_test_case_list = models.ProjectCaseReviewTestcase.objects.filter(CaseReview=case_review_id)
            for review_case in review_test_case_list:
                testcase = models.ProjectTestCase.objects.get(id=review_case.TestCase)
                if testcase is not None:
                    TestPlanService.create_testplan_case(test_plan, testcase, review_case.IsChecked, review_case.IsGroup, user.id, owner)
        else:
            checked_group = form_data.get("SelectCaseGroup", [])
            for group in checked_group:
                testcase = models.ProjectTestCase.objects.get(id=group)
                if testcase.IsGroup == True:
                    TestPlanService.create_testplan_case(test_plan, testcase, True, True, user.id, owner)
                else:
                    TestPlanService.create_testplan_case(test_plan, testcase, False, False, user.id, owner)

                # TestPlanService.create_testplan_case(test_plan,group,True,True,user.id,owner)
                # test_case_list = models.ProjectTestCase.objects.get_children(group).filter(IsGroup=False)
                # for case in test_case_list:
                #    TestPlanService.create_testplan_case(test_plan, case.id, False, False, user.id, owner)

            half_check_group = form_data.get("halfCheckCaseGroup", [])
            for group in half_check_group:
                testcase = models.ProjectTestCase.objects.get(id=group)
                TestPlanService.create_testplan_case(test_plan, testcase, False, True, user.id, owner)

        TestPlanService.update_testplan_casecount(test_plan)
        return test_plan

    @staticmethod
    def create_testplan_case(test_plan, test_case, checked, is_group, user_id, owner):
        plan_case = models.ProjectTestPlanCase()
        plan_case.TestPlan = test_plan
        plan_case.TestCase = test_case
        plan_case.IsGroup = is_group
        plan_case.IsChecked = checked
        plan_case.Creator = user_id
        plan_case.Owner = owner
        plan_case.TestResult = 0
        plan_case.Parent = test_case.Parent
        plan_case.save()

    @staticmethod
    def create_testplan_fortesting(test_plan, fortestings):
        id_list = TestPlanService.get_fortestings_list(test_plan.Project)
        for item in fortestings:
            plan_testing = models.ProjectTestPlanForTesting()
            plan_testing.TestPlan = test_plan.id
            if item in id_list:
                plan_testing.Fortesting = item
            else:
                for_testing = TestPlanService.get_testing_bean(test_plan)
                for_testing.Topic = item
                for_testing.save()
                result_data = TestPlanService.get_fortestings_dict(test_plan.Project)
                plan_testing.Fortesting = result_data.get(item)
            plan_testing.save()

    @staticmethod
    def get_testing_bean(test_plan):
        for_testing = TestApplication()
        for_testing.ProjectID = test_plan.Project
        for_testing.Status = 1
        for_testing.VersionID = test_plan.Version
        for_testing.Creator = test_plan.Creator
        for_testing.Commitor = test_plan.Creator
        for_testing.Testers = test_plan.Creator
        return for_testing

    @staticmethod
    def get_fortestings_list(Project):
        id_list = list()
        qs = models.TestApplication.objects.project_fortestings(project_id=Project)
        for info in qs:
            id_list.append(info.__dict__.get('id'))
        return id_list

    @staticmethod
    def get_fortestings_dict(Project):
        result_data = {}
        qs = models.TestApplication.objects.project_fortestings(project_id=Project)
        for info in qs:
            result_data[info.__dict__.get('Topic')] = info.__dict__.get('id')
        return result_data

    @staticmethod
    def init_model_instance(model_instance, model_fields, form_data):
        for field in model_fields:
            if field in form_data.keys():
                value = form_data.get(field)
                model_instance.__setattr__(field, value)
        return model_instance

    @staticmethod
    def update_fortestings(test_plan, fortestings):
        models.ProjectTestPlanForTesting.objects.filter(TestPlan=test_plan.id).delete()
        for fortesting in fortestings:
            if type(fortesting) == int:
                testplan_fortesting = models.ProjectTestPlanForTesting()
                testplan_fortesting.TestPlan = test_plan.id
                testplan_fortesting.Fortesting = fortesting
                testplan_fortesting.save()
            if type(fortesting) == str:
                TestPlanService.create_testplan_fortesting(test_plan, [fortesting])

    @staticmethod
    def update_testplan_casecount(plan_id):
        test_plan_case_count = models.ProjectTestPlanCase.objects.filter(TestPlan=plan_id).filter(IsGroup=False).count()
        ProjectTestPlan.objects.filter(id=plan_id.id).update(CaseCount=test_plan_case_count)

    @staticmethod
    def get_testplan_fortestings_topic(plan_id):
        fortesting_ids = models.ProjectTestPlanForTesting.objects.filter(TestPlan=plan_id).values_list('Fortesting', flat=True)
        testplan_fortestings = models.TestApplication.objects.all().filter(id__in=fortesting_ids).values_list('Topic', flat=True)
        return testplan_fortestings

    @staticmethod
    def update_testplan_status(test_plan_id, plan_status):
        test_plan = ProjectTestPlan.objects.get(id=test_plan_id)
        test_plan.Status = plan_status
        now = datetime.now()
        fortesting_ids = models.ProjectTestPlanForTesting.objects.filter(TestPlan=test_plan_id).values_list('Fortesting', flat=True)
        requirement_ids = models.RequirementTaskMap.objects.filter(TaskID__in=fortesting_ids).values_list('RequirementID', flat=True)
        if plan_status == 2:
            if test_plan.StartTime is None:
                test_plan.StartTime = now
            test_plan.save()
            TestApplication.objects.filter(id__in=fortesting_ids).update(Status=3, TestingStartDate=now)
            # 4:已提测 5:测试中 6:待上线
            Requirement.objects.filter(id__in=requirement_ids).update(Status=5)

        if plan_status == 3:
            TestApplication.objects.filter(id__in=fortesting_ids).update(Status=4, TestingFinishedDate=now)
            Requirement.objects.filter(id__in=requirement_ids).update(Status=6)
            pass_case_count = ProjectTestPlanCase.objects.filter(TestPlan=test_plan_id).filter(IsGroup=False).filter(TestResult=1).count()
            try:
                test_plan.pass_rate = round(100 * pass_case_count / test_plan.CaseCount, 2)
            except ZeroDivisionError:
                test_plan.pass_rate = 0
            test_plan.FinishTime = now
            test_plan.save()

        status_dict = {
            2: "start",
            3: "finish",
            4: "place",
            5: "pause",
        }
        history_msg = status_dict[plan_status] + '_' + str(now) + '|'
        try:
            test_plan.history = Concat('history', Value(history_msg))
            test_plan.save()
        except Exception as ex:
            SimpleLogger.exception(ex)

    @staticmethod
    def send_test_plan_email(testplan_inst):
        project_member_email_list = MemberService.get_project_member_emails(testplan_inst['Project'])
        subject = "[测试计划]" + testplan_inst['Title']
        email_message = TestPlanService.create_test_plan_email_content(testplan_inst)
        # print(subject, email_message, project_member_email_list)
        EmailService.send_email_multi(subject, email_message, project_member_email_list, )

    @staticmethod
    def create_test_plan_email_content(testplan):
        email_path = EMAIL_TEMPLATES["TestPlan"]
        # email_templates = open(email_path, 'rb', encoding='utf-8').read().decode()
        with open(email_path, 'r', encoding='utf-8') as f:
            email_templates = f.read()
        email_templates = email_templates.replace("${TEST_PLAN_TITLE}", testplan['Title'])
        email_templates = email_templates.replace("${TESTER}", testplan['view_data']['creator_name'])
        email_templates = email_templates.replace("${STARTTIME}", testplan['EstimatedStartTime'])
        email_templates = email_templates.replace("${ENDTIME}", testplan['EstimatedFinishTime'])
        email_templates = email_templates.replace("${DESC}", testplan['Desc'])
        email_templates = email_templates.replace("${VERSION}", testplan['view_data']['version_name'])

        requirement_list = ""
        for testplan_req in testplan['view_data']['requirements']:
            requirement = "<tr><th>#{ID} {REQUIREMENT_TITLE}</th></tr>"
            requirement = requirement.replace("{ID}", str(testplan_req['id']))
            requirement = requirement.replace("{REQUIREMENT_TITLE}", testplan_req['Title'])
            requirement_list = requirement_list + requirement
        if requirement_list != "":
            email_templates = email_templates.replace("${RequirementList}", requirement_list)
        else:
            email_templates = email_templates.replace("${RequirementList}", "无")

        fortesting_list = ""
        for plan_fortesting in testplan['view_data']['fortestings']:
            fortesting = "<tr><th>#{ID} {TITLE} {COMMITOR}</th></tr>"
            fortesting = fortesting.replace("{ID}", str(plan_fortesting['id']))
            fortesting = fortesting.replace("{TITLE}", plan_fortesting['Topic'])
            fortesting = fortesting.replace("{COMMITOR}", plan_fortesting['ViewData']['commitor_name'])
            fortesting_list = fortesting_list + fortesting
        if fortesting_list != "":
            email_templates = email_templates.replace("${ForTestingList}", fortesting_list)
        else:
            email_templates = email_templates.replace("${ForTestingList}", "<tr><th>无</th></tr>")

        return email_templates
