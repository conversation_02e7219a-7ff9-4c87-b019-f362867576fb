# coding=utf-8
"""
Created on 2015-10-23

@author: <PERSON><PERSON><PERSON><PERSON>
"""
from teamvision.project.models import Requirement, ProjectTaskStatus, RequirementTaskMap
from gatesidelib.common.simplelogger import SimpleLogger
from django.contrib.admin.models import DELETION, CHANGE, ADDITION


class RequirementService(object):
    """
    classdocs
    """

    @staticmethod
    def module_requirements(version_filter, status_filter, module_id):
        module_requirements = Requirement.objects.all().filter(Module=module_id).order_by('-CreationTime')
        if version_filter != 0:
            module_requirements = module_requirements.filter(Version=version_filter)
        if status_filter is not None and str(status_filter) != "0":
            module_requirements = module_requirements.filter(Status=status_filter)
        return module_requirements.order_by('Priority')

    @staticmethod
    def project_requirements(version_filter, status_filter, project_id):
        project_requirements = Requirement.objects.all().filter(ProjectID=project_id).order_by('-CreationTime')
        if version_filter != 0:
            project_requirements = project_requirements.filter(Version=version_filter)
        if status_filter != 0:
            project_requirements = project_requirements.filter(Status=status_filter)
        return project_requirements

    @staticmethod
    def update_fortestingid(req_id, fortesting_id):
        req = Requirement.objects.get(int(req_id))
        if req is not None:
            req.FortestingID = int(fortesting_id)
            req_status = ProjectTaskStatus.objects.get_by_value(3, 4)
            req.Status = req_status.id
            req.save()

    @staticmethod
    def create_req_task(task_ids, req_id):
        result = list()
        exist_task = RequirementTaskMap.objects.get_tasks(req_id)
        exist_task_ids = [task.TaskID for task in exist_task]
        for task_id in task_ids:
            if int(task_id) not in exist_task_ids:
                temp = RequirementTaskMap()
                temp.TaskType = 1
                temp.TaskID = int(task_id)
                temp.RequirementID = int(req_id)
                temp.save()
                result.append(temp)
        return result

    @staticmethod
    def log_create_activity(user, target):
        Requirement.objects.log_action(user.id, target.id, target.Title, ADDITION, "创建了新需求", target.ProjectID)

    @staticmethod
    def log_delete_activity(user, target):
        Requirement.objects.log_action(user.id, target.id, target.Title, ADDITION, "删除了需求", target.ProjectID)

    @staticmethod
    def log_change_activity(user, target):
        Requirement.objects.log_action(user.id, target.id, target.Title, ADDITION, "修改了需求", target.ProjectID)
