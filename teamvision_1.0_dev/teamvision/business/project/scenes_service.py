# coding=utf-8
"""
@Create: 2024/12/16 17:36
@Author: zhangpeng
"""

# coding=utf-8
'''
Created on 2015-10-23

@author: zhang<PERSON><PERSON>
'''
from teamvision.project.models import Tag, ProjectScenesInfo
from gatesidelib.common.simplelogger import Simple<PERSON>ogger
from django.contrib.admin.models import DELETION, CHANGE, ADDITION
from gatesidelib.color_helper import ColorHelper
from django.core.cache import cache


class ScenesService(object):
    """
        ScenesService
    """

    @staticmethod
    def set_scenes_type_cache(scenes_dict_cache_key):
        scenes_list = ProjectScenesInfo.objects.values_list('scenes_id', 'scenes_name')
        scenes_dict = {}
        for s_id, name in scenes_list:
            scenes_dict[s_id] = name

        cache.set(scenes_dict_cache_key, scenes_dict, timeout=120)

    @staticmethod
    def get_scenes_name(scenes_id):
        """
        :param scenes_id: 6: 测试用例优先级
        :return: tag_name, tag_color
        """
        scenes_dict_cache_key = "scenes_dict_type_" + str(scenes_id)
        scenes_dict = cache.get(scenes_dict_cache_key)
        if scenes_dict is None:
            ScenesService.set_scenes_type_cache(scenes_dict_cache_key)
            scenes_dict = cache.get(scenes_dict_cache_key)
        try:
            scenes_name = scenes_dict[scenes_id]
        except:
            scenes_name = "--"
        return scenes_name
