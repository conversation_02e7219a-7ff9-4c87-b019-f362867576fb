# coding=utf-8
'''
Created on 2015-10-23

@author: z<PERSON><PERSON><PERSON>
'''
from teamvision.project.models import Tag
from gatesidelib.common.simplelogger import SimpleLogger
from django.contrib.admin.models import DELETION, CHANGE, ADDITION
from gatesidelib.color_helper import ColorHelper
from django.core.cache import cache


class TagService(object):
    '''
    classdocs
    '''

    @staticmethod
    def create_tag(tag_name, tag_type, owner_id):
        try:
            dm_tag = Tag()
            dm_tag.TagName = tag_name
            dm_tag.TagOwner = owner_id
            dm_tag.TagProjectID = 0
            dm_tag.TagVisableLevel = 1
            dm_tag.TagType = int(tag_type)
            dm_tag.TagColor = ColorHelper.random_color()
            dm_tag.save()
        except Exception as ex:
            print(ex)
            SimpleLogger.error(ex)

    @staticmethod
    def edit_tag(tag_name, tag_id):
        try:
            dm_tag = Tag.objects.get(tag_id)
            dm_tag.TagName = tag_name
            dm_tag.save()
        except Exception as ex:
            print(ex)
            SimpleLogger.error(ex)

    @staticmethod
    def set_tag_type_cache(tag_dict_type_cache_key, type_value):
        tag_type_list = Tag.objects.filter(TagType=type_value).values_list('TagValue', 'TagName', 'TagColor')
        tag_type_dict = {}
        for value, name, color in tag_type_list:
            tmp_dict = {
                "name": name,
                "color": color
            }
            tag_type_dict[value] = tmp_dict

        cache.set(tag_dict_type_cache_key, tag_type_dict, timeout=120)

    @staticmethod
    def get_tag_name(tag_type, value):
        """
        :param tag_type: 6: 测试用例优先级
        :param value:
        :return: tag_name, tag_color
        """

        tag_dict_type_cache_key = "tag_dict_type_" + str(tag_type)
        tag_type_dict = cache.get(tag_dict_type_cache_key)
        if tag_type_dict is None:
            TagService.set_tag_type_cache(tag_dict_type_cache_key, tag_type)
            tag_type_dict = cache.get(tag_dict_type_cache_key)

        tag_name = "--"
        tag_color = "--"

        try:
            if value is not None:
                tag_name = tag_type_dict[value]["name"]
                tag_color = tag_type_dict[value]["color"]
        except Exception as e:
            SimpleLogger.exception(e)

        return tag_name, tag_color
