# coding=utf-8
"""
Created on 2015-11-17
@author: Dev<PERSON>
"""
import datetime, json
from django.conf import settings
from business.common.emailservice import EmailService
from business.common.file_info_service import FileInfoService
from gatesidelib.common.simplelogger import SimpleLogger
from business.project.memeber_service import MemberService
from gatesidelib.emailhelper import EmailHelper
from teamvision.api.project.serializer.project_testplan_serializer import ProjectTestPlanCaseListSerializer
from teamvision.api.project.serializer import project_serializer, project_testreport_serializer
from teamvision.home.models import FileInfo
from teamvision.project import models
from teamvision.settings import EMAIL_TEMPLATES
from django.db.models import Q
from business.utils import utils
from business.common.mongodb_service import MongoDBService
from business.business_service import BusinessService
from teamvision.project.mongo_models import TestReportMongoFile, TempFileMongoFile
import base64
from business.utils.testreport_pdf import TestReportPDF
from django.utils import timezone


class TestReportService(BusinessService):
    """
    TestReportService
    """

    @staticmethod
    def create_report_plan(test_report, plan_ids):
        for plan_id in plan_ids:
            report_plan = models.ProjectTestReportTestPlan()
            report_plan.TestPlan = plan_id
            report_plan.Report = test_report.id
            report_plan.save()
        TestReportService.create_report_reqirements_for_plans(test_report, plan_ids)
        TestReportService.report_case_statistics(test_report)
        TestReportService.report_issue_statistics(test_report)

    @staticmethod
    def create_report_reqirements_for_plans(test_report, plan_ids):
        fortestings = models.ProjectTestPlanForTesting.objects.filter(TestPlan__in=plan_ids).values_list("Fortesting", flat=True)
        reqirement_id_list = models.RequirementTaskMap.objects.filter(TaskID__in=fortestings).values_list("RequirementID", flat=True)
        reqirements = models.Requirement.objects.filter(id__in=reqirement_id_list)
        for req in reqirements:
            report_req = models.ProjectTestReportRequirements()
            report_req.Report = test_report.id
            report_req.Requirement = req.id
            report_req.TestPlan = 0
            report_req.Status = 6
            report_req.Title = req.Title
            report_req.save()

    @staticmethod
    def create_report_reqirements(test_report, plan_ids):
        fortestings = models.ProjectTestPlanForTesting.objects.filter(TestPlan__in=plan_ids).values_list("Fortesting", flat=True)
        reqirement_id_list = models.RequirementTaskMap.objects.filter(TaskID__in=fortestings).values_list("RequirementID", flat=True)
        reqirements = models.Requirement.objects.filter(id__in=reqirement_id_list)
        for req in reqirements:
            report_req = models.ProjectTestReportRequirements()
            report_req.Report = test_report.id
            report_req.Requirement = req.id
            report_req.TestPlan = 0
            report_req.Status = 6
            report_req.Title = req.Title
            report_req.save()

    @staticmethod
    def copy_report(test_report):
        new_report = test_report
        new_report.CopyID = test_report.CopyID
        new_report.id = None
        new_report.CaseStatistics = None
        new_report.IssueStatistics = None
        new_report.save()
        return new_report

    @staticmethod
    def init_model_instance(model_instance, model_fields, form_data):
        for field in model_fields:
            if field in form_data.keys():
                value = form_data.get(field)
                model_instance.__setattr__(field, value)
        return model_instance

    @staticmethod
    def report_case_statistics(test_report):
        statistics = {}
        plan_ids = models.ProjectTestReportTestPlan.objects.filter(Report=test_report.id).values_list('TestPlan', flat=True)
        test_plans_cases = models.ProjectTestPlanCase.objects.filter(TestPlan__in=plan_ids).filter(IsGroup=0).filter(IsChecked=0)
        statistics["case_count"] = test_plans_cases.count()
        statistics["pass"] = test_plans_cases.filter(TestResult=1).count()
        try:
            statistics["pass_rate"] = round((statistics["pass"] / statistics["case_count"]) * 100, 2)
        except ZeroDivisionError:
            statistics["pass_rate"] = 0
        statistics["fail"] = test_plans_cases.filter(TestResult=0).count()
        try:
            statistics["fail_rate"] = round((statistics["fail"] / statistics["case_count"]) * 100, 2)
        except ZeroDivisionError:
            statistics["fail_rate"] = 0
        case_exec_count = test_plans_cases.filter(TestResult__in=[1, 2, 3, 4]).count()
        try:
            statistics["case_exec_rate"] = round((case_exec_count / statistics["case_count"]) * 100, 2)
        except ZeroDivisionError:
            statistics["case_exec_rate"] = 0
        statistics["no_exec"] = test_plans_cases.filter(TestResult=0).count()
        statistics["blocked"] = test_plans_cases.filter(TestResult=2).count()
        statistics["re_exec"] = test_plans_cases.filter(TestResult=3).count()

        if test_report.CaseStatistics is None or test_report.CaseStatistics == "":
            test_report.CaseStatistics = json.dumps(statistics)
            test_report.save()
        else:
            eval(test_report.CaseStatistics)

    @staticmethod
    def report_issue_statistics(test_report):
        statistics = {}
        requirements = models.ProjectTestReportRequirements.objects.filter(Report=test_report.id).values_list('Requirement', flat=True)
        issues = models.ProjectIssue.objects.filter(Requirement__in=requirements)
        statistics['issue_count'] = issues.count()
        statistics['fixed_count'] = issues.filter(Status__in=[3, 4]).count()
        statistics['closed_count'] = issues.filter(Status=3).count()
        try:
            statistics['fixed_rate'] = round(((statistics['fixed_count'] / statistics['issue_count']) * 100), 2)
        except ZeroDivisionError:
            statistics['fixed_rate'] = 100

        closed_time_sum = datetime.timedelta(0)
        resolved_time_sum = datetime.timedelta(0)
        statistics['reopen_count'] = 0

        for issue in issues:
            if issue.ClosedTime is None:
                now = timezone.now()
                closed_time = now - issue.CreationTime
            else:
                closed_time = issue.ClosedTime - issue.CreationTime
            closed_time_sum = closed_time_sum + closed_time

            if issue.ResolvedTime is None:
                now = timezone.now()
                fixed_time = now - issue.CreationTime
            else:
                fixed_time = issue.ClosedTime - issue.CreationTime
            resolved_time_sum = resolved_time_sum + fixed_time

            statistics['reopen_count'] = statistics['reopen_count'] + issue.ReopenCounts

        try:
            statistics['closed_average_time'] = round(((closed_time_sum / statistics['issue_count']).total_seconds() / 3600), 2)
        except ZeroDivisionError:
            statistics['closed_average_time'] = 0
        try:
            statistics['fixed_average_time'] = round(((resolved_time_sum / statistics['issue_count']).total_seconds() / 3600), 2)
        except ZeroDivisionError:
            statistics['fixed_average_time'] = 0
        try:
            statistics['reopen_rate'] = round((statistics['reopen_count'] / statistics['issue_count']) * 100, 2)
        except ZeroDivisionError:
            statistics['reopen_rate'] = 0

        if test_report.IssueStatistics is None or test_report.IssueStatistics == "":
            test_report.IssueStatistics = json.dumps(statistics)
            test_report.save()
        else:
            eval(test_report.IssueStatistics)

    @staticmethod
    def get_test_report_requirements(report_id):
        report_requirements = models.ProjectTestReportRequirements.objects.get_report_reqs(report_id)
        return report_requirements

    @staticmethod
    def get_test_report_test_plans(report_id):
        report_plans = models.ProjectTestReportTestPlan.objects.get_report_plans(report_id)
        plans = models.ProjectTestPlan.objects.filter(id__in=report_plans)
        return plans

    @staticmethod
    def get_test_report_test_cases(report_id):
        testplan_ids = models.ProjectTestReportTestPlan.objects.get_report_plans(report_id).values_list('TestPlan', flat=True)
        testplan_cases = models.ProjectTestPlanCase.objects.filter(TestPlan__in=testplan_ids).filter(IsGroup=0)
        return testplan_cases

    @staticmethod
    def get_test_report_issues(report_id):
        requirement_ids = models.ProjectTestReportRequirements.objects.get_report_reqs(report_id).values_list('id', flat=True)
        test_report = models.ProjectTestReport.objects.get(report_id)
        if test_report.Version:
            issues = models.ProjectIssue.objects.filter(Q(Requirement__in=requirement_ids) | Q(Version=test_report.Version))
        else:
            issues = models.ProjectIssue.objects.filter(Requirement__in=requirement_ids)
        return issues

    @staticmethod
    def create_email_message(test_report):
        email_path = EMAIL_TEMPLATES["TestReport"]
        # email_templates = open(email_path, 'rb', encoding='utf-8').read().decode()
        with open(email_path, 'r', encoding='utf-8') as f:
            email_templates = f.read()
        email_templates = email_templates.replace("${TestReport}", test_report['Title'])
        email_templates = email_templates.replace("${CreateName}", test_report['view_data']['creator_name'])
        email_templates = email_templates.replace("${CreateTime}", str(test_report['CreationTime']))
        email_templates = email_templates.replace("${ProjectName}", test_report['view_data']['project_title'])

        host = settings.SITE_HOST
        test_report_url = "http://" + host + "/project/" + str(test_report['Project']) + "/test/report/" + str(test_report['id'])
        email_templates = email_templates.replace("${URL}", test_report_url)

        if test_report['Summary'] is not None:
            email_templates = email_templates.replace("${Summary}", test_report['Summary'])
        else:
            email_templates = email_templates.replace("${Summary}", "暂无")
        email_templates = email_templates.replace("${RequirementCount}", str(test_report['view_data']['requirement_count']))
        case_statistics = json.loads(test_report['CaseStatistics'])
        email_templates = email_templates.replace("${CaseCount}", str(case_statistics['case_count']))
        email_templates = email_templates.replace("${CaseExecRate}", str(case_statistics['case_exec_rate']))
        email_templates = email_templates.replace("${CasePassRate}", str(case_statistics['pass_rate']))
        issue_statistics = json.loads(test_report['IssueStatistics'])
        email_templates = email_templates.replace("${IssueCount}", str(issue_statistics['issue_count']))
        email_templates = email_templates.replace("${IssueFixedRate}", str(issue_statistics['fixed_rate']))
        email_templates = email_templates.replace("${IssueReopenRate}", str(issue_statistics['reopen_rate']))
        email_templates = email_templates.replace("${IssueCloseAvgTime}", str(issue_statistics['closed_average_time']))
        email_templates = email_templates.replace("${IssueFixedAvgTime}", str(issue_statistics['fixed_average_time']))

        test_plan_list = ""
        for test_plan in test_report['view_data']['test_plans']:
            plan = "<tr><th>#{ID} {PLAN_TITLE}</th><th>{START_TIME}-{FINISH_TIME}</th><th>用例数:{CASE_COUNT}</th><th>{OWNER}</th></tr>"
            plan = plan.replace("{ID}", str(test_plan['id']))
            plan = plan.replace("{PLAN_TITLE}", test_plan['Title'])
            plan = plan.replace("{START_TIME}", str(test_plan['StartTime']))
            plan = plan.replace("{FINISH_TIME}", str(test_plan['FinishTime']))
            plan = plan.replace("{CASE_COUNT}", str(test_plan['CaseCount']))
            plan = plan.replace("{OWNER}", str(test_plan['Owner']))
            test_plan_list = test_plan_list + plan
            email_templates = email_templates.replace("${TestPlanList}", test_plan_list)

        dev_list = []
        for for_testing in test_report['view_data']['fortesting']:
            dev = for_testing['ViewData']['creator_name']
            if dev not in dev_list:
                dev_list.append(dev)

        dev_list_str = ','.join(dev_list)
        if dev_list_str == "":
            email_templates = email_templates.replace("${DEVList}", "无")
        else:
            email_templates = email_templates.replace("${DEVList}", dev_list_str)

        requirement_list = ""
        for report_req in test_report['view_data']['requirements']:
            requirement = "<tr><th>#{ID} {REQUIREMENT_TITLE}</th></tr>"
            requirement = requirement.replace("{ID}", str(report_req['Requirement']))
            requirement = requirement.replace("{REQUIREMENT_TITLE}", report_req['Title'])
            requirement_list = requirement_list + requirement
        if requirement_list != "":
            email_templates = email_templates.replace("${RequirementList}", requirement_list)
        else:
            email_templates = email_templates.replace("${RequirementList}", "无")

        issue_list = ""
        test_report_issues = TestReportService.get_test_report_issues(test_report['id'])
        if test_report_issues is not None:
            test_report_issues_ser = project_serializer.ProjectIssueListReadOnlySerializer(test_report_issues, many=True)
            for report_issue in test_report_issues_ser.data:
                issue = "<tr><th>#{ID} {ISSUE_TITLE}</th><th>{STATUS_NAME}</th></tr>"
                issue = issue.replace("{ID}", str(report_issue['id']))
                issue = issue.replace("{ISSUE_TITLE}", report_issue['Title'])
                issue = issue.replace("{STATUS_NAME}", report_issue['status_name']['Name'])
                issue_list = issue_list + issue
            email_templates = email_templates.replace("${IssueList}", issue_list)
        else:
            email_templates = email_templates.replace("${IssueList}", "暂无")

        test_report_cases = TestReportService.get_test_report_test_cases(test_report['id'])
        if test_report_cases is not None:
            test_report_cases_ser = ProjectTestPlanCaseListSerializer(test_report_cases, many=True)
            test_report_case_list = ""
            tester_list = []
            for report_case in test_report_cases_ser.data:
                test_case = "<tr><th>#{ID} {CASE_TITLE}</th><th>{RESULT}</th><th>{OWNER_NAME}</th></tr>"
                test_case = test_case.replace('{ID}', str(report_case['id']))
                test_case = test_case.replace('{CASE_TITLE}', report_case['title'])
                test_case = test_case.replace('{RESULT}', report_case['result'])
                test_case = test_case.replace('{OWNER_NAME}', report_case['owner_name'])
                test_report_case_list = test_report_case_list + test_case
                if report_case['owner_name'] not in tester_list:
                    tester_list.append(report_case['owner_name'])
            if test_report_case_list != "":
                email_templates = email_templates.replace("${TestCaseList}", test_report_case_list)
                tester_list_str = ','.join(tester_list)
                email_templates = email_templates.replace("${TesterList}", tester_list_str)
            else:
                email_templates = email_templates.replace("${TestCaseList}", "无")
                email_templates = email_templates.replace("${TesterList}", "无")
        else:
            email_templates = email_templates.replace("${TestCaseList}", "无")

        sql = project_testreport_serializer.ProjectTestReportMemberListSerializer.QueryInfo.get_sql(test_report['id'])
        model = project_testreport_serializer.ProjectTestReportMemberListSerializer.QueryInfo.model
        query_set = model.objects.raw(sql, [test_report['id']])

        for obj in query_set:
            pm_list = obj.PM
            if pm_list is None:
                email_templates = email_templates.replace("${PMList}", "无")
            else:
                email_templates = email_templates.replace("${PMList}", obj.PM)

        email_templates = email_templates.replace("${PerformanceTest}", test_report['Performance'])
        return email_templates

    @staticmethod
    def create_image_email_message(test_report):
        email_temp__path = EMAIL_TEMPLATES["TestReportImage"]
        email_templates = open(email_temp__path, 'rb').read().decode()
        return email_templates

    @staticmethod
    def send_test_report_email(test_report, image_report=None):
        project_member_email_list = MemberService.get_project_member_emails(test_report['Project'])
        subject = "[测试报告]" + test_report['Title']

        if image_report is None:
            email_message = TestReportService.create_email_message(test_report)
            case_statistics = json.loads(test_report['CaseStatistics'])
            case_statistics_pie_size = [case_statistics['pass'], case_statistics['fail']]
            case_statistics_pie_size_labels = ['通过', '失败']
            img1 = utils.save_pie_graph_image("测试用例结果分布", case_statistics_pie_size_labels, case_statistics_pie_size)

            issue_statistics = json.loads(test_report['IssueStatistics'])
            issue_statistics_pie_size = [
                issue_statistics['fixed_count'], issue_statistics['reopen_count'], issue_statistics['closed_count']
            ]
            issue_statistics_pie_size_labels = ['修复', '重新打开', '关闭']
            img2 = utils.save_pie_graph_image("问题状态分布", issue_statistics_pie_size_labels, issue_statistics_pie_size)

            report_pdf = utils.html_to_pdf(email_message)

            # test_report_pdf = TestReportPDF(test_report, "test_report")
            # report_pdf = test_report_pdf.create()

            attachments = [
                {'name': subject + '.pdf', 'path': report_pdf}
            ]
            for file in test_report['attachments_detail']:
                result = FileInfoService.get_file(file['id'], TestReportMongoFile)
                tmp_att_file_path = utils.write_temp_file(result.read(), None, False)
                tmp_att_file = {'name': file['FileName'], 'path': tmp_att_file_path}
                attachments.append(tmp_att_file)

            EmailService.send_email_multi(subject, email_message, project_member_email_list,
                                          image_list=[{'id': 'image1', 'path': img1},
                                                      {'id': 'image2', 'path': img2}],
                                          file_list=attachments)

        else:
            img_base64 = image_report.split('base64,')[1]
            img_data = base64.b64decode(img_base64)
            tmp_img_file = utils.write_temp_file(img_data, '.png', False)
            email_message = TestReportService.create_image_email_message(test_report)
            attachments = [
                {'name': subject + '.png', 'path': tmp_img_file}
            ]

            for file in test_report['attachments_detail']:
                result = FileInfoService.get_file(file['id'], TestReportMongoFile)
                tmp_att_file_path = utils.write_temp_file(result.read(), None, False)
                tmp_att_file = {'name': file['FileName'], 'path': tmp_att_file_path}
                attachments.append(tmp_att_file)

            EmailService.send_email_multi(subject, email_message, project_member_email_list,
                                          image_list=[{'id': 'image1', 'path': tmp_img_file}],
                                          file_list=attachments)

    @staticmethod
    def add_attachments(report_id, file):
        message = []
        mongo_message = TestReportService.save_to_mongo(file, TestReportMongoFile)
        if mongo_message[0] != "0":
            file_id = FileInfoService.add_file(0, mongo_message[0], file.name, 1, 0, file.size)
            message.append(file_id)
            message.append(mongo_message[0])
            test_report = models.ProjectTestReport.objects.get(int(report_id))
            if test_report:
                if test_report.Attachments != "":
                    test_report.Attachments = str(test_report.Attachments) + str(file_id) + ','
                else:
                    test_report.Attachments = str(file_id) + ','
                test_report.save()
        else:
            message[0] = "0"
            message[1] = "长传文件失败，" + mongo_message[1]

        return message
