import os
import shutil
import zipfile
import rarfile


def extract(d_path, f_path, mode="zip"):
    """
    zip解压缩乱码问题处理
    :param d_path:
    :param f_path:
    :return:
    """
    root = d_path
    if not os.path.exists(root):
        os.makedirs(root)

    if mode == 'zip':
        zf = zipfile.ZipFile(f_path, "r")
    elif mode == 'rar':
        zf = rarfile.RarFile(f_path, "r")

    for n in zf.infolist():
        src_name = n.filename
        try:
            decode_name = src_name.encode("cp437").decode("utf-8")
        except:
            try:
                decode_name = src_name.encode("cp437").decode("gbk")
            except:
                decode_name = src_name
        spilt_arr = decode_name.split("/")
        path = root
        for temp in spilt_arr:
            path = os.path.join(path, temp)

        if decode_name.endswith("/"):
            if not os.path.exists(path):
                os.makedirs(path)
        else:
            if not os.path.exists(os.path.dirname(path)):
                os.makedirs(os.path.dirname(path))
            f = open(path, "wb")
            f.write(zf.read(src_name))
            f.close()
    zf.close()


def xmind8_2_xmind2020(f_path):
    """
    xmind8 可以打开　xmind2020 报错
    main_fest.xml(xmind8 打开另存后 更改后缀为.zip  里边包含META-INF/manifest.xml)
    xmind 修改后缀为zip ----》解压---- 》放入main_fest.xml  --- 》压缩zip  修改后缀为xmind**
    """
    # 修改名字
    retval = os.path.dirname(os.path.abspath(__file__))
    folder = os.path.dirname(f_path)
    name = os.path.basename(f_path)
    unzip_folder = os.path.splitext(name)[0]
    zip_name = unzip_folder + ".zip"
    os.chdir(folder)
    os.rename(name, zip_name)
    os.chdir(retval)

    # 解压
    unzip_path = os.path.join(folder, unzip_folder)
    if not os.path.exists(unzip_path):
        os.mkdir(unzip_path)

    inf_folder = os.path.join(unzip_path, "META-INF")
    if not os.path.exists(inf_folder):
        os.mkdir(inf_folder)

    extract(unzip_path, os.path.join(folder, zip_name))

    file_content = """<?xml version="1.0" encoding="UTF-8" standalone="no"?><manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0" password-hint=""></manifest>"""

    # shutil.copyfile("./META-INF/manifest.xml", os.path.join(inf_folder, "manifest.xml"))

    with open(inf_folder + '/manifest.xml', 'w') as tmp:
        tmp.write(file_content)

    os.remove(os.path.join(folder, zip_name))
    shutil.make_archive(unzip_path, 'zip', unzip_path)
    file_path = unzip_path + '.zip'

    os.chdir(os.path.dirname(file_path))
    os.rename(os.path.basename(file_path), name)
    os.chdir(retval)
    shutil.rmtree(unzip_path)
    # print(file_path)


if __name__ == '__main__':
    data = {}
    path = '/Users/<USER>/Downloads/mind/341.xmind'
    xmind8_2_xmind2020(path)
