# coding=utf-8
"""
@Create: 2025/1/13 17:33
@Author: zhangpeng
"""
from reportlab.pdfbase import pdfmetrics  # 注册字体
from reportlab.pdfbase.ttfonts import TTFont  # 字体类
from reportlab.platypus import Table, SimpleDocTemplate, Paragraph, Image, Spacer  # 报告内容相关类
from reportlab.lib.pagesizes import letter  # 页面的标志尺寸(8.5*inch, 11*inch)
from reportlab.lib.styles import getSampleStyleSheet  # 文本样式
from reportlab.lib import colors  # 颜色模块
from reportlab.graphics.charts.barcharts import VerticalBarChart  # 图表类
from reportlab.graphics.charts.legends import Legend  # 图例类
from reportlab.graphics.shapes import Drawing  # 绘图工具
from reportlab.lib.units import cm  # 单位：cm
from reportlab.lib.pagesizes import A4

from business.utils.utils import write_temp_file
from teamvision.settings import WEB_HOST, BASE_DIR


class TestReportPDF:
    def __init__(self, test_report, file_name):
        self.test_report = test_report
        self.file_name = file_name
        self.pdf_content = []

    def draw_title(self, title, color=colors.black, title_style='Heading1', font_size=14):
        # 获取所有样式表
        style = getSampleStyleSheet()
        # 设置标题样式
        ct = style[title_style]
        # 单独设置样式相关属性
        ct.fontName = 'SimHei'
        ct.fontSize = font_size
        ct.leading = 50
        ct.textColor = color  # 字体颜色
        ct.alignment = 1  # 居中
        ct.bold = True
        # 创建标题对应的段落，并且返回
        return Paragraph(title, ct)

    def draw_text(self, text: str):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = 'SimHei'
        ct.fontSize = 15
        ct.wordWrap = 'CJK'
        ct.alignment = 0  # 左对齐
        ct.firstLineIndent = 24  # 第一行开头空格
        ct.leading = 25
        return Paragraph(text, ct)

    def draw_img(self, path, width=10 * cm, height=5 * cm):
        img = Image(path)  # 读取指定路径下的图片
        img.drawWidth = width  # 设置图片的宽度
        img.drawHeight = height  # 设置图片的高度
        img.hAlign = 'CENTER'  # 设置图片的水平居中
        return img

    def draw_table(*args):
        col_width = 120  # 列宽度
        style = [
            ('FONTNAME', (0, 0), (-1, -1), '宋体'),  # 字体
            ('FONTSIZE', (0, 0), (-1, 0), 12),  # 第一行的字体大小
            ('FONTSIZE', (0, 1), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),  # 设置第一行背景颜色
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # 第一行水平居中
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),  # 第二行到最后一行左右左对齐
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # 所有表格上下居中对齐
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),  # 设置表格框线为grey色，线宽为0.5
        ]
        table = Table(args, colWidths=col_width, style=style)
        return table

    def myFirstPage(canvas, doc):
        """
        第一页页眉页脚配置
        :param canvas: 绘画对象，用来写入页眉页脚
        :param doc: 文档对象，可以以此得知文档的页边距信息
        :return:
        """
        # 文档宽度高度(width、height)，左右页边距(leftMargin、rightMargin)、上下页边距(topMargin、bottomMargin)
        totalPageHeight = doc.bottomMargin + doc.height + doc.topMargin  # 页面总高度
        totalPageWidth = doc.leftMargin + doc.width + doc.rightMargin  # 页面总宽度

        # 保存之前的画笔格式等状态,并设置新的状态
        canvas.saveState()
        # 设置字体及大小
        canvas.setFont('宋体', 12)
        # 添置靠左页眉
        canvas.drawImage(r"D:\logo.png", doc.leftMargin, totalPageHeight - doc.topMargin, 5.4 * cm, 1.38 * cm)
        """
        # 如果想要在页眉页脚以段落的形式配置logo图片，效果同上<<添置靠左页眉>>
        p = Paragraph("<img src='%s' width='%d' height='%d'/>" % (r"D:\logo.png", 5.4 * cm, 1.38 * cm), getSampleStyleSheet()['Normal'])  # 使用一个Paragraph Flowable存放图片
        # 配置段落可用的宽度高度
        w, h = p.wrap(1*cm, 1*cm)
        # 图片写入画布区,位置(x, y)
        p.drawOn(canvas, doc.leftMargin, totalPageHeight - doc.topMargin)
        """
        # 添置靠右页眉
        canvas.drawRightString(totalPageWidth - doc.rightMargin, totalPageHeight - doc.topMargin, "XXXX")
        # 添置中横线
        canvas.setLineWidth(1)
        canvas.line(doc.leftMargin, totalPageHeight - doc.topMargin - 0.25 * cm, totalPageWidth - doc.rightMargin,
                    totalPageHeight - doc.topMargin - 0.25 * cm)
        # 变更字体大小
        canvas.setFont('宋体', 9)
        # 添置靠左页脚,一般来说，都是左边距1*inch，下边距0.75*inch
        canvas.drawString(doc.leftMargin, doc.bottomMargin, str())
        # canvas.drawString(inch, 0.75 * inch, str(datetime.date.today()))

        # 添置靠右页脚
        canvas.drawRightString(totalPageWidth - doc.rightMargin, doc.bottomMargin, "Confidential and Protected by Copyright Laws")
        # 添置居中页脚
        canvas.drawString(totalPageWidth / 2.0, doc.bottomMargin, "1")

        # 将画笔格式等状态还原
        canvas.restoreState()

    def create(self):
        styles = getSampleStyleSheet()
        body_style = styles['BodyText']
        pdfmetrics.registerFont(TTFont('SimHei', BASE_DIR + '/fonts/SimHei.ttf'))
        self.pdf_content.append(self.draw_title("[测试报告]" + self.test_report['Title']))
        create_title = self.test_report['view_data']['creator_name'] + "创建于" + self.test_report['CreationTime']
        self.pdf_content.append(self.draw_title(create_title, color=colors.darkslategray, title_style='Heading2', font_size=10))
        self.pdf_content.append(Spacer(1, 12))
        self.pdf_content.append(self.draw_text("项目名称: " + self.test_report['view_data']['project_title']))
        developer = Paragraph(self.draw_text("项目人员: " + self.test_report['view_data']['project_title']))
        self.pdf_content.append(developer)
        self.pdf_content.append(Spacer(1, 12))
        tester = Paragraph("测试人员: 李四", body_style)
        self.pdf_content.append(tester)
        self.pdf_content.append(Spacer(1, 12))
        # 添加段落文字
        self.pdf_content.append(self.draw_text('你好！'))
        # 添加表格
        # data = [
        #     ('编程语言', '排名', '较上年增长'),
        #     ('Python', '1', '2.7%'),
        #     ('C++', '2', '-0.4%'),
        #     ('Java', '3', '-2.1%')
        # ]
        # self.pdf_content.append(self.draw_table(*data))
        self.pdf_content.append(self.draw_text(' '))
        # 添加图片
        # self.pdf_content.append(self.draw_img('1.png'))
        # 生成pdf文件
        output_filename = write_temp_file(file_suffix=".pdf", is_del=False)
        result_file = open(output_filename, "w+b")
        doc = SimpleDocTemplate(result_file, pagesize=A4, )
        doc.build(self.pdf_content)
        return output_filename
