import os, io
from tempfile import NamedTemporaryFile, TemporaryDirectory
import plotly.graph_objects as go
import plotly.io as pio
from xhtml2pdf import pisa
import plotly


def write_temp_file(file_content=None, file_suffix=None, is_del=True):
    with NamedTemporaryFile(mode='w+b', suffix=file_suffix, delete=is_del) as temp_file:
        # Encode your text in order to write bytes
        if file_content is not None:
            temp_file.write(file_content)
            # put file buffer to offset=0
            temp_file.seek(0)
        return temp_file.name


def save_pie_graph_image(image_name, name_list, number_list):
    pyplt = plotly.offline.plot
    trace = [go.Pie(labels=name_list, values=number_list)]
    layout = go.Layout(
        title=image_name
    )
    fig = go.Figure(data=trace, layout=layout)

    try:
        with TemporaryDirectory() as tmp_dirname:
            filename = os.path.join(tmp_dirname, "{}.png".format(image_name))
            pio.write_image(fig, file=filename, format="png", scale=0.5)
            with open(filename, 'rb') as tmp:
                image_file_io = io.BytesIO(tmp.read())
                return image_file_io
    except Exception as e:
        print(e)


def html_to_pdf(html_content):
    output_filename = write_temp_file(file_suffix=".pdf", is_del=False)
    result_file = open(output_filename, "w+b")
    pisa_status = pisa.CreatePDF(html_content, dest=result_file, encoding='utf-8', )
    result_file.close()
    return output_filename
