#!/bin/bash

# TeamVision AI功能部署脚本
# 用于快速部署和配置AI测试助手功能

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查Python包是否安装
check_python_package() {
    if ! python -c "import $1" &> /dev/null; then
        log_warning "Python包 $1 未安装"
        return 1
    fi
    return 0
}

# 主函数
main() {
    echo "🚀 TeamVision AI功能部署脚本"
    echo "=================================="
    
    # 检查基本环境
    log_info "检查基本环境..."
    check_command "python"
    check_command "pip"
    
    # 检查Python版本
    PYTHON_VERSION=$(python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    log_info "Python版本: $PYTHON_VERSION"
    
    if [[ $(echo "$PYTHON_VERSION >= 3.8" | bc -l) -eq 0 ]]; then
        log_error "需要Python 3.8或更高版本"
        exit 1
    fi
    
    # 检查Django项目
    if [ ! -f "manage.py" ]; then
        log_error "未找到Django项目，请在项目根目录运行此脚本"
        exit 1
    fi
    
    log_success "基本环境检查通过"
    
    # 安装Python依赖
    log_info "安装Python依赖..."
    
    # 检查并安装必需的包
    REQUIRED_PACKAGES=(
        "requests"
        "openai" 
        "httpx"
        "pydantic"
        "xlwt"
    )
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if ! check_python_package "$package"; then
            log_info "安装 $package..."
            pip install "$package"
        else
            log_success "$package 已安装"
        fi
    done
    
    # 检查Django设置
    log_info "检查Django设置..."
    
    # 检查AI应用是否在INSTALLED_APPS中
    if ! python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
import django
django.setup()
from django.conf import settings
if 'teamvision.ai' not in settings.INSTALLED_APPS:
    exit(1)
" 2>/dev/null; then
        log_warning "AI应用未在INSTALLED_APPS中，请手动添加 'teamvision.ai'"
    else
        log_success "AI应用配置正确"
    fi
    
    # 数据库迁移
    log_info "执行数据库迁移..."
    
    # 创建AI应用的迁移文件
    if python manage.py makemigrations ai --dry-run | grep -q "No changes detected"; then
        log_info "AI模型无变更"
    else
        log_info "创建AI模型迁移..."
        python manage.py makemigrations ai
    fi
    
    # 执行迁移
    log_info "应用数据库迁移..."
    python manage.py migrate
    
    log_success "数据库迁移完成"
    
    # 初始化AI数据
    log_info "初始化AI数据..."
    
    if python manage.py init_ai_data --create-default-config --test-ragflow-connection; then
        log_success "AI数据初始化完成"
    else
        log_warning "AI数据初始化部分失败，请检查RAGFlow配置"
    fi
    
    # 检查RAGFlow配置
    log_info "检查RAGFlow配置..."
    
    # 从环境变量或设置中获取RAGFlow配置
    RAGFLOW_URL=$(python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
import django
django.setup()
from django.conf import settings
print(getattr(settings, 'RAGFLOW_BASE_URL', 'NOT_SET'))
" 2>/dev/null)
    
    if [ "$RAGFLOW_URL" = "NOT_SET" ]; then
        log_warning "RAGFlow URL未配置，请在settings.py或环境变量中设置:"
        echo "  RAGFLOW_BASE_URL=http://your-ragflow-server:9380"
        echo "  RAGFLOW_API_KEY=your_api_key_here"
        echo "  RAGFLOW_AGENT_ID=your_agent_id_here"
    else
        log_success "RAGFlow URL已配置: $RAGFLOW_URL"
    fi
    
    # 运行集成测试
    log_info "运行集成测试..."
    
    if [ -f "test_ai_integration.py" ]; then
        if python test_ai_integration.py; then
            log_success "集成测试通过"
        else
            log_warning "集成测试部分失败，请检查详细输出"
        fi
    else
        log_warning "未找到集成测试文件"
    fi
    
    # 检查前端文件
    log_info "检查前端AI组件..."
    
    FRONTEND_DIR="../teamvision_fontend"
    AI_COMPONENTS=(
        "src/api/ai.js"
        "src/pages/project/project-testing/test-case/AIAssistant.vue"
        "src/components/ai/AITestCaseGenerator.vue"
        "src/components/ai/AIChatPanel.vue"
        "src/components/ai/AIWorkspace.vue"
    )
    
    if [ -d "$FRONTEND_DIR" ]; then
        for component in "${AI_COMPONENTS[@]}"; do
            if [ -f "$FRONTEND_DIR/$component" ]; then
                log_success "前端组件存在: $component"
            else
                log_warning "前端组件缺失: $component"
            fi
        done
    else
        log_warning "前端目录未找到: $FRONTEND_DIR"
    fi
    
    # 生成配置示例
    log_info "生成配置示例..."
    
    cat > ai_config_example.env << EOF
# RAGFlow配置示例
RAGFLOW_BASE_URL=http://localhost:9380
RAGFLOW_API_KEY=your_api_key_here
RAGFLOW_AGENT_ID=your_agent_id_here
RAGFLOW_TIMEOUT=30

# Redis配置 (用于缓存)
REDIS_URL=redis://localhost:6379/1

# AI功能开关
AI_FEATURES_ENABLED=true
AI_GENERATION_LIMIT_PER_DAY=100
AI_MAX_CASES_PER_REQUEST=50

# 日志级别
AI_LOG_LEVEL=INFO
EOF
    
    log_success "配置示例已生成: ai_config_example.env"
    
    # 创建快速测试脚本
    cat > quick_ai_test.py << 'EOF'
#!/usr/bin/env python
# 快速AI功能测试脚本

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
django.setup()

def quick_test():
    print("🧪 快速AI功能测试")
    print("=" * 30)
    
    try:
        # 测试模型导入
        from teamvision.ai.models import AIGenerationSession, AIGeneratedTestCase
        print("✅ AI模型导入成功")
        
        # 测试数据库连接
        session_count = AIGenerationSession.objects.count()
        case_count = AIGeneratedTestCase.objects.count()
        print(f"✅ 数据库连接正常 (会话: {session_count}, 用例: {case_count})")
        
        # 测试RAGFlow客户端
        from teamvision.ai.ragflow_client import ragflow_client
        print("✅ RAGFlow客户端导入成功")
        
        # 测试健康检查
        from teamvision.ai.health_check import get_ai_health_status
        status = get_ai_health_status()
        print(f"✅ 健康检查完成: {status.get('overall', 'unknown')}")
        
        print("\n🎉 快速测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == '__main__':
    quick_test()
EOF
    
    chmod +x quick_ai_test.py
    log_success "快速测试脚本已创建: quick_ai_test.py"
    
    # 部署完成总结
    echo ""
    echo "🎉 AI功能部署完成！"
    echo "===================="
    
    log_info "下一步操作:"
    echo "1. 配置RAGFlow连接参数 (参考 ai_config_example.env)"
    echo "2. 重启Django服务器"
    echo "3. 运行快速测试: python quick_ai_test.py"
    echo "4. 在前端测试AI助手功能"
    
    log_info "有用的命令:"
    echo "• 健康检查: python manage.py shell -c \"from teamvision.ai.health_check import perform_ai_health_check; print(perform_ai_health_check())\""
    echo "• 查看AI统计: python manage.py shell -c \"from teamvision.ai.models import AIUsageStatistics; print(AIUsageStatistics.objects.count())\""
    echo "• 重新初始化: python manage.py init_ai_data --create-default-config"
    
    log_success "部署脚本执行完成！"
}

# 执行主函数
main "$@"
