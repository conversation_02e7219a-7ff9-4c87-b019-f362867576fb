[loggers]
keys=root,infologger,errorlogger
[logger_root]
level=INFO
handlers=<PERSON><PERSON><PERSON><PERSON>,FileInfoHandler

[logger_infologger]
level=INFO
handlers=<PERSON>Handler,FileInfoHandler
propagate=0
qualname=infologger


[logger_errorlogger]
level=ERROR
handlers=<PERSON><PERSON><PERSON><PERSON>,FileErrorHandler
propagate=0
qualname=errorlogger

###############################################
[handlers]
keys=<PERSON><PERSON><PERSON><PERSON>,<PERSON>ErrorHandler,FileInfoHandler

[handler_StreamHandler]
class=StreamHandler
level=INFO
formatter=simpleformatter
args=(sys.stderr,)

[handler_FileErrorHandler]
class=handlers.TimedRotatingFileHandler
level=ERROR
formatter=simpleformatter
args=('logs/error.log', 'D', 1, 5)

[handler_FileInfoHandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=simpleformatter
args=('logs/info.log','D', 1, 5)

###############################################
[formatters]
keys=simpleformatter
[formatter_simpleformatter]
format=%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s
datefmt=%a, %d %b %Y %H:%M:%S