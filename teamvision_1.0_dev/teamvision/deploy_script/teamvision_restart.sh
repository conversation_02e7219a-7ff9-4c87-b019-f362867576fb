#!/bin/bash
# dpreal

kill -9 $(pidof uwsgi)
kill -9 $(pidof uwsgi)
ps -ef|grep runapscheduler|grep -v grep|awk '{print $2}'|xargs kill -9
echo "kill sucess !"

cd ../
sleep 1
uwsgi --ini web_uwsgi.ini

sleep 1
uwsgi --ini websocket_uwsgi.ini

cd teamvision/
sleep 1
nohup python3 manage.py runapscheduler &

echo "restart success!"
sleep 3
ps -ef|grep uwsgi|grep -v grep
ps -ef|grep runapscheduler|grep -v grep

