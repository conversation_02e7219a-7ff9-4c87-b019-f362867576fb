# coding=utf-8
'''
Created on 2014-10-10

@author: Dev<PERSON>
'''

import smtplib
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.image import MIMEImage
from gatesidelib.common.simplelogger import SimpleLogger
import os


class EmailHelper(object):
    '''
    helper for send email
    '''

    def __init__(self, emailhost, postuser, password, postfix, port=25):
        self.host = emailhost
        self.user = postuser
        self.password = password
        self.mailpostfix = postfix
        self.port = port

    def sendemaillogin(self, to, subject, message):
        try:
            me = self.user + "<" + self.user + "@" + self.mailpostfix + ">"
            s = smtplib.SMTP()
            s.connect(host=self.host, port=self.port)
            s.login(self.user, self.password)
            s.sendmail(me, to, message)
            s.close()
            return True
        except Exception as e:
            SimpleLogger.error(str(e))
            return False

    def sendmail_nologin(self, to, subject, message):
        try:
            me = self.user + "<" + self.user + "@" + self.mailpostfix + ">"
            s = smtplib.SMTP()
            s.connect(host=self.host, port=self.port)
            s.sendmail(me, to, message)
            s.close()
            return True
        except Exception as e:
            SimpleLogger.error(str(e))
            return False

    def generatetextmessage(self, message, subject, to, emailformat):
        me = self.user + "<" + self.user + "@" + self.mailpostfix + ">"
        msg = MIMEText(message, emailformat, 'utf-8')
        msg['Subject'] = subject
        msg['From'] = me
        msg['To'] = to
        return msg

    def add_attachment(self, message, file_path, file_name=None):
        if file_name is None:
            file_name = os.path.dirname(file_path)

        try:
            with open(file_path, 'rb') as file_content:
                send_file = file_content.read()
                msg_attach = MIMEApplication(send_file, Name=file_name, )
                msg_attach["Content-Type"] = 'application/octet-stream'
                msg_attach.add_header('Content-Disposition', 'attachment', filename=file_name)
                message.attach(msg_attach)
        except Exception as es:
            print('未找到附件', es)

    def add_image(self, message, image_path, image_id=None):
        if image_id is None:
            image_id = os.path.basename(image_path)
        try:
            if isinstance(image_path, str):
                with open(image_path, 'rb') as image:
                    image_tmp = image.read()
            else:
                image_tmp = image_path.read()
            msg_image = MIMEImage(image_tmp)
            msg_image.add_header('Content-ID', image_id)
            message.attach(msg_image)
        except Exception as es:
            print('未找到附件', es)

    def send_email_multipart(self, subject, message, to, cc_address_list=None, image_list=None, file_list=None):
        """
        支持发送图片, 附件
        :subject: email 主题
        :message: email 内容
        :to:      接收人列表: ['<EMAIL>', '<EMAIL>']
        :image_list: [{'id':'image1','path':'/tmp/image1.png'}] 邮件内容中图片的路径
        :file_list:  [{'name':'file1','path':'/tmp/file1'}] 附件的路径
        """
        msg = MIMEMultipart('related')  # 采用related定义内嵌资源的邮件体

        me = self.user + "<" + self.user + "@" + self.mailpostfix + ">"
        msg['Subject'] = subject
        msg['From'] = me
        msg['To'] = ','.join(to)
        if cc_address_list is not None:
            msg['Cc'] = ",".join(cc_address_list)

        msg_text = MIMEText(message, _subtype='html', _charset='utf-8')
        msg.attach(msg_text)

        if image_list is not None:
            # print("image_list = = = = =", image_list)
            for img in image_list:
                self.add_image(msg, img['path'], img['id'])

        if file_list is not None:
            # print("file_list = = = = =", file_list)
            for file in file_list:
                self.add_attachment(msg, file['path'], file['name'])

        try:
            smtp_obj = smtplib.SMTP(host=self.host, port=self.port)
            if self.password != "":
                login_result = smtp_obj.login(self.user, self.password)
                # print(login_result)
            send_result = smtp_obj.sendmail(me, to, msg.as_string())
            quit_result = smtp_obj.quit()
            # print(msg, send_result, quit_result)
        except Exception as e:
            SimpleLogger.error(str(e))


if __name__ == '__main__':
    mail = EmailHelper()
