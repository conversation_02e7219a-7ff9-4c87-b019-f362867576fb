# coding=utf-8
'''
Created on 2015-12-25

@author: Dev<PERSON>
'''
from pymongo import MongoClient
from gridfs import GridFS, GridFSBucket
from bson import ObjectId
from gatesidelib.common.simplelogger import SimpleLogger


class MongodbHelper(object):
    '''
    classdocs
    '''

    def __init__(self, host, port):
        self.host = host
        self.port = port

    def save(self, database, collection, value):
        client = MongoClient(self.host, self.port)
        db = client[database]
        collection = db[collection]
        return collection.save(value)

    def remove(self, database, collection, doc_id):
        result = None
        client = MongoClient(self.host, self.port)
        try:
            db = client[database]
            collection = db[collection]
            result = collection.remove(doc_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        finally:
            client.close()
        return result

    def get(self, database, collection, doc_id):
        result = None
        client = MongoClient(self.host, self.port)
        try:
            db = client[database]
            collection = db[collection]
            result = collection.find_one({'_id': ObjectId(doc_id)})
        except Exception as ex:
            SimpleLogger.exception(ex)
        finally:
            client.close()
        return result

    def update(self, database, collection, doc_id, value):
        result = None
        client = MongoClient(self.host, self.port)
        try:
            db = client[database]
            collection = db[collection]
            result = collection.update({'_id': ObjectId(doc_id)}, value)
        except Exception as ex:
            SimpleLogger.exception(ex)
        finally:
            client.close()
        return result

    def put_file(self, database, collection, file_byte, file_name, file_real_name, content_type):
        client = MongoClient(self.host, self.port)
        db = client[database]
        file_fs = GridFS(db)
        id = file_fs.put(file_byte, filename=file_name, name=file_real_name, content_type=content_type)
        return id

    def pub_file_bucket(self, database, collection, file_name, file_property):
        client = MongoClient(self.host, self.port)
        db = client[database]
        bucket_files = GridFSBucket(db, bucket_name=collection)
        file_bucket_fs = bucket_files.open_upload_stream(filename=file_name, metadata=file_property)
        return file_bucket_fs

    def delete_file(self, database, collection, file_id):
        client = MongoClient(self.host, self.port)
        try:
            db = client[database]
            bucket_files = GridFSBucket(db, bucket_name=collection)
            bucket_files.delete(ObjectId(file_id))
        except Exception as ex:
            SimpleLogger.exception(ex)
        finally:
            client.close()

    def get_file_bucket(self, database, collection, file_id):
        grid_out = None
        client = MongoClient(self.host, self.port)
        try:
            db = client[database]
            bucket_files = GridFSBucket(db, bucket_name=collection)
            grid_out = bucket_files.open_download_stream(ObjectId(file_id))
        except Exception as ex:
            SimpleLogger.exception(ex)
        finally:
            client.close()
        return grid_out
