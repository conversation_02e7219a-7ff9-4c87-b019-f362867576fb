#coding=utf-8
'''
Created on 2016-9-13

@author: Dev<PERSON>
'''
import redis

class RedisHelper(object):
    '''
    classdocs
    '''


    def __init__(self,host,port,db,password=None):
        '''
        Constructor
        '''
        self.host=host
        self.port=port
        self.db=db
        self.password = password


    def get_reids_connection(self,password):
        '''

        :param password:
        :return:
        '''
        pool = redis.ConnectionPool(host=self.host,port=self.port,db=self.db,password=password)
        if password is not None:
            return redis.Redis(connection_pool=pool)
        else:
            return redis.Redis(connection_pool=pool)
    
    def set_value(self,key,value,ex):
        r=self.get_reids_connection(self.password)
        r.set(key,value,ex)
    
    def get_value(self,key):
        result=""
        r=self.get_reids_connection(self.password)
        if r.exists(key):
            result=r.get(key).decode('utf-8')
        return result
    
    def get_object(self,key):
        result=""
        r=self.get_reids_connection(self.password)
        if r.exists(key):
            result=r.get(key)
        return result
    
    def delete_value(self,key):
        result=""
        r=self.get_reids_connection(self.password)
        if r.exists(key):
            result=r.delete(key)
        return result
        
    def append(self,key,value,ex):
        r=self.get_reids_connection(self.password)
        if r.exists(key):
            r.append(key,value)
        else:
            r.set(key,value,ex)
    
    def set_svalue(self,key,values,max_ex):
        r=self.get_reids_connection(self.password)
        r.sadd(key,values)
        r.expire(key,max_ex)
    
    def srem_value(self,key,values):
        r=self.get_reids_connection(self.password)
        r.srem(key,values)
        
    def get_svalue(self,key):
        r=self.get_reids_connection(self.password)
        return r.smembers(key)
    
    def has_key(self,key):
        r=self.get_reids_connection(self.password)
        return r.exists(key)
    
    def publish(self,channel,message):
        r=self.get_reids_connection(self.password)
        return r.publish(channel, message)
        
        
        
        
    
        