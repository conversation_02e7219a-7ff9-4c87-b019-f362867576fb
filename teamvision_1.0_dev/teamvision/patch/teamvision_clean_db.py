# encoding=utf-8
import pymysql
import logging, os, sys, inspect

logging.basicConfig(level=logging.INFO,
                    filename='teamvision_clean_db.py.log',
                    filemode='a',
                    format='%(asctime)s - [line:%(lineno)d] - %(levelname)s: %(message)s'
                    )

_currentFile = os.path.abspath(inspect.getfile(inspect.currentframe()))
_currentDir = os.path.dirname(_currentFile)
_parentDir = os.path.dirname(_currentDir)
sys.path.insert(0, _parentDir)


def clear_testcase():
    id_list = []

    ALL_TESTCASEID = "SELECT id FROM project_test_case"
    ALL_CASEID_PARENT_IS_NULL = "SELECT id FROM project_test_case WHERE Parent!=0 AND Parent not in (SELECT id FROM project_test_case)"
    cur.execute(ALL_CASEID_PARENT_IS_NULL)
    all_testcaseid = cur.fetchall()
    for id in all_testcaseid:
        id_list.append(str(id[0]))

    ALL_CASEID_Project_IS_NULL = "SELECT id FROM project_test_case WHERE project not in (SELECT id FROM project)"
    cur.execute(ALL_CASEID_Project_IS_NULL)
    all_testcaseid = cur.fetchall()
    for id in all_testcaseid:
        id_list.append(str(id[0]))

    print("del count =", len(id_list))
    id_list_str = ','.join(id_list)
    logging.info("delete test case list = %s" % id_list_str)
    EXEC_DEL = "DELETE FROM project_test_case WHERE id in (" + id_list_str + ");"
    cur.execute(EXEC_DEL)

def clear_autocase():
    pass

def clear_testplan():
    pass

def clear_ci():
    #de task
    del_ci_task_id = "SELECT id FROM ci_task WHERE IsActive=0"
    cur.execute(del_ci_task_id)
    del_id = fetch_id_to_str("ci_task", cur.fetchall())
    if len(del_id) > 0:
        exec_del_ci_taks = "DELETE FROM ci_task WHERE id in (" + del_id + ");"
        cur.execute(exec_del_ci_taks)

    # del history
    del_ci_task_history_id = "SELECT id FROM ci_task_history WHERE CITaskID not in (SELECT id FROM ci_task)"
    cur.execute(del_ci_task_history_id)
    del_id = fetch_id_to_str("ci_task_history", cur.fetchall())
    if len(del_id) > 0:
        exec_del_ci_task_history = "DELETE FROM ci_task_history WHERE id in (" + del_id + ");"
        cur.execute(exec_del_ci_task_history)

    # del stage
    del_stage_history_id = "SELECT id FROM ci_task_stage_history WHERE TaskHistoryID not in (SELECT id FROM ci_task_history)"
    cur.execute(del_stage_history_id)
    del_id = fetch_id_to_str("ci_task_stage_history", cur.fetchall())
    if len(del_id) > 0:
        exec_del_ci_task_history_stage = "DELETE FROM ci_task_stage_history WHERE id in (" + del_id + ");"
        cur.execute(exec_del_ci_task_history_stage)

    # del step_output
    del_step_id = "SELECT id FROM ci_task_step_output WHERE TaskHistoryID not in (SELECT id FROM ci_task_history)"
    cur.execute(del_step_id)
    del_id = fetch_id_to_str("ci_task_step_output", cur.fetchall())
    if len(del_id) > 0:
        exec_del_ci_task_history_step = "DELETE FROM ci_task_step_output WHERE id in (" + del_id + ");"
        cur.execute(exec_del_ci_task_history_step)

    # del autotesting_task_result
    del_task_result_id = "SELECT id FROM `autotesting_task_result` WHERE StageHistoryID not in (SELECT id FROM `ci_task_stage_history`)"
    cur.execute(del_task_result_id)
    del_id = fetch_id_to_str("autotesting_task_result", cur.fetchall())
    if len(del_id) > 0:
        exec_del_autotesting_task_result = "DELETE FROM `autotesting_task_result` WHERE id in (" + del_id + ");"
        cur.execute(exec_del_autotesting_task_result)

    # del autotesting_case_result
    del_autotesting_case_result = "SELECT id FROM `autotesting_case_result` WHERE TaskResultID not in (SELECT id FROM `autotesting_task_result`)"
    cur.execute(del_autotesting_case_result)
    del_id = fetch_id_to_str("autotesting_case_result", cur.fetchall())
    if len(del_id) > 0:
        exec_del_autotesting_task_result = "DELETE FROM `autotesting_case_result` WHERE id in (" + del_id + ");"
        cur.execute(exec_del_autotesting_task_result)

def fetch_id_to_str(table_name, fetch_all):
    id_list = []
    for id in fetch_all:
        id_list.append(str(id[0]))

    id_list_str = ','.join(id_list)
    print("clear tables=%s, count=[%d], list=[%s] " % (table_name, len(id_list), id_list_str))
    return id_list_str

if __name__ == '__main__':
    # online
    conn = pymysql.connect(host='************', port=9906, user='teamvision', passwd='hiwZUfvx7tLSd53z#UL4', db='teamvision', charset='utf8')
    # test
    #conn = pymysql.connect(host='************', port=9015, user='admin', passwd='cohH9oQz3pTwt$mMavzd', db='teamversion_qa', charset='utf8')

    cur = conn.cursor()
    #clear_ci()
    clear_testcase()

    cur.close()
    conn.commit()
    conn.close()
    print('sql执行成功')

