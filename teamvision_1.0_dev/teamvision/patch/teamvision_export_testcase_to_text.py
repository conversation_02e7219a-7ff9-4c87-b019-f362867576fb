# encoding=utf-8
import pymysql
import logging, os, sys, inspect

logging.basicConfig(level=logging.INFO,
                    filename='teamvision_clean_db.py.log',
                    filemode='a',
                    format='%(asctime)s - [line:%(lineno)d] - %(levelname)s: %(message)s'
                    )

_currentFile = os.path.abspath(inspect.getfile(inspect.currentframe()))
_currentDir = os.path.dirname(_currentFile)
_parentDir = os.path.dirname(_currentDir)
sys.path.insert(0, _parentDir)

"""
case_title-case
"""


def write_file(str):
    with open('./test_case.txt', 'a+') as f:
        f.write(str + '\n')


def get_parent_case(test_case, test_case_list):
    # print(test_case[0], test_case[1])
    test_case_list.append(test_case)
    if test_case[5] == 0:
        return
    test_case_sql = "SELECT id,Title,'Desc',ExpectResult,PreCondition,Parent,IsGroup FROM project_test_case WHERE id=" + str(test_case[5])
    cur.execute(test_case_sql)
    parent_case = cur.fetchall()
    get_parent_case(parent_case[0], test_case_list)


def export_testcase():
    ALL_TESTCASE_SQL = "SELECT id,Title,'Desc',ExpectResult,PreCondition,Parent,IsGroup FROM project_test_case WHERE Project=19 AND IsGroup=0"
    cur.execute(ALL_TESTCASE_SQL)
    all_testcase = cur.fetchall()
    for test_case in all_testcase:
        test_case_list = []
        get_parent_case(test_case, test_case_list)
        case_str = ""
        for case in reversed(test_case_list):
            case_str = case_str + str(case[0]) + " " + case[1] + "||"
        # print(case_str)
        write_file(case_str)


if __name__ == '__main__':
    # online
    # conn = pymysql.connect(host='************', port=9906, user='teamvision', passwd='hiwZUfvx7tLSd53z#UL4', db='teamvision',charset='utf8')
    # test
    conn = pymysql.connect(host='************', port=9015, user='admin', passwd='cohH9oQz3pTwt$mMavzd', db='teamversion_qa', charset='utf8')

    cur = conn.cursor()
    export_testcase()

    cur.close()
    conn.commit()
    conn.close()
    print('sql执行成功')
