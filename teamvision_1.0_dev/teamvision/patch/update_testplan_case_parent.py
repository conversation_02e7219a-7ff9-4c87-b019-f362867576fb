# encoding=utf-8

import pymysql
import logging, os, sys, inspect

logging.basicConfig(level=logging.INFO,
                    filename='update_testplan_case_parent.py.log',
                    filemode='a',
                    format='%(asctime)s - [line:%(lineno)d] - %(levelname)s: %(message)s'
                    )

_currentFile = os.path.abspath(inspect.getfile(inspect.currentframe()))
_currentDir = os.path.dirname(_currentFile)
_parentDir = os.path.dirname(_currentDir)
sys.path.insert(0, _parentDir)


def test_case_parent_id(case_id):
    sql_test_case_list = f"SELECT Parent FROM project_test_case WHERE id={case_id}"
    cur.execute(sql_test_case_list)
    try:
        parent_id = cur.fetchall()
        logging.info(f"select case_id={case_id}:{parent_id}")
        return parent_id[0][0]
    except Exception as e:
        logging.error(e)
        return None


def update_testplan_case_parent():
    sql_test_plan_case_list = "SELECT * FROM project_test_plan_case WHERE Parent IS NULL"
    cur.execute(sql_test_plan_case_list)
    test_plan_case_list = cur.fetchall()
    for plan_case in test_plan_case_list:
        parent_id = test_case_parent_id(plan_case[4])
        if parent_id is not None:
            sql_update = f"UPDATE project_test_plan_case SET Parent={parent_id} WHERE id={plan_case[0]}"
            cur.execute(sql_update)
            connection.commit()
            logging.info("%s" % sql_update)


if __name__ == '__main__':
    # online
    # conn = pymysql.connect(host='************', port=9906, user='teamvision', passwd='hiwZUfvx7tLSd53z#UL4', db='teamvision',
    #                        charset='utf8')

    # test
    connection = pymysql.connect(host='************', port=9015, user='admin', passwd='cohH9oQz3pTwt$mMavzd', db='teamversion_qa',
                                 charset='utf8')

    cur = connection.cursor()
    update_testplan_case_parent()
    cur.close()

    connection.commit()
    connection.close()
    print('sql执行成功')
