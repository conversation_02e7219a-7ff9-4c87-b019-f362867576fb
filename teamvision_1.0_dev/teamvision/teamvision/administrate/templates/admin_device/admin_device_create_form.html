<form class="form-horizontal" id="device_create_form" method="post" action="{{ device.form_action }}">
	<div class="form-group">
		<label for="DeviceName" class="col-sm-2 control-label">名称</label>
		<div class="col-sm-6">
			<input type="hidden" id="device_id" name="device_id" value="{{ device.device.id }}" />
			<input type="text" class="form-control" id="DeviceName" name="DeviceName" value="{{ device.device.DeviceName }}" >
		</div>
	</div>
	<div class="form-group" >
		<label for="DeviceNumber" class="col-sm-2 control-label">编号</label>
		<div class="col-sm-4">
			<input  type="text" class="form-control" id="DeviceNumber" name="DeviceNumber"    value="{{ device.device.DeviceNumber }}" >
		</div>
	</div>
	
	<div class="form-group">
		<label for="DeviceType" class="col-sm-2 control-label">类型</label>
		<div class="col-sm-5">
			<select name="DeviceType" id="DeviceType">
                    {{ device_type_controll }}
            </select>
		</div>
	</div>
	
	<div class="form-group">
		<label for="DeviceOS" class="col-sm-2 control-label">系统</label>
		<div class="col-sm-6">
			<select name="DeviceOS" id="DeviceOS">
            {{ device_os_controll }}
        </select>
		</div>
	</div>
	
	<div class="form-group">
		<label for="DeviceOSVersion" class="col-sm-2 control-label">版本</label>
		<div class="col-sm-5">
			<select name="DeviceOSVersion" id="DeviceOSVersion">
                    {{ device_os_version_controll }}
            </select>
		</div>
	</div>
	
	<div class="form-group">
		<label for="DeviceScreenSize" class="col-sm-2 control-label">分辨率</label>
		<div class="col-sm-5">
			<select name="DeviceScreenSize" id="DeviceScreenSize">
                    {{ device_screen_controll }}
            </select>
		</div>
	</div>
	
	<div class="form-group">
		<label for="DeviceBorrower" class="col-sm-2 control-label">借用人</label>
		<div class="col-sm-5">
			<select name="DeviceBorrower" id="device_borrowser">
			<option value=0>None</option>
             {{ device_borrower_controll }}
            </select>
		</div>
	</div>
	
	
	<!-- <div class="form-group">
		<label for="DeviceStatus" class="col-sm-2 control-label">状态</label>
		<div class="col-sm-5">
			<select name="DeviceStatus" id="DeviceStatus">
                    {{ device_status_controll }}
            </select>
		</div>
	</div> -->
	
	<div class="form-group">
		<div class="col-sm-offset-2 col-sm-10">
			<button type="button" class="btn btn-success" id="save_device">
				{% if device.is_create %}
				   添加
				{% else %}
				   保存
				{% endif %}
			</button>
		</div>
	</div>
</form>
