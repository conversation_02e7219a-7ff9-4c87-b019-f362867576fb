{% for device in devices %}
		     <div  class="admin_device_listview_item container-fluid">
		   	<div class="row">
				<div class="col-lg-7  col-sm-7">
						<div class="admin_device_listview_item_title row">
							<span class="col-sm-12">
								<i class="fa fa-fw fa-lg {{ device.device_os }}" style="color:green" name="project-task-check"></i>
								<span  name="project-task_title" >{{ device.device.DeviceName }}</span>
							</span>
						</div>
						<div class="admin_device_listview_item_content row">
							<span class="col-sm-9">
								<input type="hidden" name="device_id" value="{{ device.device.id }}">
								<span name="device_code">#{{ device.device.DeviceNumber }}</span>
								<span  class="">系统版本：{{ device.device_os_version }}</span>
								<span  class="">{{ device.device_screen }}</span>
								<span  class="">状态：{{ device.device_status }}</span>
								{% if device.is_borrowed  %}
								<span  class="">{{ device.device.DeviceBorrorwTime }}</span>
							    {% endif %}
							 </span>
								 
						</div>


				</div>
				<div class="col-sm-2" style="line-height:1.5em">
						<span style="margin-top:20px; display:inline-block; cursor:pointer">
						    <span labelid="{{ owner.id }}"><img src="{{ device.borrower_avatar }}" class="img-circle" title="{{ device.borrower_name }}" style="width:30px;height:30px;" /> {{ device.borrower_name }}</span>	
						</span>
				</div>
				<div class="col-sm-3" style="line-height:1.5em;">
					{% if device.device.DeviceBorrower %}
					{% if device.is_ordered %}
					<span style="margin-top:30px;display:inline-block;cursor:pointer" name="device_lend"><i class="fa fa-gavel fa-fw fa-lg" style="color:orange;" title="出借"></i></span>
					{% else %}
					<span style="margin-top:30px;display:inline-block;cursor:pointer" name="device_return"><i class="fa fa-gavel fa-fw fa-lg" style="color:green;" title="归还"></i></span>
					{% endif %}
					{% endif %}
					<span style="margin-top:30px;display:inline-block;cursor:pointer" name="device_edit">
						<a href="/administrate/device/edit/{{ device.device.id }}"><i class="fa fa-pencil-square-o fa-fw fa-lg" style="color:orange;" title="编辑"></i></a>
					</span>
					<span style="margin-top:30px;display:inline-block;cursor:pointer" name="device_delete"><i class="fa fa-trash-o fa-fw fa-lg" style="color:red;" title="删除"></i></span>
				</div>
			</div>
		    </div>

{% endfor %}