{% for permission in permissions %}
		   <div  class="admin_user_listview_item container-fluid">
		   	<div class="row">
		   		<div class="pull-left permission_row_item" style="width:170px;">
					<span style="color:#333;">
						<input type="hidden" value="{{ permission.id }}" name="permission_id" />
						<span style="font-size:14px;"  name="permission_name">
						<i class="fa fa-shield fa-fw fa-lg"></i>
							 {{ permission.name }}
						</span>
					</span> 
					
				</div>
				<div class="col-md-8 col-sm-6 usergroup_row_item" >
							<span name="permission_desc"    style="color:#333;">
								<i class="fa fa-info-circle fa-fw"></i>
								{{ permission.description }}
							</span>
				</div>
				<div class="col-sm-1" style="line-height:1.5em;">
					<span style="margin-top:30px;display:inline-block;cursor:pointer">
						{% if permission.is_permission_active %}
				  				<i  name="user_group_active_permission" active="1"  class="fa fa-toggle-on fa-fw fa-2x" style="color:green;" ></i>
				  		{% else %}
				  		
				  		<i name="user_group_active_permission" active="0" class="fa fa-toggle-off fa-fw fa-2x" style="color:gray"></i>
				  		
				  		{% endif %}
					</span>
				</div>
			</div>
		    </div>
{% endfor %}