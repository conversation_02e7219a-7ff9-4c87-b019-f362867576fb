# coding=utf-8
'''
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
'''

from django.urls import path, re_path
from teamvision.administrate.views.admin_device_view import all, device_create_get, device_create_post, device_edit_get, \
    device_edit_post, get_version_controll

from teamvision.administrate.views.admin_device_view import borrow_device, lend_device, return_device, \
    get_devcie_confirm_dialog, device_delete

admin_device_router = [
    re_path(r"device/(all)$", all),
    re_path(r"device/(lending)$", all),
    re_path(r"device/(android)$", all),
    re_path(r"device/(ios)$", all),
    re_path(r"device/(other)$", all),
    re_path(r"device/create$", device_create_get),
    re_path(r"device/create_post$", device_create_post),
    re_path(r"device/edit/(\d{1,3})$", device_edit_get),
    re_path(r"device/edit_post$", device_edit_post),
    re_path(r"device/borrow$", borrow_device),
    re_path(r"device/lend$", lend_device),
    re_path(r"device/return$", return_device),
    re_path(r"device/confirm_dialog$", get_devcie_confirm_dialog),
    re_path(r"device/delete$", device_delete),
    re_path(r"device/version_controll", get_version_controll),
]
