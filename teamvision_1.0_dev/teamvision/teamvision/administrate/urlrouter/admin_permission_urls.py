# coding=utf-8
'''
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
'''
from django.urls import re_path
from teamvision.administrate.views.admin_permission_view import permission, permission_create_dialog, permission_create, \
    update_name, update_desc
from teamvision.administrate.views.admin_permission_view import check_value_exists, permission_delete

admin_permission_router = [
    re_path(r"permission/(all)$", permission),
    re_path(r"permission/create_dialog$", permission_create_dialog),
    re_path(r"permission/create$", permission_create),
    re_path(r"permission/check_value_exists$", check_value_exists),
    re_path(r"permission/delete$", permission_delete),
    re_path(r"permission/(\d{1,6})/update_name", update_name),
    re_path(r"permission/(\d{1,6})/update_desc", update_desc),
]
