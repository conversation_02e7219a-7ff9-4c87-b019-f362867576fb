# coding=utf-8
'''
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
'''

from django.urls import re_path
from teamvision.administrate.views import admin_system_role_view

admin_system_role_router = [
    re_path(r"systemrole/(all)$", admin_system_role_view.system_role),
    re_path(r"systemrole/create$", admin_system_role_view.role_create),
    re_path(r"systemrole/check_value_exists$", admin_system_role_view.check_value_exists),
    re_path(r"systemrole/usergroup_list$", admin_system_role_view.userrole_list),
    re_path(r"systemrole/(\d{1,6})/delete$", admin_system_role_view.role_delete),
    re_path(r"systemrole/(\d{1,6})/edit_get$", admin_system_role_view.role_edit_get),
    re_path(r"systemrole/(\d{1,6})/update_permission$", admin_system_role_view.update_role_permission),
    re_path(r"systemrole/(\d{1,6})/group_permission_list$", admin_system_role_view.role_permission_list),
    re_path(r"systemrole/(\d{1,6})/update_description", admin_system_role_view.update_description),
]
