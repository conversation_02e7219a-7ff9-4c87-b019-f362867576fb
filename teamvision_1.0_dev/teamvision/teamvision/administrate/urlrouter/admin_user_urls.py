# coding=utf-8
'''
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
'''
from django.urls import re_path
from teamvision.administrate.views.admin_user_view import user, user_create_dialog, user_create, user_edit_post, \
    update_group, reset_password
from teamvision.administrate.views.admin_user_view import check_value_exists, user_list, user_delete, user_edit_get

admin_user_router = [
    re_path(r"user/(all)$", user),
    re_path(r"user/create_dialog$", user_create_dialog),
    re_path(r"user/create$", user_create),
    re_path(r"user/check_value_exists$", check_value_exists),
    re_path(r"user/user_list$", user_list),
    re_path(r"user/delete$", user_delete),
    re_path(r"user/(\d{1,6})/edit$", user_edit_get),
    re_path(r"user/(\d{1,6})/edit_post$", user_edit_post),
    re_path(r"user/(\d{1,6})/update_group", update_group),
    re_path(r"user/(\d{1,6})/reset_password", reset_password),
]
