# #coding=utf-8
# # coding=utf-8
# '''
# Created on 2014-1-5
# 
# @author: z<PERSON><PERSON><PERSON>
# '''
# from django.urls import re_path
# from doraemon.administrate.views.admin_user_group_view import user_group,group_create,check_value_exists,group_delete,usergroup_list,usergroup_edit_get,group_permission_list,update_description,update_group_permission
# 
# admin_usergroup_router=[
#                     re_path(r"usergroup/(all)$",user_group),
#                     re_path(r"usergroup/create$",group_create),
#                     re_path(r"usergroup/check_value_exists$",check_value_exists),
#                     re_path(r"usergroup/usergroup_list$",usergroup_list),
#                     re_path(r"usergroup/(\d{1,6})/delete$",group_delete),
#                     re_path(r"usergroup/(\d{1,6})/edit_get$",usergroup_edit_get),
#                     re_path(r"usergroup/(\d{1,6})/update_permission$",update_group_permission),
#                     re_path(r"usergroup/(\d{1,6})/group_permission_list$",group_permission_list),
#                     re_path(r"usergroup/(\d{1,6})/update_description",update_description),
#                  ]