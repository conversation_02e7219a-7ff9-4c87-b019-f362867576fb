# coding=utf-8
"""
Created on 2025-06-16

@author: zhangpeng
"""

from teamvision.ai import models
from django_filters import rest_framework as filters
from django_filters import BaseInFilter, NumberFilter


class NumberInFilter(BaseInFilter, NumberFilter):
    pass


class AIUsageStatisticsFilter(filters.FilterSet):
    project = NumberInFilter(field_name='project', lookup_expr='in')
    data = filters.DateFromToRangeFilter(field_name='data')

    class Meta(object):
        model = models.AIUsageStatistics
        fields = ['project', 'date']

