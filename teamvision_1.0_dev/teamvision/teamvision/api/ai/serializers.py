# coding=utf-8
"""
AI相关的序列化器
"""

from rest_framework import serializers
from teamvision.ai.models import (
    AIGenerationSession, AIGeneratedTestCase, 
    AIChatHistory, AIConfiguration, AIUsageStatistics
)


class AIGenerationSessionSerializer(serializers.ModelSerializer):
    """AI生成会话序列化器"""
    
    user_name = serializers.CharField(source='user.username', read_only=True)
    project_name = serializers.CharField(source='project.PBTitle', read_only=True)
    created_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    updated_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    completed_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    class Meta:
        model = AIGenerationSession
        fields = [
            'id', 'session_id', 'user', 'user_name', 'project', 'project_name', 'requirement_description', 
            'generation_type', 'case_count', 'status', 'ragflow_session_id', 'ragflow_generation_id',
            'generated_count', 'accepted_count', 'created_time', 'updated_time', 'completed_time', 'metadata'
        ]
        read_only_fields = [
            'id', 'session_id', 'user', 'ragflow_session_id', 'ragflow_generation_id',
            'generated_count', 'accepted_count', 'created_time', 'updated_time', 'completed_time'
        ]


class AIGenerationSessionCreateSerializer(serializers.ModelSerializer):
    """AI生成会话创建序列化器"""
    
    class Meta:
        model = AIGenerationSession
        fields = [
            'requirement_description', 'generation_type', 'case_count'
        ]
    
    def validate_case_count(self, value):
        if value < 1 or value > 50:
            raise serializers.ValidationError("生成数量必须在1-50之间")
        return value
    
    def validate_generation_type(self, value):
        valid_types = ['functional', 'boundary', 'exception', 'integration', 'performance', 'security']
        if value not in valid_types:
            raise serializers.ValidationError(f"生成类型必须是以下之一: {', '.join(valid_types)}")
        return value


class AIGeneratedTestCaseSerializer(serializers.ModelSerializer):
    """AI生成测试用例序列化器"""
    
    session_id = serializers.CharField(source='session.session_id', read_only=True)
    created_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    updated_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    class Meta:
        model = AIGeneratedTestCase
        fields = [
            'id', 'session', 'session_id', 'title', 'description', 'precondition', 'test_steps', 
            'expected_result', 'priority', 'test_type', 'tags', 'status', 'project_test_case',
            'ai_confidence', 'generation_prompt', 'user_rating', 'user_feedback', 'created_time',
            'updated_time', 'metadata'
        ]
        read_only_fields = [
            'id', 'session', 'session_id', 'project_test_case',
            'ai_confidence', 'generation_prompt', 'created_time', 'updated_time'
        ]


class AIGeneratedTestCaseUpdateSerializer(serializers.ModelSerializer):
    """AI生成测试用例更新序列化器"""
    
    class Meta:
        model = AIGeneratedTestCase
        fields = [
            'title', 'description', 'precondition', 'test_steps', 'expected_result',
            'priority', 'test_type', 'tags', 'status', 'user_rating', 'user_feedback'
        ]
    
    def validate_status(self, value):
        valid_statuses = ['generated', 'reviewed', 'accepted', 'rejected', 'modified']
        if value not in valid_statuses:
            raise serializers.ValidationError(f"状态必须是以下之一: {', '.join(valid_statuses)}")
        return value
    
    def validate_user_rating(self, value):
        if value is not None and (value < 1 or value > 5):
            raise serializers.ValidationError("用户评分必须在1-5之间")
        return value


class AIChatHistorySerializer(serializers.ModelSerializer):
    """AI对话历史序列化器"""
    
    session_id = serializers.CharField(source='session.session_id', read_only=True)
    created_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    class Meta:
        model = AIChatHistory
        fields = [
            'id', 'session', 'session_id', 'message_type', 'content',
            'ragflow_message_id', 'created_time', 'metadata'
        ]
        read_only_fields = [
            'id', 'session', 'session_id', 'ragflow_message_id', 'created_time'
        ]


class AIConfigurationSerializer(serializers.ModelSerializer):
    """AI配置序列化器"""
    
    project_name = serializers.CharField(source='project.PBTitle', read_only=True)
    user_name = serializers.CharField(source='user.username', read_only=True)
    created_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    updated_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    class Meta:
        model = AIConfiguration
        fields = [
            'id', 'config_type', 'config_key', 'config_value', 'description',
            'project', 'project_name', 'user', 'user_name', 'is_active',
            'created_time', 'updated_time'
        ]
        read_only_fields = ['id', 'created_time', 'updated_time']


class AIUsageStatisticsSerializer(serializers.ModelSerializer):
    """AI使用统计序列化器"""
    
    user_name = serializers.CharField(source='user.username', read_only=True)
    project_name = serializers.CharField(source='project.PBTitle', read_only=True)
    created_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    updated_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    # 计算字段
    acceptance_rate = serializers.SerializerMethodField()
    api_success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = AIUsageStatistics
        fields = [
            'id', 'user', 'user_name', 'project', 'project_name', 'date',
            'generation_count', 'generated_cases_count', 'accepted_cases_count',
            'chat_messages_count', 'api_calls_count', 'api_success_count', 'api_error_count',
            'total_generation_time', 'average_generation_time',
            'acceptance_rate', 'api_success_rate',
            'created_time', 'updated_time'
        ]
        read_only_fields = ['id', 'created_time', 'updated_time']
    
    def get_acceptance_rate(self, obj):
        """计算采纳率"""
        if obj.generated_cases_count > 0:
            return round((obj.accepted_cases_count / obj.generated_cases_count) * 100, 2)
        return 0.0
    
    def get_api_success_rate(self, obj):
        """计算API成功率"""
        if obj.api_calls_count > 0:
            return round((obj.api_success_count / obj.api_calls_count) * 100, 2)
        return 0.0


class TestCaseGenerationRequestSerializer(serializers.Serializer):
    """测试用例生成请求序列化器"""
    
    project_id = serializers.IntegerField()
    requirement_description = serializers.CharField(max_length=5000)
    generation_type = serializers.ChoiceField(
        choices=['functional', 'boundary', 'exception', 'integration', 'performance', 'security'],
        default='functional'
    )
    case_count = serializers.IntegerField(min_value=1, max_value=50, default=10)
    
    def validate_project_id(self, value):
        from teamvision.project.models import Project
        try:
            Project.objects.get(id=value)
        except Project.DoesNotExist:
            raise serializers.ValidationError("项目不存在")
        return value


class TestCaseAcceptanceSerializer(serializers.Serializer):
    """测试用例采纳序列化器"""
    
    ai_case_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=50
    )
    parent_id = serializers.IntegerField(default=0)
    module_id = serializers.IntegerField(default=0)
    
    def validate_ai_case_ids(self, value):
        # 验证AI用例是否存在
        existing_ids = AIGeneratedTestCase.objects.filter(
            id__in=value
        ).values_list('id', flat=True)
        
        missing_ids = set(value) - set(existing_ids)
        if missing_ids:
            raise serializers.ValidationError(f"以下AI用例不存在: {list(missing_ids)}")
        
        return value


class TestCaseOptimizationSerializer(serializers.Serializer):
    """测试用例优化序列化器"""
    
    ai_case_id = serializers.IntegerField()
    optimization_type = serializers.ChoiceField(
        choices=['quality', 'coverage', 'efficiency', 'clarity'],
        default='quality'
    )
    
    def validate_ai_case_id(self, value):
        try:
            AIGeneratedTestCase.objects.get(id=value)
        except AIGeneratedTestCase.DoesNotExist:
            raise serializers.ValidationError("AI生成的测试用例不存在")
        return value


class AIChatRequestSerializer(serializers.Serializer):
    """AI对话请求序列化器"""
    
    session_id = serializers.CharField(max_length=100)
    message = serializers.CharField(max_length=2000)
    
    def validate_session_id(self, value):
        try:
            AIGenerationSession.objects.get(session_id=value)
        except AIGenerationSession.DoesNotExist:
            raise serializers.ValidationError("会话不存在")
        return value


class TestCoverageAnalysisSerializer(serializers.Serializer):
    """测试覆盖度分析序列化器"""
    
    project_id = serializers.IntegerField()
    requirement_text = serializers.CharField(max_length=5000)
    
    def validate_project_id(self, value):
        from teamvision.project.models import Project
        try:
            Project.objects.get(id=value)
        except Project.DoesNotExist:
            raise serializers.ValidationError("项目不存在")
        return value
