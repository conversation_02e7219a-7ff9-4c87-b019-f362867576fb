# coding=utf-8
"""
AI模块的URL路由配置
"""

from django.urls import path, re_path
from teamvision.api.ai import views

urlpatterns = [
    # AI生成会话管理
    path('sessions/', views.AIGenerationSessionListCreateView.as_view(), name='ai-sessions'),
    path('sessions/<int:pk>/', views.AIGenerationSessionDetailView.as_view(), name='ai-session-detail'),
    
    # AI生成的测试用例管理
    path('sessions/<str:session_id>/cases/', views.AIGeneratedTestCaseListView.as_view(), name='ai-generated-cases'),
    path('cases/<int:pk>/', views.AIGeneratedTestCaseDetailView.as_view(), name='ai-case-detail'),
    
    # AI对话功能
    path('chat/', views.AIChatView.as_view(), name='ai-chat'),
    path('sessions/<str:session_id>/history/', views.AIChatHistoryView.as_view(), name='ai-chat-history'),
    
    # 核心AI功能
    path('generate/', views.TestCaseGenerationView.as_view(), name='ai-generate-testcases'),
    path('accept/', views.TestCaseAcceptanceView.as_view(), name='ai-accept-testcases'),
    path('optimize/', views.TestCaseOptimizationView.as_view(), name='ai-optimize-testcase'),
    path('analyze-coverage/', views.TestCoverageAnalysisView.as_view(), name='ai-analyze-coverage'),
    
    # 统计和监控
    path('statistics/', views.AIUsageStatisticsView.as_view(), name='ai-usage-statistics'),

    # 服务状态和信息
    path('status/', views.AIServiceStatusView.as_view(), name='ai-service-status'),
    path('model-info/', views.AIModelInfoView.as_view(), name='ai-model-info'),

    # 批量操作
    path('cases/batch-update-status/', views.BatchUpdateAICasesStatusView.as_view(), name='ai-batch-update-status'),
    path('cases/batch-delete/', views.BatchDeleteAICasesView.as_view(), name='ai-batch-delete'),
    path('cases/batch-rate/', views.BatchRateAICasesView.as_view(), name='ai-batch-rate'),

    # 导出功能
    path('sessions/<str:session_id>/export/', views.ExportAIGeneratedCasesView.as_view(), name='ai-export-cases'),
    path('sessions/<str:session_id>/history/export/', views.ExportChatHistoryView.as_view(), name='ai-export-history'),

    # 健康检查和监控
    path('health/', views.AIHealthCheckView.as_view(), name='ai-health-check'),
    path('metrics/', views.AIUsageMetricsView.as_view(), name='ai-usage-metrics'),

    # 配置管理
    path('config/', views.AIConfigurationView.as_view(), name='ai-configuration'),
    path('config/<int:pk>/', views.AIConfigurationDetailView.as_view(), name='ai-configuration-detail'),
    # path('templates/', views.AIPromptTemplateView.as_view(), name='ai-prompt-templates'),
    # path('model-config/', views.AIModelConfigView.as_view(), name='ai-model-config'),
]
