# coding=utf-8

from django.urls import path, re_path, include
# from django.contrib.auth.models import User,Group
# from teamvision.project.models import ProjectMember
# from rest_framework import routers, serializers, viewsets
# from rest_framework.decorators import detail_route, list_route
# from rest_framework import parsers, renderers, generics, status
# from rest_framework.authtoken.models import Token
# from rest_framework.authtoken.serializers import AuthTokenSerializer
# from rest_framework.permissions import AllowAny,IsAuthenticatedOrReadOnly
# from rest_framework.response import Response
# from rest_framework.views import APIView
# import oauth2_provider.views as oauth2_views
from rest_framework.documentation import include_docs_urls
from rest_framework.schemas import get_schema_view
from django.views.generic import TemplateView
# from rest_framework_swagger.renderers import SwaggerUIRenderer, OpenAPICodec

# from django.conf import settings
# from rest_framework.compat import unicode_to_repr
# from rest_framework.exceptions import ValidationError
# from rest_framework.utils.representation import smart_repr
# from teamvision.api.api_render import TeamvisionJSONRenderer
# import django_filters
# from rest_framework import filters


# class LoginView(APIView):
#     """
#     A view that allows users to login providing their username and password.
#     """
#
#     throttle_classes = ()
#     permission_classes = ()
#     parser_classes = (parsers.FormParser, parsers.MultiPartParser, parsers.JSONParser,)
#     renderer_classes = (renderers.JSONRenderer,)
#     serializer_class = AuthTokenSerializer
#
#     def post(self, request):
#         serializer = self.serializer_class(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         user = serializer.validated_data['user']
#         token, created = Token.objects.get_or_create(user=user)
#         return Response({'token': token.key})

# OAuth2 provider endpoints
# oauth2_endpoint_views = [
#     re_path(r'^authorize/$', oauth2_views.AuthorizationView.as_view(), name="authorize"),
#     re_path(r'^token/$', oauth2_views.TokenView.as_view(), name="token"),
#     re_path(r'^revoke-token/$', oauth2_views.RevokeTokenView.as_view(), name="revoke-token"),
# ]
#
# if settings.DEBUG:
#     # OAuth2 Application Management endpoints
#     oauth2_endpoint_views += [
#         re_path(r'^applications/$', oauth2_views.ApplicationList.as_view(), name="list"),
#         re_path(r'^applications/register/$', oauth2_views.ApplicationRegistration.as_view(), name="register"),
#         re_path(r'^applications/(?P<pk>\d+)/$', oauth2_views.ApplicationDetail.as_view(), name="detail"),
#         re_path(r'^applications/(?P<pk>\d+)/delete/$', oauth2_views.ApplicationDelete.as_view(), name="delete"),
#         re_path(r'^applications/(?P<pk>\d+)/update/$', oauth2_views.ApplicationUpdate.as_view(), name="update"),
#     ]
#
#     # OAuth2 Token Management endpoints
#     oauth2_endpoint_views += [
#         re_path(r'^authorized-tokens/$', oauth2_views.AuthorizedTokensListView.as_view(), name="authorized-token-list"),
#         re_path(r'^authorized-tokens/(?P<pk>\d+)/delete/$', oauth2_views.AuthorizedTokenDeleteView.as_view(),
#             name="authorized-token-delete"),
#     ]

from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

schema_view = get_schema_view(
    openapi.Info(
        title="Teamvision API",
        default_version='v1',
        description="测试平台接口文档",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    # re_path(r'^login/$', LoginView.as_view(), name="login"),
    # re_path(r'user/profiles/(?P<id>.+)/$', UserProfileView.as_view(), name="profile"),
    # re_path(r'user/profiles', UserProfileView.as_view(), name="profile-list"),
    # re_path(r'posts', PostView.as_view(), name="post-list"),
    # re_path(r'^o/', include((oauth2_endpoint_views,'oauth2_provider'), namespace='oauth2_provider')),
    path('docs/', include_docs_urls(title='测试平台接口文档')),
    re_path(r'swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    re_path(r'swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    re_path(r'redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    re_path(r'project/', include('teamvision.api.project.urlrouter.project_urls')),
    re_path(r'ucenter/', include('teamvision.api.ucenter.urlrouter.ucenter_urls')),
    re_path(r'home/', include('teamvision.api.home.urlrouter.home_urls')),
    re_path(r'logcat/', include('teamvision.api.logcat.urlrouter.logcat_urls')),
    re_path(r'ci/', include('teamvision.api.ci.urlrouter.ci_urls')),
    re_path(r'interface/', include('teamvision.api.interface.urlrouter.interface_urls')),
    re_path(r'auth/', include('teamvision.api.auth.urlrouter.auth_urls')),
    re_path(r'common/', include('teamvision.api.common.urlrouter.common_urls')),
    re_path(r'webhook/', include('teamvision.api.webhook.router.urls')),
    re_path(r'message/', include('teamvision.api.message.router.urls')),
    re_path(r'ai/', include('teamvision.api.ai.urls')),
    re_path(r'document/', include('teamvision.api.document.urls')),
]
