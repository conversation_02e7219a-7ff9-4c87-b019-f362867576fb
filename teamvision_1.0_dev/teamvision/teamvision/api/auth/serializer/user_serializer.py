# coding=utf-8
"""
Created on 2017-10-12

@author: Administrator
"""

from rest_framework import serializers
from django.contrib.auth.models import User, Group
from business.auth_user.user_service import UserService
from business.ucenter.account_service import AccountService
from teamvision.auth_extend.user.models import UserExtend
from business.project.product_space_service import ProductSpaceService
from teamvision.project.models import ProductSpace
from teamvision.api.project.serializer.product_space_serializer import ProductSpaceSerializer, \
    UserProductSpaceSerializer


class UserSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    system_permision = serializers.SerializerMethodField()
    system_role_label = serializers.SerializerMethodField()
    extend_info = serializers.SerializerMethodField()
    is_space_admin = serializers.SerializerMethodField()
    ViewData = serializers.SerializerMethodField()
    date_joined = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    last_login = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_avatar(self, obj):
        return AccountService.get_avatar_url(obj)

    def get_system_permision(self, obj):
        return UserService.get_system_permission(obj.id)

    def get_ViewData(self, obj):
        result = dict()
        space_list = ProductSpaceService.get_space_list(obj.id)
        result["space_list"] = self.get_space_list(obj, space_list)
        # result["space_view_data"] = self.get_space_view_data(obj, space_list)
        result["default_space"] = self.get_default_space(obj, space_list)
        return result

    def get_space_list(self, obj, space_list):
        # space_list = ProductSpaceService.get_space_list(obj.id)
        result = list()
        for space in space_list:
            # temp = ProductSpaceSerializer(instance=space)
            result.append(space.id)
        return result

    def get_space_view_data(self, obj, space_list):
        space_list = ProductSpaceService.get_space_list(obj.id)
        result = list()
        for space in space_list:
            temp = ProductSpaceSerializer(instance=space)
            result.append(temp.data)
        return result

    def get_default_space(self, obj, space_list):
        result = None
        if not obj.is_anonymous and obj.extend_info is not None:
            space = ProductSpace.objects.get(obj.extend_info.default_space)
            if space is None:
                # space_list = ProductSpaceService.get_space_list(obj.id)
                if len(space_list) > 0:
                    space = space_list[0]
            serializer = UserProductSpaceSerializer(instance=space)
            result = serializer.data
        return result

    def get_is_space_admin(self, obj):
        return UserService.is_space_admin(obj.id)

    def get_name(self, obj):
        result = "--"
        if obj is not None and obj.username != "":
            try:
                result = obj.last_name + obj.first_name
            except Exception as ex:
                pass
        return result

    def get_system_role_label(self, obj):
        result = 'U'
        for user_group in obj.groups.get_queryset():
            group = Group.objects.get(id=user_group.id)
            result = group.name[0]
        return result

    def get_extend_info(self, obj):
        result = dict()
        try:
            if obj.extend_info is not None:
                serlizer = UserExtendSerializer(obj.extend_info)
                result = serlizer.data
        except Exception as ex:
            pass
        return result

    class Meta:
        model = User
        exclude = ('password', 'is_active',)
        read_only_fields = ('id',)
        depth = 2


class UserListSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    system_role_label = serializers.SerializerMethodField()
    last_login = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_name(self, obj):
        result = "--"
        if obj is not None and obj.username != "":
            try:
                result = obj.last_name + obj.first_name
            except Exception as ex:
                pass
        return result

    def get_avatar(self, obj):
        return AccountService.get_avatar_url(obj)

    def get_system_role_label(self, obj):
        result = 'U'
        for user_group in obj.groups.get_queryset():
            group = Group.objects.get(id=user_group.id)
            result = group.name[0]
        return result

    class Meta:
        model = User
        exclude = ('password', 'is_active', 'is_superuser', 'user_permissions', 'groups', 'last_name', 'first_name',
                   'date_joined',)
        read_only_fields = ('id',)
        depth = 2


class UserExtendSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserExtend
        fields = '__all__'
        read_only_fields = ('id',)
