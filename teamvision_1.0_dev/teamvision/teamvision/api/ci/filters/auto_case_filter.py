#coding=utf-8
"""
Created on 2016-12-5

@author: z<PERSON><PERSON><PERSON>
"""


from teamvision.ci.models import AutoCase
from url_filter.filtersets.django import ModelFilterSet

class AutoCaseFilterSet(ModelFilterSet):
    class Meta(object):
        model = AutoCase
        fields = ['ProjectID','id','CaseTag','InterfaceID','ModuleID','CaseType','CaseName','ClassName','PackageName']
        exclude = ['CaseTag']


        