#coding=utf-8
"""
Created on 2016-12-5

@author: z<PERSON><PERSON><PERSON>
"""


from teamvision.ci.models import AutoCaseResult,UnitTestCaseResult
from url_filter.filtersets.django import ModelFilterSet


class AutoCaseResultFilterSet(ModelFilterSet):
    class Meta(object):
        model = AutoCaseResult
        fields = ['TaskResultID', 'Result', 'FailCategoryID']


class UnitTestCaseResultFilterSet(ModelFilterSet):
    class Meta(object):
        model = UnitTestCaseResult
        fields = ['TaskResultID','Result']
        