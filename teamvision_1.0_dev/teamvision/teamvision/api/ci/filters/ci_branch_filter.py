#coding=utf-8
"""
Created on 2016-12-5

@author: z<PERSON><PERSON><PERSON>
"""


from teamvision.ci.models import BranchAction,BranchActionTaskMap,BranchActionHistory,ProjectGitRepo
from url_filter.filtersets.django import ModelFilterSet

class ProjectGitRepoFilterSet(ModelFilterSet):
    class Meta(object):
        model = ProjectGitRepo
        fields = ['ProjectID','id']
        exclude = []


class BranchActionFilterSet(ModelFilterSet):
    class Meta(object):
        model = BranchAction
        fields = ['ProjectID','id']
        exclude = []


class BranchActionTaskMapFilterSet(ModelFilterSet):
    class Meta(object):
        model = BranchActionTaskMap
        fields = ['CITaskID','id','ActionID']
        exclude = []


class BranchActionHistoryFilterSet(ModelFilterSet):
    class Meta(object):
        model = BranchActionHistory
        fields = ['ProjectID','id','Action']
        exclude = []


        