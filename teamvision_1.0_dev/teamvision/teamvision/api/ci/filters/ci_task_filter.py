#coding=utf-8
"""
Created on 2016-12-5

@author: z<PERSON><PERSON><PERSON>
"""


from teamvision.ci.models import CITask,CIPassRateStatisticsDaily
from url_filter.filtersets.django import ModelFilterSet

class CITaskFilterSet(ModelFilterSet):
    class Meta(object):
        model = CITask
        fields = ['id','Project','TaskType','Schedule']

class CIPassRateFilterSet(ModelFilterSet):
    class Meta(object):
        model = CIPassRateStatisticsDaily
        fields = ['id','ProjectID','TaskID','StepID','Rate','StatisticsDate']
        