# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from teamvision.ci.models import AutoCase, AutoCaseResult, AutoTestingTaskResult, ServiceHost, UnitTestCaseResult
from teamvision.project.models import Project
from business.ci.ci_task_history_service import CITaskHistoryService


class AutoCaseSerializer(serializers.ModelSerializer):
    AutoCaseName = serializers.SerializerMethodField()
    CaseTypeName = serializers.SerializerMethodField()
    ProjectName = serializers.SerializerMethodField()

    def get_AutoCaseName(self, obj):
        return obj.PackageName + "." + obj.ClassName + '.' + obj.CaseName

    def get_CaseTypeName(self, obj):
        result = "--"
        if obj.CaseType == 1:
            result = "API"

        if obj.CaseType == 2:
            result = "WebUI"
        return result

    def get_ProjectName(self, obj):
        result = "--"
        if obj.ProjectID:
            project = Project.objects.get(obj.ProjectID)
            if project:
                result = project.PBTitle
        return result

    class Meta:
        model = AutoCase
        exclude = ('CreateTime',)
        read_only_fields = ('id',)


class AutoCaseResultSerializer(serializers.ModelSerializer):
    TestCaseName = serializers.SerializerMethodField()
    CaseDesc = serializers.SerializerMethodField()
    ResultFormat = serializers.SerializerMethodField()
    Exception = serializers.SerializerMethodField()
    FailCategory = serializers.SerializerMethodField()

    def get_CaseDesc(self, obj):
        result = "--"
        if obj.TestCaseID:
            auto_test_case = AutoCase.objects.get(obj.TestCaseID)
            if auto_test_case:
                result = auto_test_case.Desc
            else:
                result = '--'
        return result

    def get_ResultFormat(self, obj):
        result = "--"
        if obj.Result:
            if obj.Result == 3:
                result = "Pass"
            elif obj.Result == 2:
                result = "Fail"
            else:
                result = "Aborted"
        return result

    def get_Exception(self, obj):
        error = ""
        trace = ""
        if obj.Error:
            error = str(obj.Error)

        if obj.StackTrace:
            trace = str(obj.StackTrace)
        return error + "  " + trace

    def get_TestCaseName(self, obj):
        result = "--"
        if obj.TestCaseID:
            auto_test_case = AutoCase.objects.get(obj.TestCaseID)
            if auto_test_case:
                result = auto_test_case.ClassName + '.' + auto_test_case.CaseName
            else:
                result = '--'
        return result

    def get_FailCategory(self, obj):
        result = "--"
        if obj.FailCategoryID == 1:
            result = "服务无响应"

        if obj.FailCategoryID == 2:
            result = "断言失败"

        if obj.FailCategoryID == 3:
            result = "脚本异常"
        return result

    class Meta:
        model = AutoCaseResult
        exclude = ('CreateTime', 'IsActive')
        read_only_fields = ('id',)


class UnitTestCaseResultSerializer(serializers.ModelSerializer):
    class Meta:
        model = UnitTestCaseResult
        exclude = ('CreateTime', 'IsActive')
        read_only_fields = ('id',)


class AutoTestingTaskResultSerializer(serializers.ModelSerializer):
    CaseCountsByFailCategroy = serializers.SerializerMethodField()

    def get_CaseCountsByFailCategroy(self, obj):
        result = list()
        fail_counts = CITaskHistoryService.autocaseresult_failcount_by_category(obj.id)
        for fail_count in fail_counts:
            temp_data = dict()
            temp_data['name'] = self.get_FailCategoryName(int(fail_count.get('FailCategoryID', 0)))
            temp_data['y'] = fail_count.get('TotalCount', 0)
            result.append(temp_data)
        return result

    def get_FailCategoryName(self, FailCategoryID):
        result = "--"
        if FailCategoryID == 1:
            result = "服务无响应"

        if FailCategoryID == 2:
            result = "断言失败"

        if FailCategoryID == 3:
            result = "脚本异常"

        return result

    class Meta:
        model = AutoTestingTaskResult
        exclude = ('CreateTime', 'IsActive')
        read_only_fields = ('id',)


class ServiceHostSerializer(serializers.ModelSerializer):
    class Meta:
        model = ServiceHost
        exclude = ('CreateTime', 'IsActive')
        read_only_fields = ('id',)
