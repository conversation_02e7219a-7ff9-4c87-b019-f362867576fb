#coding=utf-8
"""
Created on 2016-10-12

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from teamvision.ci import models
import datetime


class CIRunSummayStatisticsSerializer(serializers.Serializer):
    chart_id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    version_id = serializers.IntegerField()
    chart_type = serializers.CharField()
    chart_title = serializers.CharField()
    chart_sub_title = serializers.CharField()
    xaxis = serializers.ListField()
    yaxis = serializers.ListField()
    tooltip = serializers.CharField()
    series_data = serializers.ListField()


class CIPassRateChartStatisticsSerializer(serializers.Serializer):
    chart_id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    version_id = serializers.IntegerField()
    chart_type = serializers.CharField()
    chart_title = serializers.Char<PERSON>ield()
    chart_sub_title = serializers.Char<PERSON>ield()
    xaxis = serializers.ListField()
    yaxis = serializers.ListField()
    tooltip = serializers.CharField()
    series_data = serializers.ListField()

class CILowPassRateChartStatisticsSerializer(serializers.Serializer):
    chart_id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    version_id = serializers.IntegerField()
    chart_type = serializers.CharField()
    chart_title = serializers.CharField()
    chart_sub_title = serializers.CharField()
    xaxis = serializers.ListField()
    yaxis = serializers.ListField()
    tooltip = serializers.CharField()
    series_data = serializers.ListField()


class CIRunStatisticsDailySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.CIRunStatisticsDaily
        exclude = ('CreateTime', 'IsActive')
        read_only_fields = ('id',)

class CIPassRateStatisticsDailySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.CIPassRateStatisticsDaily
        exclude = ('CreateTime', 'IsActive')
        read_only_fields = ('id',)
