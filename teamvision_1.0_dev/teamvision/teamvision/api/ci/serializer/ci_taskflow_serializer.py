#coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from teamvision.ci.models import CITaskFlow,CITaskHistory,CITaskFlowHistory,CITaskFlowSection,CITask,CIFlowSectionHistory,CIFlowSectionTaskMap
from teamvision.api.ci.serializer.ci_serializer import CITaskSerializer
from teamvision.api.ci.serializer import ci_serializer,ci_taskhistory_serializer
from business.auth_user.user_service import UserService
from business.ci.ci_task_parameter_service import  CITaskParameterService
from gatesidelib.datetimehelper import DateTimeHelper
import datetime


class CITaskFlowSectionSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()


    def get_ViewData(self,obj):
        result = dict()
        result['section_index'] = self.get_SectionIndex(obj)
        result['section_task_maps'] = self.get_section_tasks(obj)
        return result



    def get_section_tasks(self,obj):
        result = list()
        section_tasks = CIFlowSectionTaskMap.objects.get_section_task(obj.id)
        for task in section_tasks:
            temp_serlizer = CISectionTaskMapSerializer(instance=task)
            result.append(temp_serlizer.data)
        return result



    def get_SectionIndex(self,obj):
        result = 0
        flowSections = CITaskFlowSection.objects.flow_sections(obj.TaskFlow)
        index =0
        for section in flowSections:
            if obj.id == section.id:
                result = index+1
                break;
            index= index+1
        return result


    class Meta:
        model = CITaskFlowSection
        exclude = ('IsActive','CreateTime')
        read_only_fields = ('id',)


class CITaskFlowListSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()


    def get_ViewData(self,obj):
        result = dict()
        result['last_runtime'] = self.get_last_runtime(obj)
        result['sections'] = self.sections(obj)
        result['max_order'] = self.get_MaxOrder(obj)

        return result


    def get_last_runtime(self,obj):
        if obj.LastRunTime:
            return str((obj.LastRunTime+datetime.timedelta(hours=8)))[:19]
        else:
            return obj.LastRunTime

    def get_MaxOrder(self,obj):
        result = 1
        flowSections = CITaskFlowSection.objects.flow_sections(obj.id).order_by('-SectionOrder')
        if len(flowSections)>0:
            result = flowSections[0].SectionOrder
        return result


    def sections(self,obj):
        result = []
        sections = CITaskFlowSection.objects.flow_sections(obj.id)
        for section in sections:
            temp = CITaskFlowSectionSerializer(instance=section)
            result.append(temp.data)
        return result


    class Meta:
        model = CITaskFlow
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'LastRunTime': {'required': False},'Description': {'required': False}}


class CITaskFlowSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()



    def get_ViewData(self,obj):
        result = dict()
        result['max_order'] = self.get_MaxOrder(obj)
        result['sections'] = self.sections(obj)
        return result


    def get_MaxOrder(self,obj):
        result = 1
        flowSections = CITaskFlowSection.objects.flow_sections(obj.id).order_by('-SectionOrder')
        if len(flowSections)>0:
            result = flowSections[0].SectionOrder
        return result


    def sections(self,obj):
        result = []
        sections = CITaskFlowSection.objects.flow_sections(obj.id)
        for section in sections:
            temp = CITaskFlowSectionSerializer(instance=section)
            result.append(temp.data)
        return result

    class Meta:
        model = CITaskFlow
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'LastRunTime': {'required': False},'Description': {'required': False}}


class CITaskFlowHistorySerializer(serializers.ModelSerializer):
    SectionHistories = serializers.SerializerMethodField()
    StartedUser = serializers.SerializerMethodField()


    def get_SectionHistories(self,obj):
        result =list()
        flow_section_histories = CIFlowSectionHistory.objects.flow__section_history(obj.id)
        for section_history in flow_section_histories:
            section_history_serializer = CIFlowSectionHistorySerializer(instance=section_history)
            result.append(section_history_serializer.data)
        return result

    def get_StartedUser(self, obj):
        result = '系统'
        user = UserService.get_user(obj.Trigger)
        if user:
            result = user.last_name + user.first_name
            result=result[-2:]
        return result


    class Meta:
        model = CITaskFlowHistory
        exclude = ('CreateTime','IsActive')
        read_only_fields = ('id',)


class CIBranchHistorySerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()


    def get_ViewData(self,obj):
        result = dict()
        result["sections"] = self.get_Sections(obj)
        return result


    def get_Sections(self,obj):
        result =list()
        flow_sections = CITaskFlowSection.objects.flow_sections(obj.TaskFlow).order_by('SectionOrder')
        for section in flow_sections:
            section.FlowHistoryID = obj.id
            section_serializer = CIBranchSectionSerializer(instance=section)
            result.append(section_serializer.data)
        return result

    class Meta:
        model = CITaskFlowHistory
        exclude = ('CreateTime','IsActive')
        read_only_fields = ('id',)

class CIBranchSectionSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()


    def get_ViewData(self,obj):
        result = dict()
        result['section_index'] = self.get_SectionIndex(obj)
        result['section_task_maps'] = self.get_section_tasks(obj)
        result['lastest_section_history'] = self.get_section_lastest_history(obj)
        return result


    def get_section_lastest_history(self,obj):
        result = None
        section_histories = CIFlowSectionHistory.objects.all().filter(TaskFlowHistory=obj.FlowHistoryID).filter(Section=obj.id).order_by('-id')
        if len(section_histories) >0:
            lastest_history = section_histories[0]
            result = CIFlowSectionHistorySerializer(instance=lastest_history).data
        return result

    def get_section_tasks(self,obj):
        result = list()
        section_tasks = CIFlowSectionTaskMap.objects.get_section_task(obj.id)
        for task in section_tasks:
            temp_serlizer = CISectionTaskMapSerializer(instance=task)
            result.append(temp_serlizer.data)
        return result



    def get_SectionIndex(self,obj):
        result = 0
        flowSections = CITaskFlowSection.objects.flow_sections(obj.TaskFlow)
        index =0
        for section in flowSections:
            if obj.id == section.id:
                result = index+1
                break;
            index= index+1
        return result


    class Meta:
        model = CITaskFlowSection
        exclude = ('IsActive','CreateTime')
        read_only_fields = ('id',)


class CIFlowSectionHistorySerializer(serializers.ModelSerializer):
    TaskHistories = serializers.SerializerMethodField()
    SectionName = serializers.SerializerMethodField()
    SectionIndex = serializers.SerializerMethodField()
    Duration = serializers.SerializerMethodField()

    def get_TaskHistories(self,obj):
        result = list()
        task_histories = CITaskHistory.objects.get_history_by_sechistory(obj.id,is_active=0)
        for task_history in task_histories:
            task_history_serializer = ci_taskhistory_serializer.CITaskHistorySerializer(instance=task_history)
            result.append(task_history_serializer.data)
        return result

    def get_SectionName(self,obj):
        result = '默认'
        section = CITaskFlowSection.objects.get(obj.Section)
        if section:
            result = section.SectionName
        return result

    def get_Duration(self,obj):
        result = "--"
        if obj.StartTime is not None and obj.EndTime is not None:
            durations = (obj.EndTime - obj.StartTime).total_seconds()
            result = int(durations / 60)
            if result == 0:
                result = str(durations) + "秒"
            else:
                result = str(result) + "分钟"
        return result




    def get_SectionIndex(self,obj):
        result = 0
        flowSections = CITaskFlowSection.objects.flow_sections(obj.TaskFlow)
        index =0
        for section in flowSections:
            if obj.Section == section.id:
                result = index+1
                break;
            index= index+1
        return result


    class Meta:
        model = CIFlowSectionHistory
        exclude = ('CreateTime','IsActive')
        read_only_fields = ('id',)


class CISectionTaskMapSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()


    def get_ViewData(self,obj):
        result = dict()
        result['ci_task_name'] = self.get_ci_task_name(obj)
        result['parameter_groups'] = self.get_parameter_groups(obj)
        result['parameter_group_name'] = self.get_parameter_group_name(obj)
        return result


    def get_ci_task_name(self,obj):
        result = obj.CITaskID
        ci_task = CITask.objects.get(obj.CITaskID)
        if ci_task is not None:
            result = ci_task.TaskName

        return result


    def get_parameter_group_name(self,obj):
        result = obj.CITaskParameter
        parameter_group = CITaskParameterService.task_parameter(obj.CITaskParameter)
        if parameter_group is not None:
            result = parameter_group.group_name
        return result

    def get_parameter_groups(self,obj):
        parameter_groups = CITaskParameterService.task_parameter_list(obj.CITaskID)
        result = list()
        for item in parameter_groups:
            temp = dict()
            temp["id"] = str(item.id)
            temp["label"] = item.group_name
            result.append(temp)
        return result


    class Meta:
        model = CIFlowSectionTaskMap
        exclude = ('CreateTime',)
        read_only_fields = ('id',)

           

