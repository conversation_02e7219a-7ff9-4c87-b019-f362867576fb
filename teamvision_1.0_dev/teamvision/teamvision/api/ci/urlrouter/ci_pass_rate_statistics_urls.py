# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.ci.views import ci_pass_rate_statistics_view

ci_pass_rate_statistics_router = [
    re_path(r"pass_rate_statistics/(?P<id>\d{1,6})", ci_pass_rate_statistics_view.CIPassRateStatisticsDailyView.as_view()),
    re_path(r"pass_rate_statistics/create", ci_pass_rate_statistics_view.CIPassRateStatisticsDailyCreateView.as_view()),
    re_path(r"task/step/(?P<step_id>.+)/passrate_trend", ci_pass_rate_statistics_view.CIPassRateStatisticsChartDailyView.as_view()),
]
