#coding=utf-8
# coding=utf-8
"""
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
"""

from teamvision.api.ci.urlrouter import ci_task_basic_urls
# from teamvision.api.ci.urlrouter.ci_server_urls import deploy_server_router
from teamvision.api.ci.urlrouter.ci_crendentials_urls import ci_crendentials_router
from teamvision.api.ci.urlrouter.ci_task_plugin_urls import task_plugin_router
from teamvision.api.ci.urlrouter.auto_testing_urls import auto_testing_result_router
from teamvision.api.ci.urlrouter.case_tag_urls import case_tag_router
# from teamvision.api.ci.urlrouter.ci_task_basic_urls import ci_task_basic_router
from teamvision.api.ci.urlrouter.ci_task_log_urls import ci_task_log_router
from teamvision.api.ci.urlrouter.ci_run_statistics_urls import ci_run_statistics_router
from teamvision.api.ci.urlrouter.ci_pass_rate_statistics_urls import ci_pass_rate_statistics_router







urlpatterns = ci_task_basic_urls.api_task_router+ci_task_basic_urls.task_history_router+ci_task_basic_urls.api_task_router
urlpatterns=urlpatterns+ci_task_log_router
urlpatterns=urlpatterns+ci_crendentials_router
urlpatterns=urlpatterns+task_plugin_router+auto_testing_result_router+case_tag_router+ci_task_basic_urls.ci_task_basic_router
urlpatterns=urlpatterns + ci_run_statistics_router + ci_pass_rate_statistics_router
