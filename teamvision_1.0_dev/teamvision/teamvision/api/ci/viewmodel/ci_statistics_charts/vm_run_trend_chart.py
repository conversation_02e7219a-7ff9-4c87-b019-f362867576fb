# coding=utf-8
"""
Created on 2018-01-09

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.project.models import ProjectIssue
from teamvision.api.project.viewmodel.project_statistics_charts.vm_highchart import VM_HighChart
from business.ci.ci_run_statistics_service import CIStatisticsService


class CIRunSummaryTrendChart(VM_HighChart):
    """
    classdocs
    """
    def __init__(self,project_id,date_range=30):
        """
        Constructor
        """
        VM_HighChart.__init__(CIRunSummaryTrendChart,project_id,0)
        self.chart_id=1
        self.chart_type="spline"
        self.chart_title="构建趋势"
        self.chart_sub_title="总趋势"
        self.date_range= date_range
        self.xaxis=self.chart_xaxis()
        self.yaxis=self.chart_yaxis()
        self.yaxis_title=""
        self.tooltip=self.chart_tooltip()
        self.series_data=self.series()
    
    def chart_xaxis(self):
        result=list()
        last30_days_data=CIStatisticsService.run_trend_last30days(self.project_id,self.date_range)
        for data in last30_days_data:
            result.append(data.get('StatisticsDate'))
        return result
    
    def chart_yaxis(self):
        result=list()
        return result
    
    def chart_tooltip(self):
        return ""
    
    def series(self):
        result=list()
        total_trend=self.get_run_trend("total")
        success_trend = self.get_run_trend("success")
        fail_trend = self.get_run_trend("fail")
        result.append(total_trend)
        result.append(success_trend)
        result.append(fail_trend)
        return result
    
    def get_run_trend(self,name):
        result=dict()
        result['name']= name
        result['data']=list()
        last30_days_data=CIStatisticsService.run_trend_last30days(self.project_id,self.date_range)
        for data in last30_days_data:
            if name == "total":
                result['data'].append(data.get('TotalToday'))
            if name == "success":
                result['data'].append(data.get('SuccessToday'))
                result['color'] = 'green'
            if name == "fail":
                result['data'].append(data.get('FailToday'))
                result['color'] = '#f15c80'
        return result
        
            
        