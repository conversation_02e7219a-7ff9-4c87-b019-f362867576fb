# coding=utf-8
"""
Created on 2018-01-09

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.project.models import ProjectIssue
from teamvision.api.project.viewmodel.project_statistics_charts.vm_highchart import VM_HighChart
from business.ci.ci_run_statistics_service import CIStatisticsService
from business.ci.ci_task_config_service import CITaskConfigService


class CIStepPassRateTrendChart(VM_HighChart):
    """
    classdocs
    """

    def __init__(self, step_id):
        """
        Constructor
        """
        VM_HighChart.__init__(CIStepPassRateTrendChart, 0, 0)
        self.step_id = step_id
        self.chart_id = 1
        self.chart_type = "spline"
        self.chart_title = "通过率趋势"
        self.chart_sub_title = "最近15天趋势"
        self.date_range = ""
        self.xaxis = self.chart_xaxis()
        self.yaxis = self.chart_yaxis()
        self.yaxis_title = ""
        self.tooltip = self.chart_tooltip()
        self.series_data = self.series()

    def chart_xaxis(self):
        result = list()
        last15_days_data = CIStatisticsService.step_passrate_trend(self.step_id)
        for data in last15_days_data:
            result.append(data.StatisticsDate)
        return result

    def chart_yaxis(self):
        result = list()
        return result

    def chart_tooltip(self):
        return ""

    def series(self):
        result = list()
        step = CITaskConfigService.task_step(self.step_id)
        if step:
            passrate_trend = self.get_run_trend(step.step_config["step_name"])
            result.append(passrate_trend)
        return result

    def get_run_trend(self, name):
        result = dict()
        result['name'] = name
        result['data'] = list()
        last30_days_data = CIStatisticsService.step_passrate_trend(self.step_id)
        for data in last30_days_data:
            result['data'].append(data.Rate)
        return result
