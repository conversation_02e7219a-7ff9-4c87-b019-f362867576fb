#coding=utf-8
# coding=utf-8
"""
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
"""
from rest_framework import generics,response,status,settings
from teamvision.api.ci.serializer import autotesting_serializer
from rest_framework.permissions import AllowAny
from teamvision.ci.models import AutoCase,CaseTag
from teamvision.project.models import ProjectXmindTopic,ProjectXmindTopicTagMap
from business.project.project_mindmap_service import MindmapService
from teamvision.api.ci.filters.ci_pagination import CIPagination
from teamvision.api.ci.filters.auto_case_filter import AutoCaseFilterSet
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication



class AutoCaseView(generics.RetrieveUpdateDestroyAPIView):
    """
    An endpoint for users to view and update their profile information.
    """
    serializer_class = autotesting_serializer.AutoCaseSerializer
    permission_classes=[AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    

    def get_object(self):
        result_id =int(self.kwargs['id'])
        return AutoCase.objects.get(result_id)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}
        MindmapService.add_auto_tag_by_import_case(instance)
        return response.Response(serializer.data)


class AutoCaseListView(generics.ListCreateAPIView):
    """
    /api/ci/auto_cases?ProjectID=1446: return case with ProjectID 1446
    /api/ci/auto_cases?id__in=1,2: return case with id 1 and 2
    /api/ci/auto_cases return all
    """
    serializer_class = autotesting_serializer.AutoCaseSerializer
    permission_classes = [AllowAny]
    queryset = AutoCase.objects.all().filter(IsActive=1)
    pagination_class = CIPagination

    def get_queryset(self):

        query_data = self.request.GET.copy()
        if "Casename" in self.request.headers:
            get_casename = {'Casename':self.request.headers['Casename']}
            query_data.update(get_casename)
        case_tags = self.request.GET.get('CaseTag',None)
        if case_tags is not None and len(eval(case_tags)) > 0 and 0 not in eval(case_tags):
            topics = ProjectXmindTopic.objects.all().filter(Priority__in=eval(case_tags))
            case_keys = [topic.OriginalID for topic in topics]
            qs = super(AutoCaseListView, self).get_queryset().filter(TestCaseKey__in=case_keys)
        else:
            qs = super(AutoCaseListView, self).get_queryset()
        return AutoCaseFilterSet(data=query_data, queryset=qs).filter()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        MindmapService.add_auto_tag_by_import_case(serializer.instance)
        return response.Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class AutoCaseClearProjectCaseView(generics.DestroyAPIView):
    """
    /api/ci/auto_cases/delete/<project_id>?CaseType=1   delete project auto cases with case type
    """
    serializer_class = autotesting_serializer.AutoCaseSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def destroy(self, request, *args, **kwargs):
        result = dict()
        result["DeleteCaseCounts"] =0
        project_id = self.kwargs.get("project_id",0)
        case_type = self.request.GET.get("CaseType", None)
        if str(project_id) !="0":
            auto_cases = AutoCase.objects.get_by_project(project_id,case_type)
            result["DeleteCaseCounts"] = len(auto_cases)
            test_casekeys = [case.TestCaseKey for case in auto_cases]
            testcase_tags = ProjectXmindTopicTagMap.objects.all().filter(OriginalID__in=test_casekeys)
            testcase_tags.delete()
            auto_cases.delete()
        return response.Response(result,status=status.HTTP_204_NO_CONTENT)

    



