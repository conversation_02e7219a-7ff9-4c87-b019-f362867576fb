#coding=utf-8
# coding=utf-8
"""
Created on 2014-1-5

@author: zhangtian<PERSON>
"""
from rest_framework import generics,status,response
from teamvision.api.ci.serializer import ci_serializer
from rest_framework.permissions import AllowAny
from teamvision.ci.models import CaseTag
from teamvision.project.models import ProjectXmindTopicTagMap
from teamvision.api.ci.filters.case_tag_filter import CaseTagFilterSet
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication


class CaseTagView(generics.RetrieveUpdateDestroyAPIView):
    """
    An endpoint for users to view and update their profile information.
    """
    serializer_class = ci_serializer.CICaseTagSerializer
    permission_classes=[AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    

    def get_object(self):
        result_id =int(self.kwargs['id'])
        return CaseTag.objects.get(result_id)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance is not None:
            tag_maps = ProjectXmindTopicTagMap.objects.filter(TagID=instance.id)
            tag_maps.delete()
        instance.delete()
        return response.Response(status=status.HTTP_204_NO_CONTENT)



class CaseTagListView(generics.ListCreateAPIView):
    """
    get /api/ci/case/tags
        FilterSet: id, ProjectID,TagType  FilterOperation:=,__in,__gt,__contains,__icontains,Range__in,__lt,!=,__isnull
    """
    serializer_class = ci_serializer.CICaseTagSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes=[AllowAny]
    queryset=CaseTag.objects.all()
    
    def get_queryset(self):
        qs = super(CaseTagListView, self).get_queryset()
        return CaseTagFilterSet(data=self.request.GET, queryset=qs).filter()
    



