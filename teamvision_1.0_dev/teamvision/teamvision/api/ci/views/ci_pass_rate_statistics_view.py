# coding=utf-8
# coding=utf-8
"""
Created on 2019-12-24

@author: lusiyuan
"""
from rest_framework import generics, status, response
from teamvision.api.ci.serializer import ci_statistics_serializer
from rest_framework.permissions import AllowAny
from business.ci.ci_run_statistics_service import CIStatisticsService
from teamvision.ci.models import CIPassRateStatisticsDaily
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication
from teamvision.api.ci.viewmodel.ci_statistics_charts.vm_run_trend_chart import CIRunSummaryTrendChart
from teamvision.api.ci.viewmodel.ci_statistics_charts.vm_step_passrate_trend_chart import CIStepPassRateTrendChart
from teamvision.api.ci.filters.ci_task_filter import CIPassRateFilterSet


class CIPassRateStatisticsDailyView(generics.RetrieveUpdateDestroyAPIView):
    """
    get:
         path:/api/ci/pass_rate_statistics/<id>/
         id:taskid
    put:
         path:/api/ci/pass_rate_statistics/<id>/
         id:taskid
    """
    serializer_class = ci_statistics_serializer.CIPassRateStatisticsDailySerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def get_object(self):
        id = int(self.kwargs.get('id', 0))
        statictics_data = CIPassRateStatisticsDaily.objects.get(id)
        return statictics_data


class CIPassRateStatisticsDailyCreateView(generics.ListCreateAPIView):
    """
    get:
         path:/api/ci/pass_rate_statistics/create/
         id:taskid
    post:
         path:/api/ci/pass_rate_statistics/create/
         id:taskid
    """
    serializer_class = ci_statistics_serializer.CIPassRateStatisticsDailySerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def get_queryset(self):
        statictics_data = CIPassRateStatisticsDaily.objects.all()
        return CIPassRateFilterSet(data=self.request.GET, queryset=statictics_data).filter()


class CIPassRateStatisticsChartDailyView(generics.RetrieveAPIView):
    """
    get:
         path:/api/ci/task_basic/step/<step_id>/passrate_trend

    """
    serializer_class = ci_statistics_serializer.CIPassRateChartStatisticsSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def get_object(self):
        step_id = self.kwargs.get('step_id')
        CIStatisticsService.task_lowrate_trend(self.request)
        chart = CIStepPassRateTrendChart(step_id)
        return chart
