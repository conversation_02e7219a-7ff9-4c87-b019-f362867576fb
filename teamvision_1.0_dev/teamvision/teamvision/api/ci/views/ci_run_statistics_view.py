# coding=utf-8
# coding=utf-8
"""
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
"""
from rest_framework import generics, status, response
from teamvision.api.ci.serializer import ci_statistics_serializer
from rest_framework.permissions import AllowAny
from business.ci.ci_run_statistics_service import CIStatisticsService

from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication
from teamvision.api.ci.viewmodel.ci_statistics_charts.vm_run_trend_chart import CIRunSummaryTrendChart


class CIRunStatisticsDailyView(generics.RetrieveAPIView):
    """
    get:
         path:/api/ci/run_statistics/(?P<project_id>.+)/daily30
         id:project_id
    put:
         path:/api/ci/run_statistics/(?P<project_id>.+)/daily30
         id:project_id
    """
    serializer_class = ci_statistics_serializer.CIRunSummayStatisticsSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def get_object(self):
        project_id = self.kwargs.get('project_id', 0)
        create_daterange = self.request.GET.get('CreationTime', None)
        chart = CIRunSummaryTrendChart(project_id, create_daterange)
        return chart


class CIRunStatisticsDailyCreateView(generics.CreateAPIView):
    """
    get:
         path:/api/ci/task_basic/<id>/
         id:taskid
    put:
         path:/api/ci/task_basic/<id>/
         id:taskid
    """
    serializer_class = ci_statistics_serializer.CIRunStatisticsDailySerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def create(self, request, *args, **kwargs):
        instance = CIStatisticsService.updateStatisticsData(request.data)
        serializer = ci_statistics_serializer.CIRunStatisticsDailySerializer(instance)
        headers = self.get_success_headers(serializer.data)
        return response.Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
