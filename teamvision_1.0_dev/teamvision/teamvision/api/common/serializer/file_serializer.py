# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from teamvision.home.models import FileInfo
from teamvision.settings import TIME_ZONE
from teamvision.ci.models import CITask
from business.ci.ci_task_queue_service import CITQService
import datetime
from gatesidelib.datetimehelper import DateTimeHelper
import pytz


class FileSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = FileInfo
        exclude = ('IsActive',)
        read_only_fields = ('id',)
