# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from django.contrib.auth.models import User,Group
from teamvision.project.models import  ProductSpaceUser,ProductSpace
from teamvision.auth_extend.user.models import UserExtend
from business.ucenter.account_service import AccountService
from business.auth_user.user_service import UserService

class UserSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    system_permision = serializers.SerializerMethodField()
    system_role_label = serializers.SerializerMethodField()
    extend_info = serializers.SerializerMethodField()
    is_space_admin = serializers.SerializerMethodField()

    def get_avatar(self,obj):
        return AccountService.get_avatar_url(obj)

    def get_system_permision(self,obj):
        return UserService.get_system_permission(obj.id)

    def get_is_space_admin(self,obj):
        return UserService.is_space_admin(obj.id)

    def get_name(self, obj):
        return obj.last_name+obj.first_name


    def get_system_role_label(self,obj):
        result = 'U'
        for user_group in obj.groups.get_queryset():
            group=Group.objects.get(id=user_group.id)
            result = group.name[0]
        return result

    def get_extend_info(self,obj):
        reuslt = dict()
        if obj.extend_info is not None:
            serlizer = UserExtendSerializer(obj.extend_info)
            result = serlizer.data
        return result


    class Meta:
        model = User
        exclude = ('password',)
        read_only_fields = ('id',)


class UserExtendSerializer(serializers.ModelSerializer):

    class Meta:
        model = UserExtend
        fields = '__all__'
        read_only_fields = ('id',)
        
        
        