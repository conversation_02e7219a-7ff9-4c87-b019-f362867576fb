#coding=utf-8
# coding=utf-8
"""
Created on 2014-1-5

@author: zhang<PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.common.views import dic_config_view


api_dicconfig_router =[
    re_path(r"dicconfig/(?P<type_id>.+)/dicconfigs$",dic_config_view.DicConfigListView.as_view()),
    re_path(r"dicconfig/(?P<type_id>.+)/(?P<value>.+)$",dic_config_view.DicConfigView.as_view()),
    re_path(r"dicconfig/all_configs/$",dic_config_view.DicTypeListView.as_view()),
                  ]
