#coding=utf-8
# coding=utf-8
"""
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.common.views import task_queue_view


api_task_queue_router =[
                  re_path(r"task_queue/(?P<id>.+)/done$",task_queue_view.TaskQueueDoneView.as_view()),
                  re_path(r"task_queue/(?P<id>.+)$",task_queue_view.TaskQueueView.as_view()),
                  re_path(r"task_queues$",task_queue_view.TaskQueueListView.as_view()),
                  ]