# coding=utf-8
"""
Created on 2025-02-19

@author: zhangpeng
"""

from teamvision.project import models
from django_filters import rest_framework as filters
from teamvision.documents.models import DocumentDir, DocumentFile
from django_filters import BaseInFilter, NumberFilter
import django_filters


class NumberInFilter(BaseInFilter, NumberFilter):
    pass


class DocumentDirFilter(filters.FilterSet):
    id__in = NumberInFilter(field_name='id', lookup_expr='in')
    create_time = filters.DateFromToRangeFilter(field_name='create_time')

    class Meta:
        model = DocumentDir
        fields = ['id', 'create_time']


class DocumentFileFilter(filters.FilterSet):
    id__in = NumberInFilter(field_name='id', lookup_expr='in')
    create_id = NumberInFilter(field_name='create_id', lookup_expr='in')
    title = django_filters.CharFilter(field_name='title', lookup_expr='icontains')
    create_time = filters.DateFromToRangeFilter(field_name='create_time')

    class Meta:
        model = DocumentFile
        fields = ['id', 'title', 'dir_id', 'create_id', 'create_time']
