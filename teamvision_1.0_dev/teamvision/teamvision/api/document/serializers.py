# coding=utf-8
"""
@Create: 2025/2/13 14:04
@Author: zhangpeng
"""

from rest_framework import serializers
from business.auth_user.user_service import UserService
from teamvision.documents.models import DocumentDir, DocumentFile


class DirectorySerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    subdirectories = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    documents = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    children = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        data = dict()
        data["create_username"] = UserService.get_name_by_id(obj.create_id)
        return data

    def get_children(self, obj):
        children = DocumentDir.objects.filter(parent_id=obj.id)
        return DirectorySerializer(children, many=True).data

    class Meta:
        model = DocumentDir
        fields = ['id', 'name', 'description', 'parent_id', 'subdirectories', 'documents', 'create_time', 'view_data', 'product_space',
                  'children']


class DirectoryTreeSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    subdirectories = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    documents = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    children = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        data = dict()
        data["create_username"] = UserService.get_name_by_id(obj.create_id)
        return data

    def get_children(self, obj):
        children = DocumentDir.objects.filter(parent_id=obj.id).filter(is_active=True)
        return DirectoryTreeSerializer(children, many=True).data

    class Meta:
        model = DocumentDir
        fields = ['id', 'name', 'parent_id', 'subdirectories', 'documents', 'create_time', 'view_data', 'product_space', 'children',
                  'description']


class DocumentSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        data = dict()
        data["create_username"] = UserService.get_name_by_id(obj.create_id)
        data["update_username"] = UserService.get_name_by_id(obj.update_id)
        return data

    class Meta:
        model = DocumentFile
        fields = ['id', 'title', 'content', 'dir_id', 'create_time', 'update_time', 'create_id', 'update_id', 'view_data']
