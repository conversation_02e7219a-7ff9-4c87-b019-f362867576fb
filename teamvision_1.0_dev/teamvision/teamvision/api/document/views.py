# coding=utf-8
"""
@Create: 2025/2/13 14:09
@Author: zhangpeng
"""

from teamvision.documents.models import DocumentDir, DocumentFile
from .filter import DocumentFileFilter, DocumentDirFilter
from .pagination import DocumentPagination, DocumentDirPagination
from .serializers import DirectorySerializer, DocumentSerializer, DirectoryTreeSerializer
from rest_framework.generics import ListCreateAPIView, RetrieveUpdateDestroyAPIView
from django_filters import rest_framework as filters
from rest_framework import generics, status, response
from rest_framework.permissions import AllowAny
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication
from rest_framework.response import Response


class DirectoryView(RetrieveUpdateDestroyAPIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    serializer_class = DirectorySerializer

    def get_object(self):
        dir_id = self.kwargs.get('dir_id', 0)
        dir_inst = DocumentDir.objects.get(id=dir_id)
        return dir_inst

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_active = False
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class DirectoryListView(ListCreateAPIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    queryset = DocumentDir.objects.all().filter(parent_id=0).filter(is_active=True)
    serializer_class = DirectoryTreeSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = DocumentDirFilter
    pagination_class = DocumentDirPagination

    def post(self, request, *args, **kwargs):
        request.data['create_id'] = request.user.id
        request.data["product_space"] = request.data["ProductSpace"]
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class DocumentView(RetrieveUpdateDestroyAPIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    serializer_class = DocumentSerializer

    def get_object(self):
        doc_id = self.kwargs.get('doc_id', 0)
        doc_inst = DocumentFile.objects.get(id=doc_id)
        return doc_inst

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_active = False
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class DocumentListView(ListCreateAPIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    queryset = DocumentFile.objects.all().filter(is_active=True).order_by('-update_time')
    serializer_class = DocumentSerializer
    filterset_class = DocumentFileFilter
    pagination_class = DocumentPagination

    def post(self, request, *args, **kwargs):
        request.data['create_id'] = request.user.id
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

# class IssueView(generics.RetrieveUpdateDestroyAPIView, generics.CreateAPIView):
#     """
#     /api/project/issue/issue_id
#     update, get, delete issue with issue_id
#     """
#     serializer_class = DocumentSerializer
#     permission_classes = [AllowAny]
#     authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
#
#     def get_object(self):
#         issue_id = self.kwargs.get('issue_id', 0)
#         issue = models.ProjectIssue.objects.get(issue_id)
#         return issue
#
#     def patch(self, request, *args, **kwargs):
#         issue = self.get_object()
#         operation_type = request.data.get('operation', 0)
#         reslove_result = request.data.get('ResloveResult', 1)
#         processor = request.data.get("Processor", None)
#
#         desc = request.data.get("Desc", None)
#         comment = ""
#         if desc is not None:
#             # IssueService.update_issue_desc(issue, desc, request.user)
#             comment = desc
#
#         status_desc = request.data.get('status_desc')
#         if status_desc is not None and status_desc != "":
#             comment = status_desc
#
#         return self.partial_update(request, *args, **kwargs)
