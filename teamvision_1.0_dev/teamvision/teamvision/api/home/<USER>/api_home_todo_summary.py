# coding=utf-8
"""
Created on 2016-8-24

@author: <PERSON><PERSON><PERSON><PERSON>
"""
from business.project.task_service import TaskService
from business.project.fortesting_service import ForTestingService
from business.project.project_service import ProjectService
from teamvision.project.models import ProjectIssue, ProjectTestPlan, ProjectTestPlanCase


class HomeToDoSummary(object):
    """
    classdocs
    """

    def __init__(self, request):
        """
        Constructor
        """
        self.request = request
        self.all_task_count, self.todo_task_count = self.task_count()
        self.todo_issue_count, self.create_issue_count = self.issue_count()
        self.todo_fortesting_count, self.all_fortesting_count = self.fortesting_count()
        self.my_testplan_count, self.testplan_case_count = self.testplan_count()

    def task_count(self):
        all_my_tasks = TaskService.all_my_tasks(self.request, self.request.user.id)
        all_my_tasks = all_my_tasks.filter(Status__in=(0, 1, 2, 3)).filter(Parent=None)
        todo_my_tasks = all_my_tasks.filter(Status__in=(0, 1)).filter(Parent=None)
        return len(all_my_tasks), len(todo_my_tasks)

    def issue_count(self):
        all_my_issue = ProjectIssue.objects.get_processor_issue(self.request.user.id)
        all_my_issue = all_my_issue.filter(Status__in=(2, 4, 5))
        create_my_issue = ProjectIssue.objects.get_reporter_issue(self.request.user.id)
        return len(all_my_issue), len(create_my_issue)

    def fortesting_count(self):
        my_projects = ProjectService.get_projects_include_me(self.request)
        my_project_ids = [project.id for project in my_projects]
        my_fortesting = ForTestingService.get_projects_fortestings(my_project_ids).filter(
            Testers=self.request.user.id).filter(Status__in=(1, 2, 3))
        all_fortesting = ForTestingService.get_projects_fortestings(my_project_ids).filter(Testers=self.request.user.id)
        return len(my_fortesting), len(all_fortesting)

    def testplan_count(self):
        my_testplans = ProjectTestPlan.objects.get_creator_plans(self.request.user.id)
        my_testplan_ids = [testplan.id for testplan in my_testplans]
        testplan_cases = ProjectTestPlanCase.objects.get_my_plan_cases(my_testplan_ids)
        return len(my_testplans), len(testplan_cases)
