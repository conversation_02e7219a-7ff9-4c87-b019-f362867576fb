# coding=utf-8
"""
Created on 2016-12-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from teamvision.project import models
from url_filter.filtersets.django import ModelFilterSet
from django_filters import rest_framework as filters
import django_filters


class ProjectTaskFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.Task
        fields = ['ProjectID', 'Status']


class IssueStatisticsFilter(filters.FilterSet):
    s_time = django_filters.DateTimeFilter(field_name='CreationTime', lookup_expr='gte')
    e_time = django_filters.DateTimeFilter(field_name='CreationTime', lookup_expr='lte')

    class Meta:
        model = models.ProjectIssue
        fields = ['s_time', 'e_time', ]
