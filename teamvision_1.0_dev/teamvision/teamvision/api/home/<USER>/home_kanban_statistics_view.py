# coding=utf-8
"""
Created on 2021-01-11
@author: rain
"""

import datetime
from rest_framework import generics, response
from rest_framework.permissions import AllowAny
from teamvision.api.home.serializer import home_statistics_serializer
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_autocase_count_chart import \
    VM_ProjectAutoCaseCountChart
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_testcase_count_chart import \
    VM_ProjectTestCaseCountChart
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_testcase_repeatratechart import \
    VM_ProjectTestCaseRepeatrateChart
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_testplan_count_chart import \
    VM_ProjectTestPlanCountChart
from rest_framework.views import APIView
from teamvision.crontab.models import CrontabTestCaseGrowthTrend, CrontabAutoCaseGrowthTrend, \
    CrontabTestCaseSumGrowthTrend, CrontabAutoCaseSumGrowthTrend, CrontabCaseSumGrowthTrend, CrontabTestPlanCaseTrend
from collections import defaultdict
from business.project.project_service import ProjectService
from business.project.project_testcase_service import TestCaseService
from teamvision.project import models
from teamvision.project.models import ProjectTestPlan, ProjectTestPlanCase
from business.common.datetime_service import DateTimeService
from django.db.models import Sum, Avg, FloatField
from django.db.models.functions import Coalesce
from django.db.models import Q

from utils.utils import get_year_week, calculate_date_diff


class TestPlanStaticticsView(APIView):
    """
    /api/home/<USER>/testplan/7
    获取测试计划分析表
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        project_test_plan_list = ProjectTestPlan.objects.filter(IsActive=1).filter(Project__in=project_ids).filter(
            CreationTime__range=(start_time, end_time))
        require_num_list = project_test_plan_list.values_list('requireNum', flat=True)
        require_num = 0
        for num in require_num_list:
            require_num = require_num + num
        # 1 新建，2 测试中，3 已完成，4 已归档, 5 暂停
        testplan_sum = project_test_plan_list.count()
        new_count = project_test_plan_list.filter(Status=1).count()
        testing_count = project_test_plan_list.filter(Status=2).count()
        finish_count = project_test_plan_list.filter(Status=3).count() + project_test_plan_list.filter(Status=4).count()
        # filing_count = project_test_plan_list.filter(Status=4).count()
        pause_count = project_test_plan_list.filter(Status=5).count()
        if testplan_sum != 0:
            finish_rate = float('%.2f' % (finish_count / testplan_sum * 100))
        else:
            finish_rate = 0
        result = {
            "testplan_sum": testplan_sum,
            "require_num": require_num,
            "finish_rate": finish_rate,
            "data": [
                {"name": "新建", "value": new_count},
                {"name": "测试中", "value": testing_count},
                {"name": "已完成", "value": finish_count},
                {"name": "暂停", "value": pause_count},
            ]
        }
        return response.Response(data=result)


class ProjectTestPlanStaticticsView(APIView):
    """
    /api/home/<USER>/projecttestplan/[days]
    获取项目测试计划趋势图
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        project_test_plan_list = ProjectTestPlan.objects.filter(IsActive=1).filter(Project__in=project_ids).filter(
            CreationTime__range=(start_time, end_time)).values("id", "Project", "Status", "requireNum")
        all_project = models.Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")
        project_list = []
        project_require_num = []
        project_testplan_new = []
        project_testplan_testing = []
        project_testplan_finish = []
        project_testplan_pause = []
        for project in all_project:
            project_list.append(project["PBTitle"])

            project_require_num_list = project_test_plan_list.filter(Project=project["id"]).values_list('requireNum',
                                                                                                        flat=True)
            require_num = 0
            for num in project_require_num_list:
                require_num = require_num + num
            project_require_num.append(require_num)

            # 1 新建，2 测试中，3 已完成，4 已归档, 5 暂停
            new_count = project_test_plan_list.filter(Project=project["id"]).filter(Status=1).count()
            project_testplan_new.append(new_count)
            testing_count = project_test_plan_list.filter(Project=project["id"]).filter(Status=2).count()
            project_testplan_testing.append(testing_count)
            finish_count = project_test_plan_list.filter(Project=project["id"]).filter(
                Status=3).count() + project_test_plan_list.filter(Project=project["id"]).filter(Status=4).count()
            project_testplan_finish.append(finish_count)
            pause_count = project_test_plan_list.filter(Project=project["id"]).filter(Status=5).count()
            project_testplan_pause.append(pause_count)

        result = {
            "xAxis": [
                {
                    "data": project_list
                }
            ],
            "series": [
                {
                    "data": project_require_num
                },
                {
                    "data": project_testplan_new
                },
                {
                    "data": project_testplan_testing
                },
                {
                    "data": project_testplan_finish
                },
                {
                    "data": project_testplan_pause
                }
            ]
        }
        return response.Response(data=result)


class ProjectTestPlanCountView(APIView):
    """
    /api/home/<USER>/project_testplan_statictics
    获取新增测试计划趋势图
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        # 1 近7天 2 本周 3 近15天 4 本月
        type = self.kwargs.get('type')
        chart = VM_ProjectTestPlanCountChart(self.request, type)

        title_info = self.get_title_info(chart.xaxis)
        # 时间区间
        time_period_dt = self.get_ahead_day(type)
        time_period = time_period_dt.get('result')
        day = time_period_dt.get('day')

        # 需求数
        require_num = chart.series_data[0].get('require_num')
        requireNumInfo = self.get_reiquireNum_by_day(require_num, time_period)
        require_info = self.get_series_info(time_period, title_info, requireNumInfo['requireNum'], day)

        test_plan_detail = chart.series_data[0].get('data')
        plan_info = self.get_plan_status_by_day(test_plan_detail, time_period)
        # 全部测试计划
        totle_info = self.get_totle_testplan_info(plan_info)
        # 完成率
        finished_percent_info = self.get_finished_percent(plan_info, title_info)
        temp_cata = finished_percent_info.get('categories')
        # 需求数统计
        temp_require_info = self.get_requireNum_by_project(require_info, temp_cata)
        all_require_num = 0;
        for tm_num in temp_require_info:
            all_require_num = all_require_num + tm_num
        finished_percent_chart = self.get_group_chart_info(finished_percent_info, temp_require_info)

        # 各状态测试计划
        all_data = self.get_series_info(time_period, title_info, plan_info['all_data'], day)
        testing = self.get_series_info(time_period, title_info, plan_info['testing'], day)
        finished = self.get_series_info(time_period, title_info, plan_info['finished'], day)

        result_dt = {'all_data': self.get_vertical_chart(time_period, all_data, '测试计划数量', '全部测试计划'),
                     'testing': self.get_vertical_chart(time_period, testing, '测试计划数量', '测试计划-测试中'),
                     'finished': self.get_vertical_chart(time_period, finished, '测试计划数量', '测试计划-已完成'),
                     'totle_info': totle_info,
                     'finished_percent_chart': finished_percent_chart,
                     'require_num_chart': self.get_vertical_chart(time_period, require_info, '功能需求数',
                                                                  '功能需求数'),
                     'all_require_num': all_require_num
                     }
        return response.Response(result_dt)

    def get_reiquireNum_by_day(self, data, x_axis):
        requireNum, result = {}, list()
        for axis in x_axis:
            requireNum[axis] = dict()
        for _info in data:
            for key, value in _info.items():
                temp_info = key.split("_")
                if temp_info[1] in x_axis:
                    requireNum[temp_info[1]][temp_info[0]] = requireNum[temp_info[1]].get(temp_info[0], 0) + value
        return {'requireNum': requireNum}

    # 需求数
    def get_requireNum_by_project(self, require_info, temp_cata):
        temp_require_info = []
        for _title in temp_cata:
            for _detail in require_info:
                if _detail.get('name') == _title:
                    _data = _detail.get('data')
                    count_num = 0
                    for _temp in _data:
                        count_num = count_num + _temp
                    temp_require_info.append(count_num)
        return temp_require_info

    # 标题信息：项目{"1":"TEST"}
    def get_title_info(self, title_list):
        title_info = dict()
        for _title in title_list:
            temp = _title.split("_")
            title_info[temp[0]] = temp[1]
        return title_info

    # 完成率百分比:注意 因为是组合图表，所以横轴必须保持一致
    def get_finished_percent(self, data, title_dt):
        categories, temp_dt = list(), dict()
        all_data, testing, finished = list(), list(), list()
        for key, value in data.items():
            temp = dict()
            for k1, v1 in value.items():
                if v1:
                    for k2, v2 in v1.items():
                        temp[title_dt[k2]] = temp.get(title_dt[k2], 0) + v2
            temp_dt[key] = temp
        for key, value in temp_dt.get('all_data').items():
            categories.append(key)
            all_data.append(value)
        for _data in categories:
            if temp_dt.get('testing').get(_data):
                testing.append(temp_dt.get('testing')[_data])
            else:
                testing.append(0)
        for _data in categories:
            if temp_dt.get('finished').get(_data):
                finished.append(temp_dt.get('finished')[_data])
            else:
                finished.append(0)
        return {'categories': categories, 'all_data': all_data, 'testing': testing, 'finished': finished}

    # 需求数信息
    def get_require_num_info(self, data, title_dt):
        temp_dt, x_axis, y_axis = dict(), list(), list()
        for _info in data:
            for key, value in _info.items():
                temp = key.split("_")
                temp_dt[temp[0]] = temp_dt.get(temp[0], 0) + value
        for key, value in temp_dt.items():
            x_axis.append(title_dt[key])
            y_axis.append(value)
        return {'x_axis': x_axis, 'y_axis': y_axis}

    # 测试计划总信息
    def get_totle_testplan_info(self, data):
        totle_info, finished_per = dict(), 0
        for key, value in data.items():
            count = 0
            for k1, v1 in value.items():
                if v1:
                    for k2, v2 in v1.items():
                        count = count + int(v2)
            totle_info[key] = count
        if totle_info['all_data'] and totle_info['all_data'] > 0:
            finished_per = round(
                ((totle_info.get('finished', 0) + totle_info.get('archive', 0)) / totle_info.get('all_data')) * 100, 2)
        totle_info['finished_per'] = str(finished_per) + '%'
        return totle_info

    # 获取时间区间
    def get_ahead_day(self, type):
        result = list()
        day = 7
        if str(type) == '3':
            day = 15
        elif str(type) == '4':
            day = 30
        now_time = datetime.datetime.now()
        this_week_start = now_time - datetime.timedelta(days=now_time.weekday())
        this_month_start = datetime.datetime(now_time.year, now_time.month, 1)
        if str(type) == '2':
            if now_time.strftime('%Y%m%d') > this_week_start.strftime('%Y%m%d'):
                day = int(now_time.strftime('%Y%m%d')) - int(this_week_start.strftime('%Y%m%d')) + 1
            else:
                day = 1
        elif str(type) == '5':
            if now_time.strftime('%Y%m%d') > this_month_start.strftime('%Y%m%d'):
                day = int(now_time.strftime('%Y%m%d')) - int(this_month_start.strftime('%Y%m%d')) + 1
            else:
                day = 1
        for i in range(day):
            temp_date = datetime.datetime.now() + datetime.timedelta(days=-1 * (i))
            result.append(temp_date.strftime('%Y%m%d'))
        result.sort()
        return {'result': result, 'day': day}

    # 图表数据处理
    def get_series_info(self, x_axis, title_dt, all_data, day):
        temp_str = ''
        for i in range(day):
            temp_str = "0" + temp_str
        result = list()
        for key, value in title_dt.items():
            temp_data = list()
            for axis in x_axis:
                if all_data.get(axis):
                    temp_data.append(all_data.get(axis).get(key, 0))
                else:
                    temp_data.append(0)
            tp = [str(i) for i in temp_data]
            if "".join(tp) == temp_str:
                pass
            else:
                result.append({'name': value, 'data': temp_data})
        return result

    # 按天获取测试计划信息
    def get_plan_status_by_day(self, data, x_axis):

        all_data_dt, testing_dt, finished_dt, archive, result = {}, {}, {}, {}, list()

        for axis in x_axis:
            all_data_dt[axis] = dict()
            testing_dt[axis] = dict()
            finished_dt[axis] = dict()
            archive[axis] = dict()

        for _info in data:
            for key, value in _info.items():
                temp_info = key.split("_")
                if temp_info[1] in x_axis:
                    all_data_dt[temp_info[1]][temp_info[0]] = all_data_dt[temp_info[1]].get(temp_info[0], 0) + value
                    if str(temp_info[2]) in ('1', '2'):
                        testing_dt[temp_info[1]][temp_info[0]] = testing_dt[temp_info[1]].get(temp_info[0], 0) + value
                    if str(temp_info[2]) == '3':
                        finished_dt[temp_info[1]][temp_info[0]] = finished_dt[temp_info[1]].get(temp_info[0], 0) + value
                    if str(temp_info[2]) == '4':
                        archive[temp_info[1]][temp_info[0]] = archive[temp_info[1]].get(temp_info[0], 0) + value

        return {'all_data': all_data_dt, 'testing': testing_dt, 'finished': finished_dt, 'archive': archive}

    """
        1 最终的图表数据处理
        2 因为追求性能所以排序放在最后处理
        3 过滤掉0的数据
    """

    def get_object_chart(self, chart):
        temp, x_list, y_list = list(), list(), list()
        title_list = chart.xaxis
        num_list = chart.series_data[0].get('data')
        for i in range(len(num_list)):
            temp_dt = {}
            if int(num_list[i]) > 0:
                temp_dt["Project"] = title_list[i]
                temp_dt["TotalCount"] = num_list[i]
                temp.append(temp_dt)

        def sort_by_totalcount(elem):
            return elem["TotalCount"]

        temp.sort(reverse=True, key=sort_by_totalcount)
        for _data in temp:
            x_list.append(_data['Project'])
            y_list.append(_data['TotalCount'])
        chart.xaxis = x_list
        chart.series_data[0]['data'] = y_list
        return chart

    def get_vertical_chart(self, x_axis, chart_data, title, y_title):
        temp_chart = {'chart_id': 0, 'project_id': 0, 'version_id': 0}
        temp_chart['title'] = {'text': y_title, 'style': 'font-size: 12px;color:#657180'}
        temp_chart['yAxis'] = {'title': {'text': title, 'style': 'font-size: 12px'}}
        temp_chart['legend'] = {'layout': 'vertical', 'align': 'right', 'verticalAlign': 'middle'}
        temp_chart['xAxis'] = {'categories': x_axis, 'crosshair': True}
        temp_chart['series'] = chart_data
        return temp_chart

    def get_group_chart_info(self, data, temp_require_info):
        temp_chart = {'chart_id': 0, 'project_id': 0, 'version_id': 0}
        temp_chart['chart'] = {'type': 'column'}
        temp_chart['title'] = {'text': '测试计划完成率', 'style': 'font-size:12px;color:#657180'}
        temp_chart['xAxis'] = {'categories': data.get('categories'), 'crosshair': True}
        temp_chart['yAxis'] = {'title': {'text': '总数/已完成', 'style': 'font-size: 12px'}}
        temp_chart['plotOptions'] = {'column': {'borderWidth': '0'}}
        temp_chart['series'] = [{'name': '总数', 'color': '#557dfc', 'data': data.get('all_data')},
                                {'name': '完成数', 'color': '#3bc482', 'data': data.get('finished')},
                                {'name': '需求数', 'color': '#eeb017', 'data': temp_require_info}]
        return temp_chart

    # 需求数图表
    def get_requireNum_chart_info(self, data):
        temp_data = list()
        x_axis_info = data['x_axis']
        y_axis_info = data['y_axis']
        for i in range(len(x_axis_info)):
            temp_data.append({'name': x_axis_info[i], 'data': [y_axis_info[i]]})
        temp_chart = {'chart_id': 0, 'project_id': 0, 'version_id': 0}
        temp_chart['title'] = {'text': ''}
        temp_chart['yAxis'] = {'title': {'text': '需求数'}}
        temp_chart['legend'] = {'layout': 'vertical', 'align': 'right', 'verticalAlign': 'middle'}
        temp_chart['xAxis'] = {'categories': data.get('x_axis'), 'crosshair': True}
        temp_chart['series'] = temp_data
        return temp_chart


class ProjectTestPlanStatusCountView(APIView):
    # class ProjectTestPlanStatusCountView(generics.RetrieveAPIView):
    """
    /api/home/<USER>/project_testplan_status
    获取当前空间下所有测试计划的各种状态信息
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        result = {'new': 0, 'testing': 0, 'finished': 0, 'archive': 0}
        status_dt = {'1': 'new', '2': 'testing', '3': 'finished', '4': 'archive'}
        type = self.kwargs.get('type')

        now_time = datetime.datetime.now()
        # 当前时间减去一天 获得昨天当前时间
        time_month = now_time + datetime.timedelta(days=-30)
        time_month = time_month.strftime('%Y%m%d')
        time_seven = now_time + datetime.timedelta(days=-7)
        time_seven = time_seven.strftime('%Y%m%d')

        this_month_start = now_time.strftime('%Y%m') + '01'

        data = models.ProjectTestPlan.objects.all()
        for _info in data:
            CreationTime = _info.__dict__.get('CreationTime').strftime('%Y%m%d')
            if int(type) == 1:
                result[status_dt[str(_info.__dict__.get('Status'))]] = result[status_dt[
                    str(_info.__dict__.get('Status'))]] + 1
            elif int(type) == 2:
                if CreationTime >= time_month:
                    result[status_dt[str(_info.__dict__.get('Status'))]] = result[status_dt[
                        str(_info.__dict__.get('Status'))]] + 1
            elif int(type) == 3:
                if CreationTime >= this_month_start:
                    result[status_dt[str(_info.__dict__.get('Status'))]] = result[status_dt[
                        str(_info.__dict__.get('Status'))]] + 1
            elif int(type) == 4:
                if CreationTime >= time_seven:
                    result[status_dt[str(_info.__dict__.get('Status'))]] = result[status_dt[
                        str(_info.__dict__.get('Status'))]] + 1
        return response.Response(result)


class ProjectTestCaseCountView(APIView):
    """
    /api/home/<USER>/project_testcase_statictics
    获取新增测试用例趋势图
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        # 1 近7天 2 本周 3 近15天 4 本月
        type = self.kwargs.get('type')
        chart = VM_ProjectTestCaseCountChart(self.request, type)
        autoCaseChart = VM_ProjectAutoCaseCountChart(self.request, type)

        title_info = ProjectTestPlanCountView.get_title_info(self, chart.xaxis)
        # 时间区间
        time_period_dt = ProjectTestPlanCountView.get_ahead_day(self, type)
        time_period = time_period_dt.get('result')
        day = time_period_dt.get('day')

        test_case_detail = chart.series_data[0].get('data')
        case_info = self.get_case_by_day(test_case_detail, time_period)
        line_chart_info = self.get_series_info(time_period, title_info, case_info, day)
        case_info = self.get_testcase_totle(test_case_detail, title_info, time_period)
        temp_title = case_info.get('categories')
        # 新增自动化case信息
        auto_case_detail = autoCaseChart.series_data[0].get('data')
        auto_case_info = self.get_testcase_totle(auto_case_detail, title_info, time_period)
        auto_case_x_data = self.get_auto_case_info_by_project(auto_case_info, temp_title)

        result_dt = {'case_info': self.get_testcase_chart_info(case_info, auto_case_x_data),
                     'line_char_info': self.get_vertical_chart(time_period, line_chart_info,
                                                               '各项目新增功能case数量趋势',
                                                               '各项目新增功能case数量趋势')
                     }
        return response.Response(result_dt)

    def get_auto_case_info_by_project(self, auto_case_info, temp_title):
        categories_auto = auto_case_info.get('categories')
        x_asis_auto = auto_case_info.get('x_asis')
        temp_dt = dict()
        for i in range(len(categories_auto)):
            temp_dt[categories_auto[i]] = x_asis_auto[i]
        auto_case_x_data = []
        for _dtl in temp_title:
            if temp_dt.get(_dtl):
                auto_case_x_data.append(temp_dt.get(_dtl))
            else:
                auto_case_x_data.append(0)
        return auto_case_x_data

    def get_case_by_day(self, data, x_axis):
        case, result = {}, list()
        for axis in x_axis:
            case[axis] = dict()
        for _info in data:
            for key, value in _info.items():
                temp_info = key.split("_")
                if temp_info[1] in x_axis:
                    case[temp_info[1]][temp_info[0]] = case[temp_info[1]].get(temp_info[0], 0) + value
        return case

    def get_series_info(self, x_axis, title_dt, all_data, day):

        temp_str = ''
        for i in range(day):
            temp_str = "0" + temp_str
        result = list()
        for key, value in title_dt.items():
            temp_data = list()
            for axis in x_axis:
                if all_data.get(axis):
                    temp_data.append(all_data.get(axis).get(key, 0))
                else:
                    temp_data.append(0)
            tp = [str(i) for i in temp_data]
            if "".join(tp) == temp_str:
                pass
            else:
                result.append({'name': value, 'data': temp_data})
        return result

    def get_testcase_totle(self, data, title_dt, time_period):
        categories, temp_dt, x_asis = list(), dict(), list()
        for _info in data:
            for key, value in _info.items():
                temp_info = key.split('_')
                if title_dt.get(str(temp_info[0])) not in categories and temp_info[1] in time_period:
                    categories.append(title_dt.get(str(temp_info[0])))
                if temp_info[1] in time_period:
                    temp_dt[str(temp_info[0])] = temp_dt.get(str(temp_info[0]), 0) + value

        for k, v in temp_dt.items():
            x_asis.append(v)
        return {'categories': categories, 'x_asis': x_asis}

    def get_testcase_chart_info(self, data, auto_case_x_data):
        temp_chart = {'chart_id': 0, 'project_id': 0, 'version_id': 0}
        temp_chart['chart'] = {'type': 'column'}
        temp_chart['title'] = {'text': '', 'style': 'font-size:12px;color:#657180'}
        temp_chart['xAxis'] = {'categories': data.get('categories'), 'crosshair': True}
        temp_chart['yAxis'] = {'title': {'text': 'case数量', 'style': 'font-size: 12px'}}
        temp_chart['plotOptions'] = {'column': {'borderWidth': '0'}}
        temp_chart['series'] = [{'name': '新增功能case数', 'color': '#557dfc', 'data': data.get('x_asis')},
                                {'name': '新增自动化case数', 'color': '#3bc482', 'data': auto_case_x_data}
                                ]
        return temp_chart

    def get_vertical_chart(self, x_axis, chart_data, title, y_title):
        temp_chart = {'chart_id': 0, 'project_id': 0, 'version_id': 0}
        temp_chart['title'] = {'text': y_title, 'style': 'font-size: 12px;color:#657180'}
        temp_chart['yAxis'] = {'title': {'text': title, 'style': 'font-size: 12px'}}
        temp_chart['legend'] = {'layout': 'vertical', 'align': 'right', 'verticalAlign': 'middle'}
        temp_chart['xAxis'] = {'categories': x_axis, 'crosshair': True}
        temp_chart['series'] = chart_data
        return temp_chart


class ProjectAutoCaseCountView(APIView):
    """
    /api/home/<USER>/project_autocase_statictics
    获取新增自动化case趋势图
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        # 1 近7天 2 本周 3 近15天 4 本月
        type = self.kwargs.get('type')
        chart = VM_ProjectAutoCaseCountChart(self.request, type)

        title_info = ProjectTestPlanCountView.get_title_info(self, chart.xaxis)
        # 时间区间
        time_period_dt = ProjectTestPlanCountView.get_ahead_day(self, type)
        time_period = time_period_dt.get('result')
        day = time_period_dt.get('day')
        auto_case_detail = chart.series_data[0].get('data')
        case_info = self.get_case_by_day(auto_case_detail, time_period)
        line_chart_info = self.get_series_info(time_period, title_info, case_info, day)
        case_info = self.get_testcase_totle(auto_case_detail, title_info)
        result_dt = {
            'line_char_info': ProjectTestCaseCountView.get_vertical_chart(self, time_period, line_chart_info,
                                                                          '各项目新增自动化case数量趋势',
                                                                          '各项目新增自动化case数量趋势')
        }
        return response.Response(result_dt)

    def get_case_by_day(self, data, x_axis):
        case, result = {}, list()
        for axis in x_axis:
            case[axis] = dict()
        for _info in data:
            for key, value in _info.items():
                temp_info = key.split("_")
                if temp_info[1] in x_axis:
                    case[temp_info[1]][temp_info[0]] = case[temp_info[1]].get(temp_info[0], 0) + value
        return case

    def get_series_info(self, x_axis, title_dt, all_data, day):
        temp_str = ''
        for i in range(day):
            temp_str = "0" + temp_str
        result = list()
        for key, value in title_dt.items():
            temp_data = list()
            for axis in x_axis:
                if all_data.get(axis):
                    temp_data.append(all_data.get(axis).get(key, 0))
                else:
                    temp_data.append(0)
            tp = [str(i) for i in temp_data]
            if "".join(tp) == temp_str:
                pass
            else:
                result.append({'name': value, 'data': temp_data})
        return result

    def get_testcase_totle(self, data, title_dt):
        categories, temp_dt, x_asis = list(), dict(), list()
        for _info in data:
            for key, value in _info.items():
                temp_info = key.split('_')
                if title_dt.get(str(temp_info[0])) not in categories:
                    categories.append(title_dt.get(str(temp_info[0])))
                temp_dt[str(temp_info[0])] = temp_dt.get(str(temp_info[0]), 0) + value
        for k, v in temp_dt.items():
            x_asis.append(v)
        return {'categories': categories, 'x_asis': x_asis}


class HomeProjectTestCaseGrowthTrendView(APIView):
    """
        /api/home/<USER>/growthtrend/testcase/new/<days>?project=[]
    """
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        result = {
            "xAxis": {
                "data": []
            },
            "series": []
        }
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = request.GET.get('project', '').split(',')
        if project_ids[0] == '':
            project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        project_dict = ProjectService.get_space_project_dict(product_spaces)

        project_testcase_growth_trend_all = CrontabTestCaseGrowthTrend.objects.filter(
            Project__in=project_ids).values_list('Project', 'CreateDate', 'CaseGrowth').order_by("CreateDate")
        project_growth_trend_list = defaultdict(list)
        data_list = []
        for (project_id, create_date, case_crowth) in project_testcase_growth_trend_all:
            project_growth_trend_list[project_id].append(case_crowth)
            if create_date not in data_list:
                data_list.append(create_date)

        resp_series = []
        for project_id, growth_trend in project_growth_trend_list.items():
            project_testcase_growth_trend = {}
            project_testcase_growth_trend["name"] = project_dict[project_id]
            project_testcase_growth_trend["type"] = "line"
            project_testcase_growth_trend["smooth"] = True
            project_testcase_growth_trend["data"] = growth_trend
            resp_series.append(project_testcase_growth_trend)

        for growth_trend_list in resp_series:
            tmp = len(data_list) - len(growth_trend_list["data"])
            if tmp > 0:
                for i in range(tmp):
                    growth_trend_list["data"].insert(0, "")

        result["xAxis"]["data"] = data_list
        result["series"] = resp_series
        return response.Response(data=result)


class HomeProjectAutoCaseGrowthTrendView(APIView):
    """
    /api/home/<USER>/growthtrend/autocase/new/<days>
    """
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        result = {
            "xAxis": {
                "data": []
            },
            "series": []
        }
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_dict = ProjectService.get_space_project_dict(product_spaces)

        project_ids = request.GET.get('project', '').split(',')
        if project_ids[0] == '':
            project_ids = models.Project.objects.filter(ProductSpace=product_spaces).filter(IsActive=1).values_list(
                'id', flat=True)

        project_autocase_growth_trend_all = CrontabAutoCaseGrowthTrend.objects.filter(
            Project__in=project_ids).values_list('Project', 'CreateDate', 'CaseGrowth').order_by("CreateDate")
        project_growth_trend_list = defaultdict(list)
        data_list = []
        for (project_id, create_date, case_crowth) in project_autocase_growth_trend_all:
            project_growth_trend_list[project_id].append(case_crowth)
            if create_date not in data_list:
                data_list.append(create_date)

        resp_series = []
        for project_id, growth_trend in project_growth_trend_list.items():
            project_autocase_growth_trend = {}
            project_autocase_growth_trend["name"] = project_dict[project_id]
            project_autocase_growth_trend["type"] = "line"
            project_autocase_growth_trend["smooth"] = True
            project_autocase_growth_trend["data"] = growth_trend
            resp_series.append(project_autocase_growth_trend)

        for growth_trend_list in resp_series:
            tmp = len(data_list) - len(growth_trend_list["data"])
            if tmp > 0:
                for i in range(tmp):
                    growth_trend_list["data"].insert(0, "")

        result["xAxis"]["data"] = data_list
        result["series"] = resp_series
        return response.Response(data=result)


class HomeTestCaseRepeatRateView(APIView):
    def get(self, request, *args, **kwargs):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        project_ids = ','.join([str(i) for i in project_ids])
        test_case_repeat_rate = TestCaseService.get_product_space_repeat_rate(project_ids)
        try:
            result = {
                "ProductSpace": product_spaces,
                "TestCaseRepeatRate": 1 - test_case_repeat_rate[0][0]
            }
        except Exception as e:
            result = {
                "ProductSpace": product_spaces,
                "TestCaseRepeatRate": 0
            }
        return response.Response(data=result)


class ProjectTestcaseRepeatrateView(generics.RetrieveAPIView):
    """
    /api/home/<USER>/project_testcase_repratrate
    获取case重复率
    """
    serializer_class = home_statistics_serializer.ProjectTestcaseRepeatrateStatisticsSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        chart = VM_ProjectTestCaseRepeatrateChart(self.request)
        return chart


class HomeTestCaseSumTrendGrowth(APIView):
    """
        /api/home/<USER>/trendgrowth/testcase/sum?st=2020-01-01&et=2020-01-01&project=[]
    """

    def get(self, request, *args, **kwargs):
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        project_ids = request.GET.get('project', '').split(',')

        result = {
            "xAxis": {
                "data": []
            },
            "series": []
        }
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        if project_ids[0] == '':
            project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        project_dict = ProjectService.get_space_project_dict(product_spaces)

        project_testcase_sum_growth_trend_all = CrontabTestCaseSumGrowthTrend.objects.filter(project_id__in=project_ids).filter(
            create_date__range=(start_time, end_time)).values_list('project_id', 'create_date', 'case_sum').order_by("create_date")
        project_testcase_sum_growth_trend_list = defaultdict(list)
        data_list = []
        for (project_id, create_date, case_sum) in project_testcase_sum_growth_trend_all:
            project_testcase_sum_growth_trend_list[project_id].append(case_sum)
            if create_date not in data_list:
                data_list.append(create_date)

        resp_series = []
        for project_id, growth_trend in project_testcase_sum_growth_trend_list.items():
            project_testcase_growth_trend = {}
            project_testcase_growth_trend["name"] = project_dict[project_id]
            project_testcase_growth_trend["type"] = "line"
            project_testcase_growth_trend["smooth"] = True
            project_testcase_growth_trend["data"] = growth_trend
            resp_series.append(project_testcase_growth_trend)

        for growth_trend_list in resp_series:
            tmp = len(data_list) - len(growth_trend_list["data"])
            if tmp > 0:
                for i in range(tmp):
                    growth_trend_list["data"].insert(0, "")

        result["xAxis"]["data"] = data_list
        result["series"] = resp_series

        return response.Response(data=result)


class HomeAutoCaseSumTrendGrowth(APIView):
    """
        /api/home/<USER>/trendgrowth/autocase/sum/<days>?project=[]
    """

    def get(self, request, *args, **kwargs):
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        project_ids = request.GET.get('project', '').split(',')
        result = {
            "xAxis": {
                "data": []
            },
            "series": []
        }
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        if project_ids[0] == '':
            project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        project_id_name_list = models.Project.objects.filter(ProductSpace=product_spaces).filter(
            IsActive=1).values_list('id', 'PBTitle')
        project_id_name_dict = {}
        for key, value in project_id_name_list:
            project_id_name_dict[key] = value

        project_autocase_sum_growth_trend_all = CrontabAutoCaseSumGrowthTrend.objects.filter(project_id__in=project_ids).filter(
            create_date__range=(start_time, end_time)).values_list('project_id', 'create_date', 'case_sum').order_by("create_date")

        project_autocase_sum_growth_trend_list = defaultdict(list)
        data_list = []
        for (project_id, create_date, case_sum) in project_autocase_sum_growth_trend_all:
            project_autocase_sum_growth_trend_list[project_id].append(case_sum)
            if create_date not in data_list:
                data_list.append(create_date)

        resp_series = []
        for project_id, growth_trend in project_autocase_sum_growth_trend_list.items():
            project_autocase_sum_growth_trend = {}
            project_autocase_sum_growth_trend["name"] = project_id_name_dict[project_id]
            project_autocase_sum_growth_trend["type"] = "line"
            project_autocase_sum_growth_trend["smooth"] = True
            project_autocase_sum_growth_trend["data"] = growth_trend
            resp_series.append(project_autocase_sum_growth_trend)

        for growth_trend_list in resp_series:
            tmp = len(data_list) - len(growth_trend_list["data"])
            if tmp > 0:
                for i in range(tmp):
                    growth_trend_list["data"].insert(0, "")

        result["xAxis"]["data"] = data_list
        result["series"] = resp_series

        return response.Response(data=result)


class HomeTestCaseTrendGrowth(APIView):
    """
        /api/home/<USER>/testcase/trendgrowth/<days>
    """

    def get(self, request, *args, **kwargs):
        result = {
            "xAxis": {
                "data": []
            },
            "series": []
        }
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        case_sum_growth_trend_all = CrontabCaseSumGrowthTrend.objects.filter(product_space=product_spaces).filter(
            create_date__range=(start_time, end_time)).values_list('create_date', 'test_case_sum', 'auto_case_sum').order_by("create_date")
        data_list = []
        test_case_sum_list = []
        auto_case_sum_list = []
        for (create_date, test_case_sum, auto_case_sum) in case_sum_growth_trend_all:
            data_list.append(create_date)
            test_case_sum_list.append(test_case_sum)
            auto_case_sum_list.append(auto_case_sum)

        resp_series = []

        test_case_sum_growth_trend = {"name": "功能用例", "type": "line", "smooth": True, "data": test_case_sum_list}
        resp_series.append(test_case_sum_growth_trend)

        auto_case_sum_growth_trend = {"name": "自动化用例", "type": "line", "smooth": True, "data": auto_case_sum_list}
        resp_series.append(auto_case_sum_growth_trend)

        test_case_sum_new_list = []
        for i in range(len(test_case_sum_list)):
            if i == 0:
                test_case_sum_new_list.append(0)
            else:
                test_case_sum_new_list.append(test_case_sum_list[i] - test_case_sum_list[i - 1])
        test_case_sum_new_growth_trend = {"name": "功能用例新增", "type": "bar", "barWidth": "20%", "smooth": True,
                                          "yAxisIndex": 1, "data": test_case_sum_new_list}
        resp_series.append(test_case_sum_new_growth_trend)

        auto_case_sum_new_list = []
        for i in range(len(auto_case_sum_list)):
            if i == 0:
                auto_case_sum_new_list.append(0)
            else:
                auto_case_sum_new_list.append(auto_case_sum_list[i] - auto_case_sum_list[i - 1])
        auto_case_sum_new_growth_trend = {"name": "自动化用例新增", "type": "bar", "barWidth": "20%", "smooth": True,
                                          "yAxisIndex": 1, "data": auto_case_sum_new_list}
        resp_series.append(auto_case_sum_new_growth_trend)

        result["xAxis"]["data"] = data_list
        result["series"] = resp_series

        return response.Response(data=result)


class IssueStatisticsView(APIView):
    """
        /api/home/<USER>/issue?st=2023-01-01&et=2024-01-01&project=0,1,2
    """

    def get(self, request):
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_list = request.GET.get('project', '').split(',')
        project_ids = []
        if project_list[0] == '':
            project_ids = ProjectService.get_project_ids_by_space(product_spaces)
            issue_list = models.ProjectIssue.objects.filter(CreationTime__gte=start_time).filter(
                CreationTime__lte=end_time + ' 23:59:59').filter(Project__in=project_ids)
        else:
            project_ids = project_list
            issue_list = models.ProjectIssue.objects.filter(CreationTime__gte=start_time).filter(
                CreationTime__lte=end_time + ' 23:59:59').filter(Project__in=project_list)

        result = dict()
        result['total'] = issue_list.count()
        result['open'] = issue_list.filter(Status=2).count()
        result['close'] = issue_list.filter(Status=3).count()
        result['fix'] = issue_list.filter(Status=4).count()
        result['reopen'] = issue_list.filter(Status=5).count()

        result['severity'] = dict()
        result['severity']['p0'] = issue_list.filter(Severity=0).count()
        result['severity']['p1'] = issue_list.filter(Severity=1).count()
        result['severity']['p2'] = issue_list.filter(Severity=2).count()
        result['severity']['p3'] = issue_list.filter(Severity=3).count()
        result['severity']['p4'] = issue_list.filter(Severity=4).count()

        result['discover_way'] = dict()
        result['discover_way']['manual'] = issue_list.filter(discover_way=1).count()
        result['discover_way']['auto'] = issue_list.filter(discover_way=2).count()
        result['discover_way']['replay'] = issue_list.filter(discover_way=3).count()
        result['discover_way']['monitor'] = issue_list.filter(discover_way=4).count()
        result['discover_way']['other'] = issue_list.filter(discover_way=5).count()

        result['project'] = []
        project_list = ProjectService.get_space_project_dict(product_spaces)

        for project_id in project_ids:
            project_id = int(project_id)
            total = issue_list.filter(Project=project_id).count()
            p0 = issue_list.filter(Project=project_id).filter(Severity=0).count()
            p1 = issue_list.filter(Project=project_id).filter(Severity=1).count()
            p2 = issue_list.filter(Project=project_id).filter(Severity=2).count()
            p3 = issue_list.filter(Project=project_id).filter(Severity=3).count()
            p4 = issue_list.filter(Project=project_id).filter(Severity=4).count()
            auto = issue_list.filter(Project=project_id).filter(discover_way=2).count()
            replay = issue_list.filter(Project=project_id).filter(discover_way=3).count()
            tmp_project_issues = {
                "id": project_id,
                "title": project_list[project_id],
                "total": total,
                "p0": p0,
                "p1": p1,
                "p2": p2,
                "p3": p3,
                "p4": p4,
                "auto": auto,
                "replay": replay
            }

            result['project'].append(tmp_project_issues)
        return response.Response(data=result)


class TestPlanAvgCaseStatisticsView(APIView):
    """
        /api/home/<USER>/testplan/testcase/avg?st=2023-01-01&et=2024-01-01
    """

    def get(self, request):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        project_list = ProjectService.get_projects_by_space(product_spaces)
        product_test_plans = ProjectTestPlan.objects.filter(Project__in=project_list).filter(
            CreationTime__gte=start_time).filter(CreationTime__lte=end_time)
        test_plan_cases = ProjectTestPlanCase.objects.filter(TestPlan__in=product_test_plans)
        result = list()

        average_case_count = product_test_plans.values('Project').annotate(avg_case_count=Avg('CaseCount')).order_by(
            'avg_case_count')
        for entry in average_case_count:
            project_id = entry['Project']
            avg_cases = float('%.1f' % entry['avg_case_count'])
            project_name = ProjectService.get_project_name_by_id(project_id)
            tmp = {
                'id': project_id,
                'name': project_name,
                'avg_cases': avg_cases
            }

            project_test_plans = product_test_plans.filter(Project=project_id)
            avg_case_exec_count = project_test_plans.count()
            test_plan_cases_exec_count = test_plan_cases.filter(TestPlan__in=project_test_plans).exclude(
                TestResult=0).count()
            tmp['avg_case_exec'] = float('%.1f' % (test_plan_cases_exec_count / avg_case_exec_count))
            result.append(tmp)

        return response.Response(data=result)


class RequirementCaseAvgStatistics(APIView):
    """
        /api/home/<USER>/requirement/testcase/avg?st=2023-01-01&et=2024-01-01
    """

    def get(self, request):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        project_list = ProjectService.get_projects_by_space(product_spaces)
        product_test_plans = ProjectTestPlan.objects.filter(Project__in=project_list).filter(
            CreationTime__gte=start_time).filter(CreationTime__lte=end_time)
        result = list()
        project_case_requirement_count = product_test_plans.values('Project').annotate(
            total_case_count=Sum('CaseCount'),
            total_require_num=Sum('requireNum')
        )
        for entry in project_case_requirement_count:
            project_id = entry['Project']
            avg_cases = entry['total_case_count']
            requirement_count = entry['total_require_num']
            if avg_cases > 0 and requirement_count > 0:
                req_avg_cases = avg_cases / requirement_count
            else:
                req_avg_cases = 0
            project_name = ProjectService.get_project_name_by_id(project_id)

            tmp = {
                'id': project_id,
                'name': project_name,
                'avg_cases': float('%.1f' % req_avg_cases)
            }
            result.append(tmp)

        result_sort = sorted(result, key=lambda x: x['avg_cases'])
        return response.Response(data=result_sort)


class TestPlanCaseTrendStatisticsView(APIView):
    """
        /api/home/<USER>/testplan/testcase/trend?project=19&st=2023-01-01&et=2024-01-01
    """

    def get(self, request):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = request.GET.get('project', '').split(',')
        if project_ids[0] == '':
            project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        else:
            project_ids = list(map(int, project_ids))
        project_dict = ProjectService.get_space_project_dict(product_spaces)

        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')

        date_diff = calculate_date_diff(start_time, end_time)
        if date_diff <= 365:
            st_y, st_w = get_year_week(start_time)
            end_y, end_w = get_year_week(end_time)

            filter_result = CrontabTestPlanCaseTrend.objects.filter(project__in=project_ids).filter(
                week__isnull=False).filter(Q(year=st_y, week__gte=st_w) | Q(year__gt=st_y)).filter(
                Q(year=end_y, week__lte=end_w) | Q(year__lte=end_y))

            query_result = filter_result.values('year', 'week', 'project').annotate(
                total_test_plan_avg_cases=Sum('test_plan_avg_cases'),
                total_test_plan_avg_cases_exec=Sum('test_plan_avg_cases_exec'),
                total_requirement_avg_cases=Sum('requirement_avg_cases'),
                total_test_plan_sum_cases=Sum('test_plan_sum_cases'),
                total_test_plan_sum_cases_exec=Sum('test_plan_sum_cases_exec')
            )

            # 构建统计结果数据结构
            statistics = []
            year_weeks = sorted(set((result['year'], result['week']) for result in query_result))
            for project in project_ids:
                statistic = {
                    'project': project,
                    'name': project_dict[project],
                    'total_test_plan_avg_cases': [0] * len(year_weeks),
                    'total_test_plan_avg_cases_exec': [0] * len(year_weeks),
                    'total_requirement_avg_cases': [0] * len(year_weeks),
                    'total_test_plan_sum_cases': [0] * len(year_weeks),
                    'total_test_plan_sum_cases_exec': [0] * len(year_weeks),
                }

                for result in query_result:
                    if result['project'] == project:
                        year = result['year']
                        week = result['week']
                        index = list(year_weeks).index((year, week))

                        statistic['total_test_plan_avg_cases'][index] = result['total_test_plan_avg_cases'] or 0
                        statistic['total_test_plan_avg_cases_exec'][index] = result[
                                                                                 'total_test_plan_avg_cases_exec'] or 0
                        statistic['total_requirement_avg_cases'][index] = result['total_requirement_avg_cases'] or 0
                        statistic['total_test_plan_sum_cases'][index] = result['total_test_plan_sum_cases'] or 0
                        statistic['total_test_plan_sum_cases_exec'][index] = result[
                                                                                 'total_test_plan_sum_cases_exec'] or 0

                statistics.append(statistic)
            response_data = {
                'date_list': (f"{year}-{week}W" for year, week in year_weeks),
                'project': statistics,
            }
        else:
            start_date = datetime.datetime.strptime(start_time, "%Y-%m-%d")
            end_date = datetime.datetime.strptime(end_time, "%Y-%m-%d")
            st_y = start_date.year
            st_m = start_date.month
            end_y = end_date.year
            end_m = end_date.month
            filter_result = CrontabTestPlanCaseTrend.objects.filter(project__in=project_ids).filter(
                month__isnull=False).filter(Q(year=st_y, month__gte=st_m) | Q(year__gt=st_y)).filter(
                Q(year=end_y, month__lte=end_m) | Q(year__lte=end_y))

            query_result = filter_result.values('year', 'month', 'project').annotate(
                total_test_plan_avg_cases=Sum('test_plan_avg_cases'),
                total_test_plan_avg_cases_exec=Sum('test_plan_avg_cases_exec'),
                total_requirement_avg_cases=Sum('requirement_avg_cases'),
                total_test_plan_sum_cases=Sum('test_plan_sum_cases'),
                total_test_plan_sum_cases_exec=Sum('test_plan_sum_cases_exec')
            )
            # 构建统计结果数据结构
            statistics = []
            year_months = sorted(set((result['year'], result['month']) for result in query_result))
            for project in project_ids:
                statistic = {
                    'project': project,
                    'name': project_dict[project],
                    'total_test_plan_avg_cases': [0] * len(year_months),
                    'total_test_plan_avg_cases_exec': [0] * len(year_months),
                    'total_requirement_avg_cases': [0] * len(year_months),
                    'total_test_plan_sum_cases': [0] * len(year_months),
                    'total_test_plan_sum_cases_exec': [0] * len(year_months),
                }

                for result in query_result:
                    if result['project'] == project:
                        year = result['year']
                        week = result['month']
                        index = list(year_months).index((year, week))

                        statistic['total_test_plan_avg_cases'][index] = result['total_test_plan_avg_cases'] or 0
                        statistic['total_test_plan_avg_cases_exec'][index] = result[
                                                                                 'total_test_plan_avg_cases_exec'] or 0
                        statistic['total_requirement_avg_cases'][index] = result['total_requirement_avg_cases'] or 0
                        statistic['total_test_plan_sum_cases'][index] = result['total_test_plan_sum_cases'] or 0
                        statistic['total_test_plan_sum_cases_exec'][index] = result[
                                                                                 'total_test_plan_sum_cases_exec'] or 0

                statistics.append(statistic)
            response_data = {
                'date_list': (f"{year}-{month}M" for year, month in year_months),
                'project': statistics,
            }

        return response.Response(data=response_data)
