# coding=utf-8
"""
Created on 2018-01-09

@author: z<PERSON><PERSON><PERSON>
"""

from business.common.system_config_service import SystemConfigService
from teamvision.api.project.viewmodel.project_statistics_charts.vm_piechart import VM_PieChart
from business.home.home_statistics_service import HomeStatisticsService
from teamvision.api.home.viewmodel.home_statistics_charts.vm_highchart import VM_HighChart
from teamvision.project.models import ProjectTaskStatus


class VM_FortestingStatusPieChart(VM_HighChart):
    """
    classdocs
    """

    def __init__(self,request):
        """
        Constructor
        """
        VM_PieChart.__init__(VM_FortestingStatusPieChart,0,0)
        self.chart_id=0
        self.project_id=0
        self.version_id=0
        self.request = request
        self.chart_type = 'column'
        self.chart_title = ''
        self.show_legend = False
        self.legend_layout = "vertical"
        self.legend_align = "right"
        self.series_name = '测试需求数量'
        self.xaxis = self.chart_xaxis()
        self.yaxis = self.chart_yaxis()
        self.series_data = self.get_series()

    def chart_xaxis(self):
        status_name = list()
        status_ids = self.status_ids()
        for status_id in status_ids:
            dm_fortesting_status = ProjectTaskStatus.objects.all().filter(Type=2).filter(Status=(int(status_id)))[0]
            status_name.append(dm_fortesting_status.Desc)
        return status_name

    def status_ids(self):
        fortesting_data = HomeStatisticsService.fortesting_count_bystatus(self.request)
        fortesting_ids = list()
        for data in fortesting_data:
            if not data['Status'] in fortesting_ids:
                fortesting_ids.append(data['Status'])
        return fortesting_ids

    def chart_yaxis(self):
        result = list()
        return result

    def chart_tooltip(self):
        return ""

    def get_series(self):
        result = list()
        fortesting_count = self.get_series_data()
        result.append(fortesting_count)
        return result


    def get_series_data(self):
        result = dict()
        result['name'] = '测试需求数量'
        result['data'] = list()
        status_data = HomeStatisticsService.fortesting_count_bystatus(self.request)
        for data_item in status_data:
            result['data'].append(data_item.get('TotalCount'))
        return result


        