from teamvision.project.models import Project, ProjectTestPlan, ProjectTestCase
from teamvision.ci.models import AutoCase
from business.common.datetime_service import DateTimeService
from django.db.models import Q


class HightChartStackedColumnChart(object):
    '''
        create 20200728 zhangpeng21
        type: 1=demand; 2=test
    '''

    def __init__(self, project_list, chart_type, days, title):
        self.project_list = project_list
        self.chart_type = chart_type
        self.days = days
        self.title = title
        self.categories = []
        self.series = self.get_series()

    def get_series(self):
        starttime, endtime = DateTimeService.get_starttime_endtime(self.days)

        if self.chart_type == 1:
            pass

        if self.chart_type == 2:
            all_project = Project.objects.filter(id__in=self.project_list).filter(IsActive=1).values("id", "PBTitle")
            all_project.count()
            days_testpaln = ProjectTestPlan.objects.filter(Project__in=self.project_list).filter(CreationTime__range=(starttime, endtime))
            days_testpaln.count()
            categories = []

            testplan_new = {
                "name": "新建",
                "data": []
            }
            testplan_process = {
                "name": "测试中",
                "data": []
            }
            testplan_fin = {
                "name": "测试完成",
                "data": []
            }

            for i in all_project:
                testplan_new_count = days_testpaln.filter(Project=i["id"]).filter(Status=1).count()
                testplan_process_count = days_testpaln.filter(Project=i["id"]).filter(Status=2).count()
                testplan_fin_count = days_testpaln.filter(Project=i["id"]).filter(Status=3).count() + days_testpaln.filter(
                    Project=i["id"]).filter(Status=4).count()

                # if testplan_new_count == 0 and testplan_process_count == 0 and testplan_fin_count == 0:
                #    pass
                # else:
                categories.append(i["PBTitle"])
                testplan_new["data"].append(testplan_new_count)
                testplan_process["data"].append(testplan_process_count)
                testplan_fin["data"].append(testplan_fin_count)

        self.categories = categories
        return [testplan_new, testplan_process, testplan_fin]

    @staticmethod
    def obj2dict(obj):
        result = {}
        result["title"] = obj.title
        result["categories"] = obj.categories
        result["series"] = obj.series
        return result


class HightChartBaseColumnChart(object):
    def __init__(self, project_list, start_time, end_time, title):
        self.project_list = project_list
        self.start_time = start_time
        self.end_time = end_time
        self.title = title
        self.categories = []
        self.series = self.get_series()

    def get_series(self):
        all_project = Project.objects.filter(id__in=self.project_list).filter(IsActive=1).values("id", "PBTitle")
        all_project.count()

        days_testcase = ProjectTestCase.objects.filter(Project__in=self.project_list).filter(
            CreationTime__range=(self.start_time, self.end_time)).filter(IsGroup=0).filter(IsActive=1).filter(~Q(Status=1))
        days_testcase.count()

        days_autocase = AutoCase.objects.filter(ProjectID__in=self.project_list).filter(
            CreateTime__range=(self.start_time, self.end_time)).filter(IsActive=1)
        days_autocase.count()

        categories = []
        testcase_new = {
            "name": "测试用例",
            "data": []
        }
        autocase_new = {
            "name": "自动化Case",
            "data": []
        }

        for project in all_project:
            testcase_count = days_testcase.filter(Project=project["id"]).count()
            autocase_count = days_autocase.filter(ProjectID=project["id"]).count()

            categories.append(project["PBTitle"])
            testcase_new["data"].append(testcase_count)
            autocase_new["data"].append(autocase_count)

        self.categories = categories
        return [testcase_new, autocase_new]

    @staticmethod
    def obj2dict(obj):
        result = {}
        result["title"] = obj.title
        result["categories"] = obj.categories
        result["series"] = obj.series
        return result
