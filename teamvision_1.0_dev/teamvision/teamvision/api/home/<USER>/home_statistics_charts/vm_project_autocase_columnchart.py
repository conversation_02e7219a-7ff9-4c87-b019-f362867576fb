# coding=utf-8
'''
Created on 2018-01-09

@author: <PERSON><PERSON><PERSON><PERSON>
'''

from business.common.system_config_service import SystemConfigService
from teamvision.api.project.viewmodel.project_statistics_charts.vm_piechart import VM_PieChart
from business.home.home_statistics_service import HomeStatisticsService
from teamvision.api.home.viewmodel.home_statistics_charts.vm_highchart import VM_HighChart
from teamvision.project.models import Project
from business.project.project_service import ProjectService


class VM_ProjectAutoCaseColumnChart(VM_HighChart):
    '''
    classdocs
    '''

    def __init__(self, request):
        '''
        Constructor
        '''
        VM_PieChart.__init__(VM_ProjectAutoCaseColumnChart, 0, 0)
        self.chart_id = 0
        self.project_id = 0
        self.version_id = 0
        self.request = request
        self.chart_type = 'column'
        self.chart_title = ''
        self.show_legend = False
        self.legend_layout = "vertical"
        self.legend_align = "right"
        self.series_name = '项目自动化用例数量'
        self.xaxis = self.chart_xaxis()
        self.yaxis = self.chart_yaxis()
        self.series_data = self.get_series()

    def chart_xaxis(self):
        project_names = list()
        status_ids = self.project_ids()
        for project_id in status_ids:
            dm_project = Project.objects.get(project_id)
            if dm_project:
                project_names.append(dm_project.PBTitle)
        return project_names

    def project_ids(self):
        product_spaces = self.request.META.get("HTTP_PRODUCT_SPACE")
        project_list = ProjectService.get_project_ids_by_space(product_spaces)
        project_testcases = HomeStatisticsService.auto_case_counts(project_list)
        project_ids = list()
        for data in project_testcases:
            if not data['ProjectID'] in project_ids:
                project_ids.append(data['ProjectID'])
        return project_ids

    def chart_yaxis(self):
        result = list()
        return result

    def chart_tooltip(self):
        return ""

    def get_series(self):
        result = list()
        project_version_counts = self.get_series_data()
        result.append(project_version_counts)
        return result

    def get_series_data(self):
        product_spaces = self.request.META.get("HTTP_PRODUCT_SPACE")
        project_list = ProjectService.get_project_ids_by_space(product_spaces)
        result = dict()
        result['name'] = '项目自动化用例数量'
        result['data'] = list()
        status_data = HomeStatisticsService.auto_case_counts(project_list)
        for data_item in status_data:
            result['data'].append(data_item.get('TotalCount'))
        return result
