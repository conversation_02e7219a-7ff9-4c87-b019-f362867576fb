#coding=utf-8
'''
Created on 2021-01-27

@author: rain
'''

from teamvision.api.project.viewmodel.project_statistics_charts.vm_piechart import VM_BarChart
from business.home.home_statistics_service import HomeStatisticsService
from teamvision.api.home.viewmodel.home_statistics_charts.vm_highchart import VM_Bar_HighChart

class VM_ProjectAutoCaseCountChart(VM_Bar_HighChart):
    '''
    classdocs
    '''

    def __init__(self,request,type):
        '''
        Constructor
        '''
        VM_BarChart.__init__(VM_ProjectAutoCaseCountChart,0,0,type)
        self.chart_id=0
        self.project_id=0
        self.version_id=0
        self.request = request
        self.chart_type = 'line'
        self.chart_title = ''
        self.show_legend = False
        self.legend_layout = "vertical"
        self.legend_align = "right"
        self.series_name = ''
        self.xaxis = self.chart_xaxis()
        self.yaxis = self.chart_yaxis()
        self.series_data = self.get_series()
        self.type = type

    def chart_xaxis(self):
        project_names = list()
        status_ids = self.project_ids()
        for project_info in status_ids:
            if project_info.__dict__.get('PBTitle') is not None:
                project_names.append(str(project_info.__dict__.get('id'))+'_'+project_info.__dict__.get('PBTitle'))
        return project_names

    def project_ids(self):
        return HomeStatisticsService.testpoint_testplan_count_byproject_id(self.request)

    def chart_yaxis(self):
        result = list()
        return result

    def chart_tooltip(self):
        return ""

    def get_series(self):
        result = list()
        project_version_counts = self.get_series_data()
        result.append(project_version_counts)
        return result

    def get_series_data(self):
        result,project_ids = dict(),list()
        result['name'] = '自动化case'
        result['data'] = list()
        project_info = self.project_ids()
        for _project in project_info:
            project_ids.append(_project.__dict__.get('id'))
        status_data = HomeStatisticsService.testpoint_autocase_count_byproject(self.request,self.type,project_ids)
        for data_item in status_data:
            result['data'].append(data_item.get('TotalCount'))
        return result


