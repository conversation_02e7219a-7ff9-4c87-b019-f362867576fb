# coding=utf-8
"""
Created on 2018-01-09

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from business.common.system_config_service import SystemConfigService
from teamvision.api.project.viewmodel.project_statistics_charts.vm_piechart import VM_PieChart
from business.home.home_statistics_service import HomeStatisticsService
from teamvision.api.home.viewmodel.home_statistics_charts.vm_highchart import VM_HighChart
from teamvision.project.models import Project


class VM_ProjectTestCaseColumnChart(VM_HighChart):
    """
      class docs
    """

    def __init__(self, request):
        '''
        Constructor
        '''
        VM_PieChart.__init__(VM_ProjectTestCaseColumnChart, 0, 0)
        self.project_test_case = HomeStatisticsService.testpoint_count_byproject(request)
        self.chart_id = 0
        self.project_id = 0
        self.version_id = 0
        self.request = request
        self.chart_type = 'column'
        self.chart_title = ''
        self.show_legend = False
        self.legend_layout = "vertical"
        self.legend_align = "right"
        self.series_name = '项目用例数量'
        self.xaxis = self.chart_xaxis()
        self.yaxis = self.chart_yaxis()
        self.series_data = self.get_series()

    def chart_xaxis(self):
        project_names, project_dt = list(), dict()
        status_ids = self.project_ids()
        project_info = Project.objects.all()
        for _temp in project_info:
            project_dt[_temp.__dict__.get('id')] = _temp.__dict__.get('PBTitle')
        for project_id in status_ids:
            if project_dt.get(project_id):
                project_names.append(project_dt.get(project_id))
        return project_names

    def project_ids(self):
        project_testcases = self.project_test_case
        project_ids = list()
        for data in project_testcases:
            if not data['Project'] in project_ids:
                project_ids.append(data['Project'])
        return project_ids

    def chart_yaxis(self):
        result = list()
        return result

    def chart_tooltip(self):
        return ""

    def get_series(self):
        result = list()
        project_version_counts = self.get_series_data()
        result.append(project_version_counts)
        return result

    def get_series_data(self):
        result = dict()
        result['name'] = '项目用例数量'
        result['data'] = list()
        status_data = self.project_test_case
        for data_item in status_data:
            result['data'].append(data_item.get('TotalCount'))
        return result
