# coding=utf-8

from business.common.system_config_service import SystemConfigService
from teamvision.api.project.viewmodel.project_statistics_charts.vm_piechart import VM_PieChart
from business.home.home_statistics_service import HomeStatisticsService
from teamvision.api.home.viewmodel.home_statistics_charts.vm_highchart import VM_HighChart
from teamvision.project.models import Project
from collections import defaultdict


class VM_ProjectTestCaseRepeatrateChart(VM_HighChart):
    '''
    classdocs
    '''

    def __init__(self, request):
        '''
        Constructor
        '''
        VM_PieChart.__init__(VM_ProjectTestCaseRepeatrateChart, 0, 0)
        self.request = request
        self.projects_name = self.get_projects_name()
        self.project_case_repeat_rate, self.project_create_date, self.project_ids = self.get_status()
        self.xAxis = self.get_xAxis()
        self.series = self.get_series()

    def get_status(self):
        status_data = HomeStatisticsService.project_testcase_repeatrate(self.request)
        project_case_repeat_rate = defaultdict(list)
        project_create_date = list()
        project_ids = list()
        for project_id, create_date, repeat_rate in status_data:
            tmp = float('%.2f' % (repeat_rate * 100))
            if tmp > 0:
                project_case_repeat_rate[project_id].append(tmp)
                if not create_date in project_create_date:
                    project_create_date.append(create_date)
                if not project_id in project_ids:
                    project_ids.append(project_id)
        return project_case_repeat_rate, project_create_date, project_ids

    def get_projects_name(self):
        projects_name = dict()
        result = Project.objects.all().values("id", "PBTitle")
        for item in result:
            projects_name[item['id']] = item['PBTitle']
        return projects_name

    def get_xAxis(self):
        result = dict()
        result['data'] = self.project_create_date
        return result

    def get_series(self):
        result = list()
        for project_id, create_date in self.project_case_repeat_rate.items():
            case_repeat_rate = dict()
            case_repeat_rate['name'] = self.projects_name[project_id]
            case_repeat_rate['data'] = create_date
            case_repeat_rate['type'] = "line"
            data_length = len(self.xAxis['data']) - len(case_repeat_rate['data'])

            if data_length > 0:
                for i in range(data_length):
                    case_repeat_rate['data'].insert(0, '')

            result.append(case_repeat_rate)
        return result

