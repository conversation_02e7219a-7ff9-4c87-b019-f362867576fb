#coding=utf-8
'''
Created on 2018-01-09

@author: <PERSON><PERSON><PERSON><PERSON>
'''

from business.common.system_config_service import SystemConfigService
from teamvision.api.project.viewmodel.project_statistics_charts.vm_piechart import VM_PieChart
from business.home.home_statistics_service import HomeStatisticsService
from teamvision.api.home.viewmodel.home_statistics_charts.vm_highchart import VM_HighChart
from teamvision.project.models import Project

class VM_ProjectVersionColumnChart(VM_HighChart):
    '''
    classdocs
    '''

    def __init__(self,request):
        '''
        Constructor
        '''
        VM_PieChart.__init__(VM_ProjectVersionColumnChart,0,0)
        self.chart_id=0
        self.project_id=0
        self.version_id=0
        self.request = request
        self.chart_type = 'column'
        self.chart_title = ''
        self.show_legend = False
        self.legend_layout = "vertical"
        self.legend_align = "right"
        self.series_name = '项目版本数量'
        self.xaxis = self.chart_xaxis()
        self.yaxis = self.chart_yaxis()
        self.series_data = self.get_series()

    def chart_xaxis(self):
        project_names = list()
        status_ids = self.project_ids()
        for project_id in status_ids:
            dm_project = Project.objects.get(project_id)
            project_names.append(dm_project.PBTitle)
        return project_names

    def project_ids(self):
        project_versions = HomeStatisticsService.project_version_counts()
        project_ids = list()
        for data in project_versions:
            if not data['VProjectID'] in project_ids:
                project_ids.append(data['VProjectID'])
        return project_ids

    def chart_yaxis(self):
        result = list()
        return result

    def chart_tooltip(self):
        return ""

    def get_series(self):
        result = list()
        project_version_counts = self.get_series_data()
        result.append(project_version_counts)
        return result


    def get_series_data(self):
        result = dict()
        result['name'] = '项目版本数量'
        result['data'] = list()
        status_data = HomeStatisticsService.project_version_counts()
        for data_item in status_data:
            result['data'].append(data_item.get('TotalCount'))
        return result


        