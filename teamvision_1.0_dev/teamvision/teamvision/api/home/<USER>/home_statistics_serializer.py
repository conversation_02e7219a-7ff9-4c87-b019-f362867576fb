#coding=utf-8
"""
Created on 2018-12-13

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers



class FortestingTrendStatisticsSerializer(serializers.Serializer):
    chart_id=serializers.IntegerField()
    project_id=serializers.IntegerField()
    version_id=serializers.IntegerField()
    chart_type=serializers.CharField()
    chart_title=serializers.CharField()
    chart_sub_title=serializers.CharField()
    xaxis=serializers.ListField()
    yaxis=serializers.ListField()
    tooltip=serializers.CharField()
    series_data=serializers.ListField()
    
    def save(self):
        raise Exception("only get request")


class FortestingStatusColumnStatisticsSerializer(serializers.Serializer):
    chart_id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    version_id = serializers.IntegerField()
    chart_type = serializers.CharField()
    chart_title = serializers.CharField()
    show_legend = serializers.<PERSON><PERSON>anField()
    series_name = serializers.CharField()
    series_data = serializers.ListField()
    legend_layout = serializers.CharField()
    legend_align = serializers.CharField()
    xaxis = serializers.ListField()
    yaxis = serializers.ListField()

    def save(self):
        raise Exception("only get request")


class ProjectVersionColumnStatisticsSerializer(serializers.Serializer):
    chart_id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    version_id = serializers.IntegerField()
    chart_type = serializers.CharField()
    chart_title = serializers.CharField()
    show_legend = serializers.BooleanField()
    series_name = serializers.CharField()
    series_data = serializers.ListField()
    legend_layout = serializers.CharField()
    legend_align = serializers.CharField()
    xaxis = serializers.ListField()
    yaxis = serializers.ListField()

    def save(self):
        raise Exception("only get request")


class ProjectTestcaseRepeatrateStatisticsSerializer(serializers.Serializer):
    series = serializers.ListField()
    xAxis = serializers.JSONField()
