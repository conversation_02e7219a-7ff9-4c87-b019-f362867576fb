# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import path, re_path
from teamvision.api.home.views import home_statistics_view, home_kanban_statistics_view

api_home_statistics_router = [
    re_path(r"statistics/fortesting_status_column$", home_statistics_view.FortestingStatusPie.as_view()),
    re_path(r"statistics/project_version_column$", home_statistics_view.ProjectVersionColumnView.as_view()),
    re_path(r"statistics/project_testcase_column$", home_statistics_view.ProjectTestcaseColumnView.as_view()),
    re_path(r"statistics/project_autocase_column$", home_statistics_view.ProjectAutocaseColumnView.as_view()),
    re_path(r"statistics/case_total_count$", home_statistics_view.ProjectCaseTotalCountView.as_view()),
    re_path(r"statistics/case_repeat_rate$", home_statistics_view.ProjectCaseRepeatRate.as_view()),
    re_path(r"statistics/demand/(?P<days>.+)", home_statistics_view.HomeDemandView.as_view()),
    re_path(r"statistics/demandtroughput/(?P<days>.+)", home_statistics_view.HomeDemandThroughputView.as_view()),
    re_path(r"statistics/projectdemandstatus/(?P<days>.+)", home_statistics_view.HomeProjectDemandStatusView.as_view()),
    re_path(r"statistics/demandavgleadtime/(?P<days>.+)", home_statistics_view.DeamndAvgLeadTimeView.as_view()),
    re_path(r"statistics/testcasecount?", home_statistics_view.HomeTestCaseCountView.as_view()),
    re_path(r"statistics/testtaskstatus$", home_statistics_view.HomeForTestingTaskStatusView.as_view()),
    re_path(r"statistics/testeff/(?P<days>.+)", home_statistics_view.HomeTestEffView.as_view()),
    re_path(r"statistics/testwaittime/(?P<days>.+)", home_statistics_view.HomeTestWaitTimeView.as_view()),
    re_path(r"statistics/demandstatus$", home_statistics_view.HomeDemandStatusView.as_view()),
    re_path(r"teststatus/(?P<days>.+)", home_statistics_view.HomeTestStatusView.as_view()),
    re_path(r"projecttestcasecount?", home_statistics_view.HomeProjectTestCaseCountView.as_view()),
    re_path(r"testefficiency/(?P<days>.+)", home_statistics_view.HomeTestEfficiencyView.as_view()),
]
