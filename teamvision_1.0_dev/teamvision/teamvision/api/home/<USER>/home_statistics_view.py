# coding=utf-8
"""
Created on 2018-1-5
@author: <PERSON><PERSON><PERSON><PERSON>
"""
from django.db.models import QuerySet, Count
from rest_framework import generics, response
from teamvision.api.home.serializer import home_statistics_serializer
from rest_framework.permissions import AllowAny
from teamvision.project import models
from teamvision.ci.models import AutoCase, CITask, CITaskHistory, AutoTestingTaskResult, AutoCaseResult
from teamvision.api.home.viewmodel.home_statistics_charts.vm_fortesting_status_columnchart import \
    VM_FortestingStatusPieChart
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_version_columnchart import \
    VM_ProjectVersionColumnChart
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_testcase_columnchart import \
    VM_ProjectTestCaseColumnChart
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_autocase_columnchart import \
    VM_ProjectAutoCaseColumnChart
from teamvision.api.home.viewmodel.home_statistics_charts.vm_project_testcase_repeatratechart import \
    VM_ProjectTestCaseRepeatrateChart
from teamvision.project.models import Project, ProjectTestCase
from rest_framework.views import APIView
from teamvision.api.home.viewmodel.home_statistics_charts.vm_home_highchart import HightChartStackedColumnChart, \
    HightChartBaseColumnChart
from business.project.project_service import ProjectService
from business.project.product_space_service import ProductSpaceService
from business.common.datetime_service import DateTimeService
from business.ci.ci_task_service import CITaskService
from django.db import connection
from business.project.project_testcase_service import TestCaseService
from django.db.models import Q


# from teamvision.api.crontab import apschduler_jobs
# from teamvision.api.crontab.apschduler_jobs import scheduler


class FortestingStatusPie(generics.RetrieveAPIView):
    """
    /api/home/<USER>/fortesting_status_pie
    获取每日新增以及解决的bug趋势图
    """
    serializer_class = home_statistics_serializer.FortestingStatusColumnStatisticsSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        chart = VM_FortestingStatusPieChart(self.request)
        return chart


class ProjectVersionColumnView(generics.RetrieveAPIView):
    """
    /api/home/<USER>/fortesting_status_pie
    获取每日新增以及解决的bug趋势图
    """
    serializer_class = home_statistics_serializer.ProjectVersionColumnStatisticsSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        chart = VM_ProjectVersionColumnChart(self.request)
        return chart


class ProjectTestcaseColumnView(generics.RetrieveAPIView):
    """
    /api/home/<USER>/fortesting_status_pie
    获取每日新增以及解决的bug趋势图
    """
    serializer_class = home_statistics_serializer.ProjectVersionColumnStatisticsSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        chart = VM_ProjectTestCaseColumnChart(self.request)
        return chart


class ProjectAutocaseColumnView(generics.RetrieveAPIView):
    """
    /api/home/<USER>/fortesting_status_pie
    获取每日新增以及解决的bug趋势图
    """
    serializer_class = home_statistics_serializer.ProjectVersionColumnStatisticsSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        chart = VM_ProjectAutoCaseColumnChart(self.request)
        return chart


class ProjectCaseTotalCountView(APIView):
    """
    /api/home/<USER>/case_total_count
    获取当前空间下所有功能测试用例/自动化测试用例
    """
    serializer_class = None
    permission_classes = [AllowAny]

    #    def get(self, request, *args, **kwargs):
    #        mind_files = models.ProjectXmindFile.objects.filter(FileType=1)
    #        mind_file_ids = [file.id for file in mind_files]
    #        all_testcsae_count = len(models.ProjectXmindTopic.objects.all().filter(MindFileID__in=mind_file_ids).filter(IsLeaf=1).values("OriginalID").distinct())
    #        all_autocase_count = len(AutoCase.objects.all().filter(IsActive=1))
    #        result = dict()
    #        result["CaseCount"] = all_testcsae_count
    #        result["AutoCount"] = all_autocase_count
    #        return response.Response(result)

    def get(self, request, *args, **kwargs):
        productspaces = request.META.get("HTTP_PRODUCT_SPACE")

        all_testcsae_count = 0
        project_ids = models.Project.objects.filter(ProductSpace=productspaces).filter(IsActive=1).values_list('id',
                                                                                                               flat=True)

        for project_id in project_ids:
            count = models.ProjectTestCase.objects.filter(Project=project_id).filter(IsActive=1).filter(
                ~Q(Status=1)).filter(IsGroup=0).count()
            all_testcsae_count = all_testcsae_count + count

        all_autocase_count = 0
        for project_id in project_ids:
            count = AutoCase.objects.filter(ProjectID=project_id).filter(IsActive=1).count()
            all_autocase_count = all_autocase_count + count

        result = dict()
        result["CaseCount"] = all_testcsae_count
        result["AutoCount"] = all_autocase_count
        return response.Response(result)


class ProjectTestcaseRankView(APIView):
    """
    /api/home/<USER>/project_testcase_rank
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        all_project = Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")

        spaces_cases = ProjectTestCase.objects.filter(IsActive=1).filter(IsGroup=0).filter(Project__in=project_ids)
        projects_cases = spaces_cases.filter(Status=0)
        projects_cases_archive = spaces_cases.filter(Status=1)

        spaces_auto_cases = AutoCase.objects.filter(IsActive=1).filter(ProjectID__in=project_ids)

        project_list = []
        projects_cases_count_list = []
        projects_cases_archive_count_list = []
        project_auto_case_count_list = []
        project_auto_case_exec_count_list = []
        auto_case_exec_dict = self.get_auto_execute_case(project_ids)
        for project in all_project:
            project_list.append(project["PBTitle"])
            projects_cases_count_list.append(projects_cases.filter(Project=project["id"]).count())
            projects_cases_archive_count_list.append(projects_cases_archive.filter(Project=project["id"]).count())
            project_auto_case_count_list.append(spaces_auto_cases.filter(ProjectID=project["id"]).count())
            count = auto_case_exec_dict.get(project["id"])
            if count:
                project_auto_case_exec_count_list.append(count)
            else:
                project_auto_case_exec_count_list.append(0)

        resp_body = {
            "xAxis": [
                {
                    "data": project_list
                }
            ],
            "series": [
                {
                    "data": projects_cases_count_list
                },
                {
                    "data": projects_cases_archive_count_list
                },
                {
                    "data": project_auto_case_count_list
                },
                {
                    "data": project_auto_case_exec_count_list
                }
            ]
        }

        # productspaces = request.META.get("HTTP_PRODUCT_SPACE")
        # functionChart = VM_ProjectTestCaseColumnChart(self.request)
        # autoChart = VM_ProjectAutoCaseColumnChart(self.request)
        # autoExecuteCase = self.get_auto_execute_case(productspaces)
        # result = self.get_all_function_info(functionChart, autoChart, autoExecuteCase)

        return response.Response(data=resp_body)

    def get_all_function_info(self, functionChart, autoChart, autoExecuteCase):
        data_info = functionChart.series_data[0].get('data')
        xaxis_info = functionChart.xaxis

        temp_auto_case_dt = dict()
        auto_data_info = autoChart.series_data[0].get('data')
        auto_xaxis_info = autoChart.xaxis
        for i in range(len(auto_xaxis_info)):
            temp_auto_case_dt[auto_xaxis_info[i]] = auto_data_info[i]

        y_data, x_data, y_data_auto, y_auto_exe_case = list(), list(), list(), list()
        y_data_archive = list()
        for i in range(len(data_info)):
            if data_info[i] > 0:
                y_data.append(data_info[i])
                x_data.append(xaxis_info[i])
                if temp_auto_case_dt.get(xaxis_info[i]):
                    y_data_auto.append(temp_auto_case_dt.get(xaxis_info[i]))
                else:
                    y_data_auto.append(0)
                if autoExecuteCase.get(xaxis_info[i]):
                    y_auto_exe_case.append(autoExecuteCase.get(xaxis_info[i]))
                else:
                    y_auto_exe_case.append(0)
        auto_percent, execute_percent = list(), list()
        for i in range(len(y_data)):
            if y_data[i] > 0:
                if y_data_auto[i] / y_data[i] > 1:
                    auto_percent.append(90)
                    execute_percent.append(90)
                else:
                    auto_percent.append(round((y_data_auto[i] / y_data[i]) * 100, 2))
                    execute_percent.append(round((y_auto_exe_case[i] / y_data[i]) * 100, 2))
            else:
                auto_percent.append(0)
                execute_percent.append(0)

        temp_chart = {
            "xAxis": [
                {
                    "data": []
                }
            ],
            "series": [
                {
                    "data": []
                },
                {
                    "data": []
                },
                {
                    "data": []
                },
                {
                    "data": []
                }
            ]
        }

        temp_chart['xAxis'][0]["data"] = x_data
        temp_chart["series"][0]["data"] = y_data
        temp_chart["series"][1]["data"] = y_data
        temp_chart["series"][2]["data"] = y_data_auto
        temp_chart["series"][3]["data"] = y_auto_exe_case
        return temp_chart

    def get_auto_execute_case(self, project_ids):
        temp_dt, uuid_dt, case_dt = dict(), dict(), dict()

        ci_task = CITask.objects.filter(IsActive=1).filter(Project__in=project_ids)
        AutoTestingTaskResultInfo = AutoTestingTaskResult.objects.all()
        for _detail in AutoTestingTaskResultInfo:
            uuid_dt[_detail.TaskUUID] = _detail.id

        AutoCaseResultInfo = AutoCaseResult.objects.values('TaskResultID').annotate(Count('id'))
        for _detai2 in AutoCaseResultInfo:
            case_dt[_detai2.get('TaskResultID')] = [_detai2.get('id__count')]
        for _ci_task in ci_task:
            task_id = _ci_task.id
            project_id = _ci_task.Project
            ci_task_his = CITaskHistory.objects.get_task_history(task_id).order_by('-id')
            TaskUUID = 0
            for _temp_uuid in ci_task_his:
                if _temp_uuid.StartTime:
                    TaskUUID = _temp_uuid.TaskUUID
                    break
            AutoCaseResultCount = case_dt.get(uuid_dt.get(TaskUUID))
            if AutoCaseResultCount:
                if temp_dt.get(project_id):
                    temp_dt[project_id] = temp_dt[project_id] + AutoCaseResultCount[0]
                else:
                    temp_dt[project_id] = AutoCaseResultCount[0]
        return temp_dt


class HomeProjectAutoCasePercentView(APIView):
    """
    /api/home/<USER>/project_autocase_percent?st=2000-01-01&end=2022-01-01
    """
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        all_project = models.Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")

        project_list = []
        project_autocase_percent_list = []
        project_autocase_exec_percent_list = []
        project_autocase_exec_cover_percent_list = []

        test_case_all = models.ProjectTestCase.objects.filter(IsGroup=0).filter(~Q(Status=1)).filter(IsActive=1)
        auto_case_all = AutoCase.objects.filter(IsActive=1)

        cursor = connection.cursor()

        for project in all_project:
            project_list.append(project["PBTitle"])
            project_testcase_count = test_case_all.filter(Project=project["id"]).count()
            project_autocase_count = auto_case_all.filter(ProjectID=project["id"]).count()

            try:
                project_autocase_percent = float('%.2f' % ((project_autocase_count / project_testcase_count) * 100))
                if project_autocase_percent > 100:
                    project_autocase_percent = 100
            except ZeroDivisionError:
                project_autocase_percent = 0
            project_autocase_percent_list.append(project_autocase_percent)

            count_project_autocase_result_sql = "SELECT COUNT(DISTINCT TestCaseID) FROM autotesting_case_result  WHERE CreateTime > '{StartTime}' AND TaskResultID " + \
                                                "in (SELECT id FROM autotesting_task_result WHERE StageHistoryID " + \
                                                "in (SELECT id FROM ci_task_stage_history WHERE TaskID in (SELECT id FROM ci_task WHERE Project={Project})))"
            count_project_autocase_result_sql = count_project_autocase_result_sql.replace("{StartTime}", str(start_time))
            count_project_autocase_result_sql = count_project_autocase_result_sql.replace("{Project}", str(project["id"]))
            cursor.execute(count_project_autocase_result_sql)
            project_autocase_exec_count = cursor.fetchone()[0]

            try:
                project_autocase_exec_percent = float('%.2f' % (project_autocase_exec_count / project_autocase_count * 100))
            except ZeroDivisionError:
                project_autocase_exec_percent = 0
            project_autocase_exec_percent_list.append(project_autocase_exec_percent)
            # print(project_autocase_exec_count, project_autocase_count)

            try:
                project_autocase_exec_cover_percent = float('%.2f' % (project_autocase_exec_count / project_testcase_count * 100))
            except ZeroDivisionError:
                project_autocase_exec_cover_percent = 0
            project_autocase_exec_cover_percent_list.append(project_autocase_exec_cover_percent)

        result = {
            "xAxis": [
                {
                    "data": []
                }
            ],
            "series": [
                {
                    "data": []
                },
                {
                    "data": []
                },
                {
                    "data": []
                }
            ]
        }

        result["xAxis"][0]["data"] = project_list
        result["series"][0]["data"] = project_autocase_percent_list
        result["series"][1]["data"] = project_autocase_exec_percent_list
        result["series"][2]["data"] = project_autocase_exec_cover_percent_list

        return response.Response(data=result)


class HomeDemandView(APIView):
    def get(self, request, *args, **kwargs):
        filter_days = self.kwargs.get("days", 7)
        result = {}
        result["demandstatus"]["title"] = "需求状态分布"
        result["demandstatus"]["categories"] = ["新建", "测试中", "测试完成"]

        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)
        project_new = models.Project.objects.filter(CreationTime__gte=starttime, CreationTime__lte=endtime).filter(
            IsActive=1).values_list('id', flat=True)
        count_new = project_new.count
        project_process = models.Project.objects.filter(IsActive=1).values_list('id', flat=True)
        project_fin = models.Project.objects.filter(IsActive=1).values_list('id', flat=True)
        result["demandstatus"]["series"] = [{"name": 'kcache', "data": [5, 3, 4], },
                                            {"name": 'dorado', "data": [3, 4, 4], }]
        return response.Response(data=result)


class HomeTestStatusView(APIView):
    """
    /api/home/<USER>/teststatus/(?P<days>.+)
    """

    def get(self, request, *args, **kwargs):
        filter_days = self.kwargs.get("days", 7)

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        chart = HightChartStackedColumnChart(project_ids, 2, filter_days, "需求测试状态分布")
        return response.Response(data=HightChartStackedColumnChart.obj2dict(chart))


class HomeProjectTestCaseCountView(APIView):
    """
    /api/home/<USER>/projecttestcasecount?st=2020-01-01&en=2020-01-01
    """

    def get(self, request, *args, **kwargs):
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        chart = HightChartBaseColumnChart(project_ids, start_time, end_time, "新增用例数量")
        return response.Response(data=HightChartBaseColumnChart.obj2dict(chart))


class HomeTestEfficiencyView(APIView):
    """
    /api/home/<USER>/
    """

    def get(self, request, *args, **kwargs):
        filter_days = self.kwargs.get("days", 7)
        chart = HightChartStackedColumnChart(3, filter_days, "需求测试状态分布")
        return response.Response(data=HightChartBaseColumnChart.obj2dict(chart))


class HomeTestCaseCountView(APIView):
    """
    /api/home/<USER>/testcasecount?st=2020-01-01&et=2020-01-01
    """

    def get(self, request, *args, **kwargs):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)
        start_time = request.GET.get('st', '')
        end_time = request.GET.get('et', '')
        result = {}

        new_testcase_count = models.ProjectTestCase.objects.filter(Project__in=project_ids).filter(
            CreationTime__range=(start_time, end_time)).filter(IsGroup=0).filter(IsActive=1).filter(~Q(Status=1)).count()
        new_autocase_count = AutoCase.objects.filter(ProjectID__in=project_ids).filter(
            CreateTime__range=(start_time, end_time)).filter(IsActive=1).count()

        all_testcase = models.ProjectTestCase.objects.filter(Project__in=project_ids).filter(IsActive=1).values(
            "id", "Project", "Title", "Parent", "IsGroup")
        all_testcase_count = all_testcase.filter(IsGroup=0).count()
        all_testcase_count_valid = all_testcase.filter(IsGroup=0).filter(~Q(Status=1)).count()
        all_autocase_id_list = AutoCase.objects.filter(ProjectID__in=project_ids).filter(IsActive=1).values_list('id', flat=True)
        all_autocase_count = all_autocase_id_list.count()
        try:
            auto_percent = float('%.4f' % (all_autocase_count / all_testcase_count_valid))
        except ZeroDivisionError:
            auto_percent = 0

        auto_testcase_exec = AutoCaseResult.objects.filter(StartTime__range=(start_time, end_time)).filter(
            TestCaseID__in=all_autocase_id_list).values('TestCaseID').distinct().count()

        try:
            auto_exec_rate = round((auto_testcase_exec / all_autocase_count) * 100, 2)
        except ZeroDivisionError:
            auto_exec_rate = 0

        result["days"] = [start_time, end_time]
        result["new_testcase"] = new_testcase_count
        result["new_autocase"] = new_autocase_count
        result["new_autocase"] = new_autocase_count
        result["all_testcase"] = all_testcase_count
        result["all_testcase_valid"] = all_testcase_count_valid
        result["all_autocase"] = all_autocase_count
        result["auto_percent"] = auto_percent
        result["auto_exec_rate"] = auto_exec_rate
        result["repeat_rate"] = 0

        return response.Response(data=result)


class HomeForTestingTaskStatusView(APIView):
    """
    /api/home/<USER>/testtaskstatus
    """

    def get(self, request, *args, **kwargs):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        result = {}
        testtask = models.TestApplication.objects.filter(ProjectID__in=project_ids).filter(IsActive=1)
        result["test_waitto"] = testtask.filter(Status=1).count()
        result["test_wait"] = testtask.filter(Status=2).count()
        result["test_ing"] = testtask.filter(Status=3).count()
        result["test_done"] = testtask.filter(Status=4).count()
        result["test_online"] = testtask.filter(Status=5).count()

        # 3:开发中 4:已提测 5:测试中 6: 7:已上线
        demands = models.Requirement.objects.filter(ProjectID__in=project_ids).filter(IsActive=1)
        # result["demand_developing"] = demands.filter(Status=3).count()
        result["demand_waittest"] = demands.filter(Status=4).count()
        result["demand_testing"] = demands.filter(Status=5).count()
        result["demand_online"] = demands.filter(Status=7).count()
        result["demand_sum"] = result["demand_waittest"] + result["demand_testing"] + result["demand_online"]
        return response.Response(data=result)


class HomeDemandStatusView(APIView):
    """
    /api/home/<USER>/demandstatus
    """

    def get(self, request, *args, **kwargs):
        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        result = {}
        # 3:开发中 4:已提测 5:测试中 6: 7:已上线
        demands = models.Requirement.objects.filter(ProjectID__in=project_ids).filter(IsActive=1)
        # result["demand_developing"] = demands.filter(Status=3).count()
        result["demand_waittest"] = demands.filter(Status=4).count()
        result["demand_testing"] = demands.filter(Status=5).count()
        result["demand_online"] = demands.filter(Status=7).count()
        result["demand_sum"] = result["demand_waittest"] + result["demand_testing"] + result["demand_online"]
        return response.Response(data=result)


class HomeTestEffView(APIView):
    """
    /api/home/<USER>/testeff/(?P<days>.+)
    """

    def get(self, request, *args, **kwargs):
        result = {}
        filter_days = self.kwargs.get("days", 7)
        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        all_project = models.Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")
        project_list = []
        testplan_avg_casecount_list = []
        testplan_avg_testingtime_list = []

        for project in all_project:
            project_testplans_list = models.ProjectTestPlan.objects.filter(IsActive=1).filter(
                Project=project["id"]).filter(CreationTime__range=(starttime, endtime)).values("id", "Title", "CaseCount", "FinishTime",
                                                                                               "StartTime", "requireNum")
            project_list.append(project["PBTitle"])

            project_testplan_casecount_sum = 0
            project_testplan_testingtime_sum = 0
            for testplan in project_testplans_list:
                testplan_testtingtime = 0
                project_testplan_casecount_sum = project_testplan_casecount_sum + testplan["CaseCount"]
                if testplan["StartTime"] is not None and testplan["FinishTime"] is not None:
                    testplan_testtingtime = (testplan["FinishTime"] - testplan["StartTime"]).total_seconds()
                elif testplan["FinishTime"] is None and testplan["StartTime"] is not None:
                    testplan_testtingtime = (endtime - testplan["StartTime"]).total_seconds()
                elif testplan["StartTime"] is None:
                    pass
                project_testplan_testingtime_sum = project_testplan_testingtime_sum + testplan_testtingtime
                # print("testpaln_id= %s, start=%s, end=%s, testplan_testtingtime=%s" % (testplan["id"], testplan["StartTime"], testplan["FinishTime"], testplan_testtingtime))
            # print("project_testplan_testingtime_sum=", project_testplan_testingtime_sum)

            try:
                testplan_avg_casecount = project_testplan_casecount_sum // len(project_testplans_list)
                testplan_avg_casecount_list.append(testplan_avg_casecount)
            except ZeroDivisionError:
                testplan_avg_casecount_list.append(0)

            try:
                testplan_avg_testtingtime = project_testplan_testingtime_sum / len(project_testplans_list)
                testplan_avg_testingtime_list.append(float('%.2f' % (testplan_avg_testtingtime / 3600 / 24)))
            except ZeroDivisionError:
                testplan_avg_testingtime_list.append(0)

        result["projects"] = project_list
        result["project_testplan_casecount_avg"] = testplan_avg_casecount_list
        result["project_testplan_testtingtime_avg"] = testplan_avg_testingtime_list

        project_casecount_sum = 0
        project_count = 0
        for project_casecount in testplan_avg_casecount_list:
            project_casecount_sum = project_casecount_sum + project_casecount
            if project_casecount != 0:
                project_count = project_count + 1
        try:
            result["all_project_testplan_casecount_avg"] = project_casecount_sum // project_count
        except ZeroDivisionError:
            result["all_project_testplan_casecount_avg"] = 0

        project_testingtime_sum = 0
        project_count = 0
        for project_testingtime in testplan_avg_testingtime_list:
            project_testingtime_sum = project_testingtime_sum + project_testingtime
            if project_testingtime != 0:
                project_count = project_count + 1
        try:
            result["all_project_testplan_testingtime_avg"] = float('%.2f' % (project_testingtime_sum / project_count))
        except ZeroDivisionError:
            result["all_project_testplan_testingtime_avg"] = 0

        return response.Response(data=result)


class HomeTestWaitTimeView(APIView):
    """
    /api/home/<USER>/testwaittime/(?P<days>.+)
    """

    def get(self, request, *args, **kwargs):
        result = {}
        filter_days = self.kwargs.get("days", 7)
        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        all_project = models.Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")
        project_list = []

        testplan_avg_testingtime_list = []

        for project in all_project:
            project_list.append(project["PBTitle"])
            project_test_application_list = models.TestApplication.objects.filter(IsActive=1).filter(
                ProjectID=project["id"]).filter(CreationTime__range=(starttime, endtime)).values("id", "CommitTime",
                                                                                                 "TestingStartDate")
            project_all_application_testwait_sum = 0
            application_count = 0
            for test_application in project_test_application_list:
                if test_application["CommitTime"] is not None:
                    # print("%s - %s - %s" % (
                    #        test_application["id"], test_application["CommitTime"], test_application["TestingStartDate"]))
                    application_count = application_count + 1
                    if test_application["CommitTime"] is not None and test_application["TestingStartDate"] is not None:
                        testwaittime = (test_application["TestingStartDate"] - test_application[
                            "CommitTime"]).total_seconds()
                    elif test_application["TestingStartDate"] is not None:
                        testwaittime = (endtime - test_application["CommitTime"]).total_seconds()
                    project_all_application_testwait_sum = project_all_application_testwait_sum + testwaittime
            try:
                project_testwait_avg = float('%.2f' % (project_all_application_testwait_sum / 3600 / 24 / application_count))
            except ZeroDivisionError:
                project_testwait_avg = 0

            testplan_avg_testingtime_list.append(project_testwait_avg)
        result = {
            "projects": project_list,
            "testplan_avg_testingtime_list": testplan_avg_testingtime_list
        }

        return response.Response(data=result)


class HomeDemandThroughputView(APIView):
    """
    /api/home/<USER>/demandtroughput/<days>
    """

    def get(self, request, *args, **kwargs):
        result = {}
        filter_days = self.kwargs.get("days", 7)
        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        demands = models.Requirement.objects.filter(ProjectID__in=project_ids).filter(IsActive=1).filter(
            CreationTime__range=(starttime, endtime))
        # 1:待评审 2:待排期 3:开发中 4:已提测 5:测试中 6:待上线 7:已上线
        result["demand_finish"] = demands.filter(Status=7).count()
        result["demand_waitprocess"] = demands.filter(Status=1).count() + demands.filter(Status=2).count()
        result["demand_processing"] = demands.filter(Status=3).count() + demands.filter(
            Status=4).count() + demands.filter(Status=5).count() + demands.filter(Status=6).count()
        result["demand_sum"] = result["demand_finish"] + result["demand_waitprocess"] + result["demand_processing"]
        try:
            result["finish_ratio"] = float('%.2f' % (result["demand_finish"] / result["demand_sum"])) * 100
        except ZeroDivisionError:
            result["finish_ratio"] = 0.0
        return response.Response(data=result)


class HomeProjectDemandStatusView(APIView):
    """
    /api/home/<USER>/projectdemandstatus/<days>
    """

    def get(self, request, *args, **kwargs):
        result = {}
        filter_days = self.kwargs.get("days", 7)
        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        all_project = models.Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")
        project_list = []
        new_create_list = []
        wait_proecesing_list = []
        finish_list = []
        processing_list = []
        for project in all_project:
            demands = models.Requirement.objects.filter(IsActive=1).filter(ProjectID=project["id"]).filter(
                CreationTime__range=(starttime, endtime)).values("id", "Status")
            # 1:待评审 2:待排期 3:开发中 4:已提测 5:测试中 6:待上线 7:已上线
            new_create = demands.filter(Status=1).count()
            wait_process = new_create + demands.filter(Status=2).count()
            finish = demands.filter(Status=7).count()
            # processing = demands.filter(Status=3).count() + demands.filter(Status=4).count() + demands.filter(Status=5).count() + demands.filter(Status=6).count()

            project_list.append(project["PBTitle"])
            new_create_list.append(new_create)
            wait_proecesing_list.append(wait_process)
            finish_list.append(finish)
            # processing_list.append(processing)

        result["projects"] = project_list
        result["new_create_list"] = new_create_list
        result["wait_proecesing_list"] = wait_proecesing_list
        result["finish_list"] = finish_list
        # result["processing_list"] = processing_list
        return response.Response(data=result)


class DeamndAvgLeadTimeView(APIView):
    """
    /api/home/<USER>/demandavgleadtime/<days>
    """

    def get(self, request, *args, **kwargs):
        result = {}
        filter_days = self.kwargs.get("days", 7)
        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        all_project = models.Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")
        project_list = []
        demand_time_list = []
        develop_time_list = []
        test_time_list = []
        demand_time_sum = 0
        develop_time_sum = 0
        test_time_sum = 0
        for project in all_project:
            project_demand_list = models.Requirement.objects.filter(IsActive=1).filter(ProjectID=project["id"]).filter(
                CreationTime__range=(starttime, endtime)).values("id", "Status", "CreationTime", "StartDate", "FinishedDate", "DevelopTime",
                                                                 "TestTime", "WaitOnlineTime")
            project_demand_time = 0
            project_develop_time = 0
            project_test_time = 0
            for demand in project_demand_list:
                if demand["FinishedDate"] is None:
                    demand_time = (endtime - demand["CreationTime"]).total_seconds()
                else:
                    demand_time = (demand["FinishedDate"] - demand["CreationTime"]).total_seconds()
                project_demand_time = project_demand_time + demand_time

                if demand["TestTime"] is None and demand["DevelopTime"] is None:
                    demand_develop_time = 0
                elif demand["TestTime"] is None and demand["DevelopTime"] is not None:
                    demand_develop_time = (endtime - demand["DevelopTime"]).total_seconds()
                else:
                    demand_develop_time = (demand["TestTime"] - demand["DevelopTime"]).total_seconds()
                project_develop_time = project_develop_time + demand_develop_time

                if demand["WaitOnlineTime"] is None and demand["TestTime"] is None:
                    demand_test_time = 0
                elif demand["WaitOnlineTime"] is None and demand["TestTime"] is not None:
                    demand_test_time = (endtime - demand["DevelopTime"]).total_seconds()
                else:
                    demand_test_time = (demand["TestTime"] - demand["DevelopTime"]).total_seconds()
                project_test_time = project_test_time + demand_test_time

            project_list.append(project["PBTitle"])
            demand_time_list.append(float('%.2f' % (project_demand_time / 3600 / 24)))
            develop_time_list.append(float('%.2f' % (project_develop_time / 3600 / 24)))
            test_time_list.append(float('%.2f' % (project_test_time / 3600 / 24)))

            for time in demand_time_list:
                demand_time_sum = demand_time_sum + time

            for time in develop_time_list:
                develop_time_sum = develop_time_sum + time

            for time in test_time_list:
                test_time_sum = test_time_sum + time

            result["projects"] = project_list
            result["project_demand_time"] = demand_time_list
            result["project_develop_time"] = develop_time_list
            result["project_test_time"] = test_time_list

            result["demand_time_sum"] = float('%.2f' % demand_time_sum)
            result["develop_time_sum"] = float('%.2f' % develop_time_sum)
            result["test_time_sum"] = float('%.2f' % test_time_sum)

        return response.Response(data=result)


class ProjectCaseRepeatRate(APIView):
    """
    /api/home/<USER>/case_repeat_rate
    """

    def get(self, request, *args, **kwargs):
        result = {}
        filter_days = self.kwargs.get("days", 7)
        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        all_project = models.Project.objects.filter(id__in=project_ids).filter(IsActive=1).values("id", "PBTitle")
        project_list = []

        for project in all_project:
            project_list.append(project["PBTitle"])
            project_test_application_list = models.TestApplication.objects.filter(IsActive=1).filter(
                ProjectID=project["id"]).filter(CreationTime__range=(starttime, endtime)).values("id", "CommitTime", "TestingStartDate")
            project_all_application_testwait_sum = 0
            application_count = 0
            for test_application in project_test_application_list:
                pass
