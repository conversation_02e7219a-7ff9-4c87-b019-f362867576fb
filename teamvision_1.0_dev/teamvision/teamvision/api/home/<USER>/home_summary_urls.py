# coding=utf-8
"""
Created on 2014-1-5
@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.home.views import home_summary_view
from teamvision.api.project.views.project_requirement_view import HomeRequirementsView
from teamvision.api.project.views.project_testplan_view import ProjectTestPlanListInfoListView

api_home_summary_router = [
    re_path(r"todo/summary$", home_summary_view.TodoSummaryView.as_view()),
    re_path(r"activity/list$", home_summary_view.ActivityListView.as_view()),
    re_path(r"projecttestplans/(?P<days>.+)$", ProjectTestPlanListInfoListView.as_view()),
    re_path(r"requirements", HomeRequirementsView.as_view()),
]
