# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.home.views.home_statistics_view import ProjectTestcaseRankView, HomeProjectAutoCasePercentView
from teamvision.api.home.views.home_kanban_statistics_view import TestPlanStaticticsView, \
    ProjectTestPlanStaticticsView, IssueStatisticsView, ProjectTestPlanStatusCountView, ProjectTestCaseCountView, \
    ProjectAutoCaseCountView, HomeProjectTestCaseGrowthTrendView, HomeProjectAutoCaseGrowthTrendView, \
    HomeTestCaseSumTrendGrowth, HomeAutoCaseSumTrendGrowth, HomeTestCaseRepeatRateView, ProjectTestcaseRepeatrateView, \
    HomeTestCaseTrendGrowth, TestPlanAvgCaseStatisticsView, RequirementCaseAvgStatistics, \
    TestPlanCaseTrendStatisticsView

api_home_kanban_statistics_router = [
    re_path(r"statistics/issue", IssueStatisticsView.as_view()),
    re_path(r"statistics/project_testcase_statictics/(?P<type>.+)/$", ProjectTestCaseCountView.as_view()),
    re_path(r"statistics/project_autocase_statictics/(?P<type>.+)/$", ProjectAutoCaseCountView.as_view()),
    re_path(r"statistics/project_testcase_rank", ProjectTestcaseRankView.as_view()),
    re_path(r"statistics/project_autocase_percent?", HomeProjectAutoCasePercentView.as_view()),
    re_path(r"statistics/growthtrend/case", HomeTestCaseTrendGrowth.as_view()),
    re_path(r"statistics/growthtrend/testcase/new/(?P<days>.+)", HomeProjectTestCaseGrowthTrendView.as_view()),
    re_path(r"statistics/growthtrend/autocase/new/(?P<days>.+)", HomeProjectAutoCaseGrowthTrendView.as_view()),
    re_path(r"statistics/growthtrend/testcase/sum", HomeTestCaseSumTrendGrowth.as_view()),
    re_path(r"statistics/growthtrend/autocase/sum", HomeAutoCaseSumTrendGrowth.as_view()),
    re_path(r"statistics/testcaserepeatrate$", HomeTestCaseRepeatRateView.as_view()),
    re_path(r"statistics/project_testcase_repeatrate$", ProjectTestcaseRepeatrateView.as_view()),
    re_path(r"statistics/testplan$", TestPlanStaticticsView.as_view()),
    re_path(r"statistics/projecttestplan?", ProjectTestPlanStaticticsView.as_view()),
    re_path(r"statistics/project_testplan_status/(?P<type>.+)/$", ProjectTestPlanStatusCountView.as_view()),
    re_path(r"statistics/testplan/testcase/avg?", TestPlanAvgCaseStatisticsView.as_view()),
    re_path(r"statistics/requirement/testcase/avg?", RequirementCaseAvgStatistics.as_view()),
    re_path(r"statistics/testplan-requirement/testcase/trend", TestPlanCaseTrendStatisticsView.as_view()),
]
