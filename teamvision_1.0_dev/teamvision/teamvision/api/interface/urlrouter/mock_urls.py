# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.interface.views import mock_api_view,mock_response_view


api_mock_router =[re_path(r"mock/api/(?P<id>.+)",mock_api_view.MockApiView.as_view()),
                  re_path(r"mock/apis$",mock_api_view.MockApiListView.as_view()),
                  re_path(r"mock/api_tree$",mock_api_view.MockApiTreeView.as_view()),
                  re_path(r"mock/response/(?P<id>.+)",mock_response_view.MockResponseView.as_view()),
                  re_path(r"mock/responses$",mock_response_view.MockResponseListView.as_view()),
                  ]
