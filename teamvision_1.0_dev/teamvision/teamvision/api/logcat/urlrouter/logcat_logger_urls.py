#coding=utf-8
# coding=utf-8
"""
Created on 2014-1-5

@author: ETHAN
"""

from django.urls import re_path
from teamvision.api.logcat.views import logcat_logger_view



api_logger_router=[re_path(r"logger/(?P<id>.+)/$",logcat_logger_view.LoggerView.as_view()),
                         re_path(r"loggers/$",logcat_logger_view.LoggerListView.as_view()),
                         re_path(r"logger/$",logcat_logger_view.LoggerCreateView.as_view()),
                         ]

