# coding=utf-8
"""
@Create: 2023/3/28 17:08
@Author: zhangpeng
"""
from django.urls import re_path
from teamvision.api.message.views import message_view

urlpatterns = [
    re_path(r"config/list$", message_view.MessageConfigListView.as_view()),
    re_path(r"config/(?P<id>.+)$", message_view.MessageConfigView.as_view()),
    re_path(r"channel/list$", message_view.MessageChannelListView.as_view()),
    re_path(r"channel/(?P<id>.+)$", message_view.MessageChannelView.as_view()),
    re_path(r"channels$", message_view.MessageChannelNameListView.as_view()),
]