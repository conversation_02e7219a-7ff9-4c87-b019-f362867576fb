# coding=utf-8
"""
@Create: 2023/3/28 19:07
@Author: zhangpeng
"""
from rest_framework import serializers
from teamvision.message import models


class MessageConfigSerializer(serializers.ModelSerializer):
    created_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)
    status_t = serializers.CharField(source='get_status_display', read_only=True, required=False)

    class Meta:
        model = models.MessageConfig
        exclude = ('modified_time', 'is_delete',)
        read_only_fields = ('id',)


class MessageChannelSerializer(serializers.ModelSerializer):
    created_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)
    channel_t = serializers.CharField(source='get_channel_display', read_only=True, required=False)

    class Meta:
        model = models.MessageChannel
        exclude = ('modified_time', 'is_delete',)
        read_only_fields = ('id',)

