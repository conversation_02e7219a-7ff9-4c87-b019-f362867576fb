# coding=utf-8
"""
@Create: 2023/3/28 17:10
@Author: zhangpeng
"""
from rest_framework import response
from rest_framework.generics import ListCreateAPIView, RetrieveUpdateDestroyAPIView
from rest_framework.authentication import BasicAuthentication
from rest_framework.permissions import Allow<PERSON>ny, IsAdminUser, IsAuthenticated
from rest_framework.views import APIView

from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication
from teamvision.api.message.serializer.message_serializer import MessageConfigSerializer, MessageChannelSerializer
from teamvision.message.models import MessageConfig, MessageChannel
from django_filters import rest_framework as filters


class MessageConfigListView(ListCreateAPIView):
    """
    /api/message/config/list
    FilterSet: ['id','Partent','IsGroup']
    FilterOperation:=,__in,__gt,__contains,__icontains,Range__in,__lt,!=,__isnull
    """
    serializer_class = MessageConfigSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [IsAdminUser]
    queryset = MessageConfig.objects.all()
    filter_backends = (filters.DjangoFilterBackend,)


class MessageConfigView(RetrieveUpdateDestroyAPIView):
    """
    /api/message/config/<id>
    """
    serializer_class = MessageConfigSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [IsAuthenticated]

    def get_object(self):
        config_id = self.kwargs['id']
        return MessageConfig.objects.get(id=config_id)


class MessageChannelListView(ListCreateAPIView):
    """
    /api/message/channel/list
    FilterSet: ['id','Partent','IsGroup']
    FilterOperation:=,__in,__gt,__contains,__icontains,Range__in,__lt,!=,__isnull
    """
    serializer_class = MessageChannelSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [IsAdminUser]
    queryset = MessageChannel.objects.all()
    filter_backends = (filters.DjangoFilterBackend,)


class MessageChannelView(RetrieveUpdateDestroyAPIView):
    """
    /api/message/channel/<id>
    """
    serializer_class = MessageChannelSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [IsAdminUser]

    def get_object(self):
        channel_id = int(self.kwargs['id'])
        return MessageChannel.objects.get(id=channel_id)


class MessageChannelNameListView(ListCreateAPIView):
    """
    /api/message/channels
    mei xie wan
    """
    serializer_class = MessageChannelSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [IsAdminUser]

    def get(self, request):
        result = []
        for item in MessageChannel.channel_choices:
            tmp = {}
            tmp['key'] = item[0]
            tmp['value'] = item[1]
            result.append(tmp)
        return response.Response(result)
