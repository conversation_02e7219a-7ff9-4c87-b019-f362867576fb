# coding=utf-8
"""
Created on 2016-12-5

@author: z<PERSON><PERSON><PERSON>
"""

from teamvision.project import models
from url_filter.filtersets.django import ModelFilterSet
from django_filters import rest_framework as filters
from teamvision.project.models import ProjectIssue, ProjectTestReport
from django_filters import BaseInFilter, NumberFilter
import django_filters


class NumberInFilter(BaseInFilter, NumberFilter):
    pass


class IssueFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectIssue
        fields = ['Project', 'Version', 'Title', 'Desc', 'ProjectPhase', 'Priority', 'Status', 'Module', 'Processor',
                  'IssueCategory', 'Solution', 'Severity', 'Creator', 'CreationTime', 'ClosedTime', 'ResolvedTime',
                  'Team']


class IssueFilter(filters.FilterSet):
    Project__in = NumberInFilter(field_name='Project', lookup_expr='in')
    Status__in = NumberInFilter(field_name='Status', lookup_expr='in')
    Processor__in = NumberInFilter(field_name='Processor', lookup_expr='in')
    Priority__in = NumberInFilter(field_name='Priority', lookup_expr='in')
    Version__in = NumberInFilter(field_name='Version', lookup_expr='in')
    Creator__in = NumberInFilter(field_name='Creator', lookup_expr='in')
    Solution__in = NumberInFilter(field_name='Solution', lookup_expr='in')
    ProjectPhase__in = NumberInFilter(field_name='ProjectPhase', lookup_expr='in')
    search = filters.CharFilter(method='search_title_or_desc')
    CreationTime = filters.DateFromToRangeFilter(field_name='CreationTime', )
    Severity__in = NumberInFilter(field_name='Severity', lookup_expr='in')

    # CreationTime__range = filters.DateTimeFromToRangeFilter(field_name="CreationTime")

    def search_title_or_desc(self, queryset, name, value):
        if value:
            return queryset.filter(Title__icontains=value) | queryset.filter(Desc__icontains=value)
        return queryset

    class Meta:
        model = ProjectIssue
        fields = ['Project', 'Version', 'Title', 'Desc', 'ProjectPhase', 'Priority', 'Status', 'Processor', 'IssueCategory',
                  'Solution', 'Severity', 'Creator', 'CreationTime', 'ClosedTime', 'ResolvedTime', 'Team']


class TestReportFilter(filters.FilterSet):
    Project__in = NumberInFilter(field_name='Project', lookup_expr='in')
    Status__in = NumberInFilter(field_name='Status', lookup_expr='in')
    ReportType__in = NumberInFilter(field_name='ReportType', lookup_expr='in')
    Version__in = NumberInFilter(field_name='Version', lookup_expr='in')
    Creator__in = NumberInFilter(field_name='Creator', lookup_expr='in')
    Title__icontains = django_filters.CharFilter(field_name='Title', lookup_expr='icontains')
    CreationTime = filters.DateFromToRangeFilter(field_name='CreationTime', )

    class Meta:
        model = ProjectTestReport
        fields = ['Project', 'Version', 'Title', 'Status', 'Creator', 'CreationTime', 'ReportType']
