# coding=utf-8
"""
"""

from teamvision.project import models
from url_filter.filtersets.django import ModelFilterSet
from django_filters import rest_framework as filters
from django_filters import BaseInFilter, NumberFilter, OrderingFilter


class NumberInFilter(BaseInFilter, NumberFilter):
    pass


class CaseReviewFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectCaseReview
        fields = ['id', 'IsActive', 'Title', 'Desc', 'Project', 'Creator', 'Status', 'Deadline', 'CaseCount',
                  'CreationTime']


class CaseReviewFilter(filters.FilterSet):
    Title__icontains = filters.CharFilter(field_name='Title', lookup_expr='icontains')
    sort = OrderingFilter(fields=('id',))

    class Meta:
        model = models.ProjectCaseReview
        fields = ['id', 'IsActive', 'Title', 'Desc', 'Project', 'Creator', 'Status', 'Deadline', 'CaseCount',
                  'CreationTime']
