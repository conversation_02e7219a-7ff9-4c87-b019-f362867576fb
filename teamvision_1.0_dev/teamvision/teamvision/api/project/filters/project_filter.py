# coding=utf-8
"""
Created on 2016-12-5

@author: z<PERSON><PERSON><PERSON>
"""
from teamvision.api.project.filters.filter import NumberInFilter
from teamvision.project import models
from url_filter.filtersets.django import ModelFilterSet
from django_filters import rest_framework as filters


class ProjectTaskFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.Task
        fields = ['ProjectID', 'Status', 'Title', 'Parent', 'Version', 'TaskType']


class ProjectTaskOwnerFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectTaskOwner
        fields = ['Task', 'Version']


class ProjectTaskDependencyFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectTaskOwner
        fields = ['Task', 'Version']


class ProjectDocumentFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectDocument
        fields = ['id', 'ProjectID', 'Parent', 'ReadOnly', 'Type']


class ProjectFortestingFilterSet(filters.FilterSet):
    Status__in = NumberInFilter(field_name='Status', lookup_expr='in')

    class Meta:
        model = models.TestApplication
        fields = ['id', 'ProjectID', 'VersionID', 'Status', 'CommitTime', 'TestingFinishedDate', 'BVTPassed', 'Testers']


class ProjectRequirementFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.Requirement
        fields = ['id', 'ProjectID', 'Version', 'Status', 'FortestingID']


class ProjectTagFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.Tag
        fields = ['TagType']


class ProjectTaskStatusFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectTaskStatus
        fields = ['id', 'Type']


class ProjectTestApplicationNumberStatisticsFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectTestApplicationNumberStatistics
        fields = ['id', 'ProjectID', 'VersionID', 'TestApplicationNumber', 'StatisticsMonth', 'StatisticsDate']


class ProjectTestApplicationTimeStatisticsFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectTestApplicationTimeStatistics
        fields = ['id', 'ProjectID', 'VersionID', 'TestApplicationID', 'TestDuration', 'StatisticsMonth',
                  'StatisticsDate']
