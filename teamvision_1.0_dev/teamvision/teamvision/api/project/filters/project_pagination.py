# coding=utf-8
"""
Created on 2016-10-25
@author: <PERSON><PERSON><PERSON><PERSON>
"""
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class ProjectPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 10000


class ProjectIssuePagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 1000
    ordering = ''

    def get_page_total(self):
        if self.page.paginator.count > self.page_size:
            return self.page.paginator.count // self.page_size
        else:
            return 1

    def get_paginated_response(self, data):
        return Response({
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'count': self.page.paginator.count,
            'page_size': self.page_size,
            'page_total': self.get_page_total(),
            'results': data
        })


class RequirementPagination(PageNumberPagination):
    page_size = 10
    page_query_param = 'page'
    page_size_query_param = 'page_size'
    max_page_size = 100


class ProjectCaseReviewPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 10000


class ProjectTestReportPagination(PageNumberPagination):
    page_size_query_param = 'page_size'
    page_size = 10
    max_page_size = 10000
