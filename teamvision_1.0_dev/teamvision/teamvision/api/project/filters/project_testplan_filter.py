# coding=utf-8
"""
Created on 2016-12-5

@author: z<PERSON><PERSON><PERSON>
"""

from teamvision.project import models
from url_filter.filtersets.django import ModelFilterSet
from django_filters import rest_framework as filters


class TestPlanCaseFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectTestPlanCase
        fields = ['id', 'TestPlan', 'TestResult', 'Owner']


class TestPlanFilterSet(ModelFilterSet):
    class Meta(object):
        model = models.ProjectTestPlan
        fields = ['id', 'Version']


class TestPlanFilter(filters.FilterSet):
    class Meta:
        model = models.ProjectTestPlan
        fields = ['Status']
