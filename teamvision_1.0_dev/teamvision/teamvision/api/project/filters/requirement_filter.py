# encoding=utf-8
from django_filters import rest_framework as filters
from teamvision.project.models import Requirement

from django_filters import BaseInFilter, NumberFilter


class NumberInFilter(BaseInFilter, NumberFilter):
    pass


class RequirementFilter(filters.FilterSet):
    id = NumberInFilter(field_name='id', lookup_expr='in')
    Status = NumberInFilter(field_name='Status', lookup_expr='in')
    Priority = NumberInFilter(field_name='Priority', lookup_expr='in')
    Risk = NumberInFilter(field_name='RiskLevel', lookup_expr='in')
    Version = NumberInFilter(field_name='Version', lookup_expr='in')
    Module = NumberInFilter(field_name='Module', lookup_expr='in')
    Title__icontains = filters.CharFilter(field_name='Title', lookup_expr='icontains')

    class Meta:
        model = Requirement
        fields = ['id', 'Status', 'Priority', 'RiskLevel', 'Version', 'Module', 'Title', ]
