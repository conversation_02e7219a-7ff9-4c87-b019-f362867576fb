# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from teamvision.project import models
from teamvision.project import mongo_models
from teamvision.settings import WEB_HOST, TIME_ZONE
from teamvision.home.models import FileInfo
from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.api.common.serializer.file_serializer import FileSerializer
from teamvision.api.project.serializer.project_mindmap_serializer import ProjectMindFileSerializer
from teamvision.home.models import Team
from business.auth_user.user_service import UserService
from business.project.task_service import TaskService
from business.ucenter.account_service import AccountService
from json.decoder import JSONDecoder


class SpaceUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProductSpaceUser
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id',)


class ProductSpaceSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["SpaceAdmin"] = self.get_SpaceAdmin(obj)
        return result

    def get_SpaceAdmin(self, obj):
        result = "--"
        if obj.SpaceAdmin is not None:
            user = UserService.get_user(obj.SpaceAdmin)
            if user is not None:
                result = (user.last_name + user.first_name)[-2:]
        return result

    class Meta:
        model = models.ProductSpace
        exclude = ('IsActive', 'CreationTime')
        read_only_fields = ('id',)


class UserProductSpaceSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProductSpace
        exclude = ('IsActive', 'CreationTime', 'LabelColor', 'Creator', 'Key', 'SpaceAdmin')
        read_only_fields = ('id',)
