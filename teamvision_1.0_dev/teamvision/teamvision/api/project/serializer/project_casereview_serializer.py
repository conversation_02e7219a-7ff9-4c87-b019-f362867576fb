# coding=utf-8
"""
"""
from rest_framework import serializers
from teamvision.api.project.serializer.project_requirement_serializer import ProjectRequirementListSerializer
from teamvision.project import models
from business.auth_user.user_service import UserService
from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.api.project.serializer.project_testcase_serializer import ProjectTestCaseSerializer
from teamvision.project.models import ProjectCaseReviewReviewer


class ProjectCaseReviewSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    Deadline = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()
        result['creator_name'] = self.creator_name(obj)
        result['status_name'] = self.status_name(obj)
        result['requirements'] = self.get_requirements(obj)
        result['reviewer'] = self.get_reviewer_name(obj)
        return result

    def creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def status_name(self, obj):
        result = ""
        if obj.Status == 1:
            return "进行中"
        if obj.Status == 2:
            return "已完成"
        if obj.Status == 3:
            return "已归档"
        return result

    def get_requirements(self, obj):
        require_ids = models.ProjectCaseReviewRequirement.objects.filter(case_review_id=obj.id).values_list(
            'requirement_id', flat=True)
        requirement_ins = models.Requirement.objects.filter(id__in=require_ids)
        requirements = ProjectRequirementListSerializer(instance=requirement_ins, many=True)
        return requirements.data

    def get_reviewer_name(self, obj):
        u_ids = ProjectCaseReviewReviewer.objects.filter(CaseReview=obj.id).values_list('Reviewer', flat=True)
        user_name_list = ""
        for u in u_ids:
            name = UserService.get_name_by_id(u)
            if user_name_list == "":
                user_name_list = name
            else:
                user_name_list = name + ',' + user_name_list
        return user_name_list

    class Meta:
        model = models.ProjectCaseReview
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectCaseReviewListSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    Deadline = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()
        result['creator_name'] = self.creator_name(obj)
        result['reviewer_name'] = self.get_reviewer_name(obj)
        result['status_name'] = self.status_name(obj)
        return result

    def creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def get_reviewer_name(self, obj):
        u_ids = ProjectCaseReviewReviewer.objects.filter(CaseReview=obj.id).values_list('Reviewer', flat=True)
        user_name_list = ""
        for u in u_ids:
            name = UserService.get_name_by_id(u)
            if user_name_list == "":
                user_name_list = name
            else:
                user_name_list = name + ',' + user_name_list
        return user_name_list

    def status_name(self, obj):
        result = ""
        if obj.Status == 1:
            return "进行中"
        if obj.Status == 2:
            return "已完成"
        if obj.Status == 3:
            return "已归档"
        return result

    class Meta:
        model = models.ProjectCaseReview
        exclude = ('IsActive', 'Desc', 'Project', 'Creator', 'CaseCount',)
        read_only_fields = ('id', 'CreationTime')


class ProjectCaseReviewCaseTreeSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    view_data = serializers.SerializerMethodField()
    reviewcase_list = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    TestCase = serializers.SerializerMethodField()

    def get_TestCase(self, obj):
        return obj.id

    def get_view_data(self, obj):
        result = dict()
        return result

    def get_children(self, obj):
        result = list()
        self.reviewcaseidlist = []
        child_nodes = models.ProjectTestCase.objects.get_children(obj.id).filter(IsActive=1).order_by("IsGroup")
        if len(obj.casereview_case_ids) > 0:
            child_nodes = child_nodes.filter(id__in=obj.casereview_case_ids)
        if len(child_nodes) > 0:
            for child_node in child_nodes:
                self.get_child_data(child_node, result, obj.casereview_case_ids, obj.casereview_id)
        return result

    def get_child_data(self, parent_node, child_data, casereview_case_ids, casereview_id):
        if parent_node:
            temp_result_list = models.ProjectCaseReviewTestcase.objects.all().filter(CaseReview=casereview_id).filter(
                TestCase=parent_node.id)
            if len(temp_result_list) > 0:
                temp_result = temp_result_list[0]
                temp_serializer = ProjectCaseReviewCaseResultSerializer(instance=temp_result)
            else:
                if parent_node.IsGroup:
                    temp_serializer = ProjectTestCaseSerializer(instance=parent_node)
                else:
                    return
            temp_data = temp_serializer.data
            if parent_node.IsGroup == False:
                child_data.append(temp_data)
                self.reviewcaseidlist.append(temp_data["id"])
                return
            temp_data["children"] = list()
            child_nodes = models.ProjectTestCase.objects.get_children(parent_node.id).filter(IsActive=1).order_by(
                "IsGroup")
            if len(casereview_case_ids) > 0:
                child_nodes = child_nodes.filter(id__in=casereview_case_ids)
            if len(child_nodes) > 0:
                for child_node in child_nodes:
                    self.get_child_data(child_node, temp_data["children"], casereview_case_ids, casereview_id)
                child_data.append(temp_data)
            else:
                child_data.append(temp_data)
                return
        else:
            return

    def get_reviewcase_list(self, obj):
        return self.reviewcaseidlist

    class Meta:
        model = models.ProjectTestCase
        exclude = ('IsActive', 'ExpectResult', 'Precondition', 'accessTest')
        read_only_fields = ('id',)


class ProjectCaseReviewCaseResultSerializer(serializers.ModelSerializer):
    Title = serializers.SerializerMethodField(method_name="get_case_property")
    Priority = serializers.SerializerMethodField(method_name="priority")

    def get_case_property(self, obj):
        result = ""
        test_case = models.ProjectTestCase.objects.get(obj.TestCase)
        if test_case is not None:
            result = test_case.__dict__.get("Title")
        return result

    def priority(self, obj):
        result = "P2"
        test_case = models.ProjectTestCase.objects.get(obj.TestCase)
        if test_case is not None:
            tags = models.Tag.objects.filter(TagType=6).filter(TagValue=test_case.Priority)
            if len(tags) > 0:
                result = tags[0].TagName
        return result

    class Meta:
        model = models.ProjectCaseReviewTestcase
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseKityMindSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    Deadline = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    data = serializers.SerializerMethodField(method_name="get_data")

    def get_data(self, obj):
        data = list()
        result = dict()
        temp_serializer = ProjectCaseReviewMindTitleSerializer(instance=obj)
        result["data"] = temp_serializer.data
        result["children"] = self.get_children(obj)
        data.append(result)
        return data

    def get_children(self, obj):
        result = list()
        casereview_cases = models.ProjectCaseReviewTestcase.objects.get_case_review_cases(obj.id)
        casereview_cases_ids = [case.TestCase for case in casereview_cases]
        case_group = models.ProjectTestCase.objects.filter(Project=obj.Project).filter(Parent=0).filter(
            id__in=casereview_cases_ids)

        for item in case_group:
            child_data = dict()
            child_data["data"] = dict()
            child_data["children"] = list()
            root_node = models.ProjectTestCase.objects.get(item.id)
            self.get_child_data(root_node, child_data, casereview_cases_ids)
            result.append(child_data)
        return result

    def get_child_data(self, parent_node, child_data, case_review_cases):
        if parent_node.id in case_review_cases:
            if parent_node:
                temp_serializer = ProjectTestCaseKityMindTopicSerializer(instance=parent_node)
                temp_data = temp_serializer.data
                child_data["data"] = temp_data
                if parent_node.IsGroup:
                    child_nodes = models.ProjectTestCase.objects.get_children(parent_node.id).filter(
                        id__in=case_review_cases).filter(IsActive=1).order_by("IsGroup")
                    for child_node in child_nodes:
                        temp_child_data = dict()
                        temp_child_data["data"] = dict()
                        temp_child_data["children"] = list()
                        self.get_child_data(child_node, temp_child_data, case_review_cases)
                        child_data["children"].append(temp_child_data)
                else:
                    return child_data
            else:
                return child_data

    class Meta:
        model = models.ProjectCaseReview
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectTestCaseKityMindTopicSerializer(serializers.ModelSerializer):
    text = serializers.CharField(source='Title')
    note = serializers.CharField(source='Desc')
    priority = serializers.CharField(source='Priority')

    class Meta:
        model = models.ProjectTestCase
        exclude = ('CreationTime', 'IsActive', 'UpdateTime', 'Module', 'RunTimes', 'automatedCompletion', 'automate')
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectCaseReviewMindTitleSerializer(serializers.ModelSerializer):
    text = serializers.CharField(source='Title')

    class Meta:
        model = models.ProjectCaseReview
        exclude = ('CreationTime', 'IsActive', 'Deadline')
        extra_kwargs = {'IsActive': {'required': False}}
