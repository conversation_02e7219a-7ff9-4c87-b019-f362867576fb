# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from teamvision.project import models


class VM_ProjectIssueSerializer(serializers.Serializer):
    Project = serializers.CharField()
    Version = serializers.CharField()
    ModuleName = serializers.CharField()
    id = serializers.IntegerField()
    ProjectID = serializers.IntegerField()
    CreateTime = serializers.DateTimeField()
    Title = serializers.CharField()
    TeamName = serializers.CharField()
    ServerityLabel = serializers.CharField()
    ServerityLabelStyle = serializers.CharField()
    ServerityName = serializers.CharField()
    PriorityName = serializers.CharField()
    CategoryName = serializers.CharField()
    StatusLabel = serializers.CharField()
    StatusLabelStyle = serializers.CharField()
    StatusName = serializers.CharField()
    SolutionLabel = serializers.CharField()
    SolutionLabelStyle = serializers.CharField()
    SolutionName = serializers.CharField()
    Creator = serializers.CharField()
    Processor = serializers.CharField()

    class QueryInfo:
        model = models.ProjectIssue
        serializer_sql = "select  t2.PBTitle as Project,t2.id as ProjectID,t3.VVersion as Version,t5.`Name` as ModuleName,t1.id, " \
                         " DATE_FORMAT(date_add(t1.CreationTime, interval 8 hour),'%%Y-%%m-%%d %%T') as CreateTime, " \
                         " t1.Title,t4.`Name` as TeamName,t6.Label as ServerityLabel,t6.LabelStyle as ServerityLabelStyle, " \
                         " t6.`Name` as ServerityName ,t7.`Name` as PriorityName,t9.`Name` as CategoryName, " \
                         " t10.`Name` as PhraseName,t8.Label as StatusLabel,t8.LabelStyle as StatusLabelStyle,t8.`Name` as StatusName ," \
                         " t11.Label as SolutionLabel,t11.LabelStyle as SolutionLabelStyle,t11.`Name` as SolutionName," \
                         " CONCAT(t12.last_name,t12.first_name) as Creator,CONCAT(t13.last_name,t13.first_name) as Processor from project_issue t1" \
                         " inner join project t2 on t1.Project =t2.id " \
                         " inner join project_version t3 on t1.Version =t3.id " \
                         " inner join team_role t4 on t1.Team = t4.id " \
                         " inner join project_module t5 on t1.Module = t5.id " \
                         " inner join project_issue_severity t6 on t1.Severity = t6.`Value` " \
                         " inner join project_issue_priority t7 on t1.Priority = t7.`Value` " \
                         " inner join project_issue_status t8 on t1.`Status` = t8.`Value` " \
                         " inner join project_issue_category t9 on t1.IssueCategory = t9.`Value` " \
                         " inner join project_phase t10 on t1.ProjectPhase = t10.`Value` " \
                         " inner join project_issue_resolved_result t11 on t1.Solution = t11.`Value` " \
                         " inner join auth_user t12 on t1.Creator = t12.id " \
                         " inner join auth_user t13 on t1.Processor = t13.id " \
                         " where t1.Project in %(Project)s "
        required_parameters = ["projectids"]
        optional_parameters = ["versions", "status", "serverity", "priority", "team", "processor", "create_time",
                               "project_phrase", "category", "solution"]

        @staticmethod
        def get_sql(optional_parameters):
            result = VM_ProjectIssueSerializer.QueryInfo.serializer_sql
            optional_filters = VM_ProjectIssueSerializer.QueryInfo.get_filters()
            for parameter in optional_parameters.keys():
                temp = optional_filters.get(parameter)
                if temp is not None:
                    result = result + temp
            return result

        @staticmethod
        def get_filters():
            optional_filters = dict()
            optional_filters.keys()
            optional_filters["Version"] = "and  t1.Version in %(Version)s"
            optional_filters["Module"] = "and  t1.Module in %(Module)s"
            optional_filters["Team"] = "and  t1.Team in %s"
            optional_filters["Severity"] = "and  t1.Severity in %(Severity)s"
            optional_filters["Priority"] = "and  t1.Priority in %(Priority)s"
            optional_filters["Status"] = "and  t1.Status in %(Status)s"
            optional_filters["IssueCategory"] = "and  t1.IssueCategory in %(IssueCategory)s"
            optional_filters["ProjectPhase"] = "and  t1.ProjectPhase in %(ProjectPhase)s"
            optional_filters["Solution"] = "and  t1.Solution in %(Solution)s"
            optional_filters["Creator"] = "and  t1.Creator in %(Creator)s"
            optional_filters["Processor"] = "and  t1.Processor in %(Processor)s"
            optional_filters["Title"] = "and  t1.Title like %(Title)s"
            optional_filters["CreationTime"] = "and  t1.CreationTime > %(CreationTime)s"
            for item in optional_filters.values():
                item = " " + item + " "
            return optional_filters
