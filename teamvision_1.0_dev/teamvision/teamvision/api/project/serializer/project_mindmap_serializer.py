# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from teamvision.project import models
from teamvision.ci.models import CaseTag
from business.auth_user.user_service import UserService
from business.project.project_mindmap_service import MindmapService
from business.project.topic_tree_node import TopicTreeNode


class ProjectMindFileSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = {}
        result["PointCounts"] = self.get_CasePointCounts(obj)
        result["AutoPointCounts"] = self.get_AutoPointCounts(obj)
        result["FinishedPointCounts"] = self.get_FinishedPointCounts(obj)
        result["Owner"] = self.get_Owner(obj)
        result["UpdateTime"] = self.get_UpdateTime(obj)
        result["VersionName"] = self.get_VersionName(obj)
        return result

    def get_CasePointCounts(self, obj):
        return obj.TestPointCount

    def get_AutoPointCounts(self, obj):
        all_topics = models.ProjectXmindTopic.objects.all().filter(MindFileID=obj.id)
        original_ids = [topic.OriginalID for topic in all_topics]
        tags = models.ProjectXmindTopicTagMap.objects.filter(OriginalID__in=original_ids).filter(TagID=10)
        temp_originalids = list()
        for tag in tags:
            if tag.OriginalID not in temp_originalids:
                temp_originalids.append(tag.OriginalID)
        return len(temp_originalids)

    def get_FinishedPointCounts(self, obj):
        all_topics = models.ProjectXmindTopic.objects.all().filter(MindFileID=obj.id)
        original_ids = [topic.id for topic in all_topics]
        pass_tags = models.ProjectXmindTopicTagMap.objects.filter(TopicID__in=original_ids).filter(TagID=11)
        fail_tags = models.ProjectXmindTopicTagMap.objects.filter(TopicID__in=original_ids).filter(TagID=12)
        tbc_tags = models.ProjectXmindTopicTagMap.objects.filter(TopicID__in=original_ids).filter(TagID=13)
        return [len(pass_tags), len(fail_tags), len(tbc_tags)]

    def get_Owner(self, obj):
        result = "--"
        owner = UserService.get_user(obj.Owner)
        if owner is not None:
            result = owner.last_name + owner.first_name
        return result[-2:]

    def get_VersionName(self, obj):
        result = "--"
        version = models.Version.objects.get(obj.VersionID)
        if version is not None:
            result = version.VVersion
        return result

    def get_UpdateTime(self, obj):
        result = "--"
        all_topics = models.ProjectXmindTopic.objects.all().filter(MindFileID=obj.id).order_by('-UpdateTime')
        if len(all_topics) > 0:
            lastest_time = all_topics[0].UpdateTime
            if lastest_time is not None:
                result = str(lastest_time.month) + "月" + str(lastest_time.day) + "日 " + str(
                    lastest_time.hour) + ":" + str(lastest_time.minute) + "分"
        return result

    def get_CasePoints(self, obj):
        result = []
        root_node = TopicTreeNode()
        root_topic = models.ProjectXmindTopic.objects.get(obj.RootTopic)
        MindmapService.get_topic_tree(0, root_topic, root_node)
        MindmapService.get_leaf_node(root_node, result)
        return result

    class Meta:
        model = models.ProjectXmindFile
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectMindTopicSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectXmindTopic
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectMindTopicTagMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectXmindTopicTagMap
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectKityMindTopicSerializer(serializers.ModelSerializer):
    text = serializers.CharField(source='Text')
    note = serializers.CharField(source='Note')
    priority = serializers.CharField(source='Priority')
    progress = serializers.CharField(source='Progress')
    image = serializers.CharField(source='Image')
    link = serializers.CharField(source='Link')
    resource = serializers.SerializerMethodField()
    background = serializers.CharField(source="BackgroundColor")
    fontfamily = serializers.CharField(source="FontFamily")
    fontsize = serializers.CharField(source="FontSize")
    color = serializers.CharField(source="FontColor")

    def get_resource(self, obj):
        result = list()
        topic_tag_maps = models.ProjectXmindTopicTagMap.objects.get_topic_tags(obj.id)
        for topic_tag in topic_tag_maps:
            tag = CaseTag.objects.get(topic_tag.TagID)
            if tag is not None:
                result.append(tag.TagName)
        return result

    class Meta:
        model = models.ProjectXmindTopic
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectKityMindSerializer(serializers.ModelSerializer):
    data = serializers.SerializerMethodField()

    def get_data(self, obj):
        result = list()
        child_data = dict()
        child_data["data"] = dict()
        child_data["children"] = list()
        root_node = models.ProjectXmindTopic.objects.get(obj.RootTopic)
        self.get_child_data(root_node, child_data)
        result.append(child_data)
        return result

    def get_child_data(self, parent_node, child_data):
        if parent_node:
            temp_serializer = ProjectKityMindTopicSerializer(instance=parent_node)
            temp_data = temp_serializer.data
            temp_data["font-family"] = temp_data.pop("fontfamily")
            temp_data["font-size"] = int(temp_data.pop("fontsize"))
            child_data["data"] = temp_data
            child_nodes = models.ProjectXmindTopic.objects.get_children(parent_node.id, parent_node.MindFileID).filter(
                IsActive=1)
            if len(child_nodes) > 0:
                for child_node in child_nodes:
                    temp_child_data = dict()
                    temp_child_data["data"] = dict()
                    temp_child_data["children"] = list()
                    self.get_child_data(child_node, temp_child_data)
                    child_data["children"].append(temp_child_data)
            else:
                return child_data
        else:
            return child_data

    class Meta:
        model = models.ProjectXmindFile
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectXMindTopicSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectXmindTopic
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'IsActive': {'required': False}}
