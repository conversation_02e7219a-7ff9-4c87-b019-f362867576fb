# coding=utf-8
"""
Created on 2016-10-12

@author: zhangtiande
"""

from rest_framework import serializers

from business.project.project_task_service import ProjectTaskStatusService
from teamvision.project import models
from business.project.requirement_service import RequirementService
from business.auth_user.user_service import UserService


class ProjectRequirementSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    StartDate = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["Version"] = self.get_version(obj)
        result["Owner"] = self.get_owner(obj)
        result["ExecutiveOwner"] = self.get_executive_owner(obj)
        result["Priority"] = self.get_priority(obj)
        result["Status"] = self.get_status(obj)
        result["ReleaseDate"] = self.get_release_date(obj)
        result["Risk"] = self.get_risk(obj)
        return result

    def get_version(self, obj):
        result = "待排期"
        if obj.Version is not None:
            version = models.Version.objects.get(obj.Version)
            if version is not None:
                result = version.VVersion
        return result

    def get_release_date(self, obj):
        result = "--"
        if obj.ReleaseDate is not None:
            result = obj.ReleaseDate
        return result

    def get_owner(self, obj):
        return UserService.get_name_by_id(obj.Owner)

    def get_executive_owner(self, obj):
        return UserService.get_name_by_id(obj.ExecutiveOwner)

    def get_priority(self, obj):
        result = "P2"
        if obj.Priority is not None:
            priority = ProjectTaskStatusService.get_status_name(4, obj.Priority)
            result = priority
        return result

    def get_status(self, obj):
        result = "新建"
        if obj.Status is not None:
            status = ProjectTaskStatusService.get_status_name(3, obj.Status)
            result = status
        return result

    def get_risk(self, obj):
        result = ""
        if obj.RiskLevel is not None:
            risk = ProjectTaskStatusService.get_status_name(5, obj.RiskLevel)
            result = risk
        return result

    class Meta:
        model = models.Requirement
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectRequirementListSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["Owner"] = self.get_owner(obj)
        result["Status"] = self.get_status(obj)
        result["Priority"] = self.get_priority(obj)
        return result

    def get_owner(self, obj):
        return UserService.get_name_by_id(obj.Owner)

    def get_status(self, obj):
        result = "新建"
        if obj.Status is not None:
            status = ProjectTaskStatusService.get_status_name(3, obj.Status)
            result = status
        return result

    def get_priority(self, obj):
        priority = ProjectTaskStatusService.get_status_name(4, obj.Priority)
        return priority

    class Meta:
        model = models.Requirement
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class RequirementProjectSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["Modules"] = self.get_Modules(obj)
        result["RequirementCounts"] = self.get_ProjectReqCounts(obj)
        return result

    def get_Modules(self, obj):
        result = list()
        project_modules = models.ProjectModule.objects.project_modules(obj.id)
        for module in project_modules:
            module.version_filter = obj.version_filter
            module.status_filter = obj.status_filter
            module.priority_filter = obj.priority_filter
            module.risk_filter = obj.risk_filter
            temp_serializer = RequirementModuleSerializer(instance=module)
            result.append(temp_serializer.data)

        return result

    def get_ProjectReqCounts(self, obj):
        project_requirements = RequirementService.project_requirements(obj.version_filter, obj.status_filter, obj.id)
        return len(project_requirements)

    class Meta:
        model = models.Project
        exclude = ('IsActive', 'PBDescription', 'PBCreator', 'PBHttpUrl', 'PBVisiableLevel', 'PBPlatform', 'PBLead', 'PBKey')
        read_only_fields = ('id',)


class RequirementModuleSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["Progress"] = self.get_ModuleProgress(obj)
        result["Requirements"] = self.get_Requirements(obj)
        return result

    def get_ModuleProgress(self, obj):
        module_requirements = RequirementService.module_requirements(obj.version_filter, obj.status_filter, obj.id)
        finshed_requirements = RequirementService.module_requirements(obj.version_filter, 6, obj.id)
        if len(module_requirements) == 0:
            return 0
        else:
            return len(finshed_requirements) / len(module_requirements)

    def get_Requirements(self, obj):
        result = list()
        # no_version_requirements = RequirementService.module_requirements(0, obj.status_filter, obj.id).order_by('Status')
        module_requirements = RequirementService.module_requirements(obj.version_filter, obj.status_filter,
                                                                     obj.id).order_by('Status')
        if str(obj.priority_filter) != "0":
            # no_version_requirements = no_version_requirements.filter(Priority=obj.priority_filter)
            module_requirements = module_requirements.filter(Priority=obj.priority_filter)
        if str(obj.risk_filter) != "0":
            # no_version_requirements = no_version_requirements.filter(RiskLevel=obj.risk_filter)
            module_requirements = module_requirements.filter(RiskLevel=obj.risk_filter)
        # no_version_reqids = [req.id for req in no_version_requirements]
        # for requirement in no_version_requirements:
        #     temp_serializer = ProjectRequirementSerializer(instance=requirement)
        #     result.append(temp_serializer.data)
        for requirement in module_requirements:
            temp_serializer = ProjectRequirementSerializer(instance=requirement)
            result.append(temp_serializer.data)
            # if requirement.id not in no_version_reqids:
        return result

    class Meta:
        model = models.ProjectModule
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class RequirementTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.RequirementTaskMap
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class HomeProjectRequirementsSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["Requirements"] = self.get_Requirements(obj)
        result["RequirementCounts"] = self.get_ProjectReqCounts(obj)
        return result

    def get_Requirements(self, obj):
        result = list()
        module_requirements = RequirementService.module_requirements(obj.version_filter, obj.status_filter,
                                                                     obj.id).order_by('Status')
        if str(obj.priority_filter) != "0":
            module_requirements = module_requirements.filter(Priority=obj.priority_filter)
        if str(obj.risk_filter) != "0":
            module_requirements = module_requirements.filter(RiskLevel=obj.risk_filter)
        for requirement in module_requirements:
            temp_serializer = ProjectRequirementSerializer(instance=requirement)
            result.append(temp_serializer.data)
        return result

    def get_ProjectReqCounts(self, obj):
        project_requirements = RequirementService.project_requirements(obj.version_filter, obj.status_filter, obj.id)
        return len(project_requirements)

    class Meta:
        model = models.Project
        exclude = ('IsActive', 'PBDescription', 'PBCreator', 'PBHttpUrl', 'PBVisiableLevel', 'PBPlatform', 'PBLead', 'PBKey')
        read_only_fields = ('id',)
