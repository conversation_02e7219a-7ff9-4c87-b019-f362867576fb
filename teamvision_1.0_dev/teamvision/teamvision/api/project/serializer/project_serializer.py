# coding=utf-8
"""
Created on 2016-10-12

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from django.contrib.auth.models import User

from business.project.project_task_service import ProjectTaskStatusService
from business.project.task_service import TaskService
from teamvision.api.project.serializer.project_requirement_serializer import ProjectRequirementListSerializer
from teamvision.project import models
from teamvision.project import mongo_models
from teamvision.settings import WEB_HOST, TIME_ZONE
from teamvision.home.models import FileInfo
from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.api.common.serializer.file_serializer import FileSerializer
from teamvision.api.project.serializer.project_mindmap_serializer import ProjectMindFileSerializer
from teamvision.home.models import Team
from gatesidelib.common.simplelogger import SimpleLogger
from business.auth_user.user_service import UserService
from business.project.module_service import ModuleService
from business.project.version_service import VersionService
from business.project.project_service import ProjectService
from business.ucenter.account_service import AccountService
import datetime, re, time, pytz, random


class ProjectMemberSerializer(serializers.ModelSerializer):
    email = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    role_name = serializers.SerializerMethodField()
    role_color = serializers.SerializerMethodField()

    def get_name(self, obj):
        result = "--"
        user = UserService.get_user(int(obj.PMMember))
        if user:
            result = user.last_name + user.first_name
        return result

    def get_role_name(self, obj):
        result = "User"
        role = models.ProjectRole.objects.get(int(obj.PMRoleID))
        if role:
            result = role.PRName
        return result

    def get_role_color(self, obj):
        result = "green"
        role = models.ProjectRole.objects.get(int(obj.PMRoleID))
        if role:
            result = role.PRColor
        return result

    def get_color(self, obj):
        colorArr = ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F']
        color = ""
        for i in range(6):
            color += colorArr[random.randint(0, 14)]
        return "#" + color

    def get_email(self, obj):
        result = "--"
        user = UserService.get_user(int(obj.PMMember))
        if user:
            result = user.email
        return result

    def get_avatar(self, obj):
        result = "/static/global/images/fruit-avatar/Fruit-1.png"
        try:
            user = UserService.get_user(int(obj.PMMember))
            if user is not None:
                result = AccountService.get_avatar_url(user)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    class Meta:
        model = models.ProjectMember
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id', 'email', 'name')


class ProjectRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectRole
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Tag
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectVersionSerializer(serializers.ModelSerializer):
    VersionLabel = serializers.SerializerMethodField()

    def get_VersionLabel(self, obj):
        result = obj.VVersion
        if obj.VStartDate and obj.VReleaseDate:
            result = result + " ( " + str(obj.VStartDate)[5:] + ':' + str(obj.VReleaseDate)[5:] + " )"
        return result

    class Meta:
        model = models.Version
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'VDescription': {'required': False}}
        read_only_fields = ('id',)


class ProjectTaskOwnerSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTaskOwner
        exclude = ('CreationTime', 'IsActive')


class ProjectTaskDependencySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTaskDependency
        exclude = ('CreationTime', 'IsActive')


class IssueActivitySerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    action_flag_name = serializers.SerializerMethodField()
    action_type_name = serializers.SerializerMethodField()
    creator_name = serializers.SerializerMethodField()
    FieldDesc = serializers.SerializerMethodField()

    def get_action_flag_name(self, obj):
        result = ''
        if obj.ActionFlag == 1:
            result = "添加了"

        if obj.ActionFlag == 2:
            result = "更新了"

        if obj.ActionFlag == 3:
            result = "删除了"
        return result

    def get_action_type_name(self, obj):
        result = ''
        if obj.ActionType == 1:
            result = "问题"

        if obj.ActionType == 2:
            result = "备注"

        return result

    def get_creator_name(self, obj):
        processor = User.objects.get(id=obj.Creator)
        result = processor.username
        if processor.first_name and processor.last_name:
            result = processor.last_name + processor.first_name
        return result

    def get_FieldDesc(self, obj):
        result = ''
        if obj.FieldName == "0":
            return result
        if obj.FieldName != '':
            issue = models.ProjectIssue()
            result = issue.get_field_verbose_name(obj.FieldName)
        return result

    class Meta:
        model = models.IssueActivity
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        # extra_kwargs = {'NewValue': {'blank': True}, 'FieldName': {'required': False}, 'OldValue': {'required': False}}


class ProjectSerializer(serializers.ModelSerializer):
    Versions = serializers.SerializerMethodField(method_name="versions")
    Members = serializers.SerializerMethodField(method_name="members")
    Modules = serializers.SerializerMethodField(method_name="modules")
    Display = serializers.SerializerMethodField()
    LatestVersion = serializers.SerializerMethodField()
    view_data = serializers.SerializerMethodField()

    class Meta:
        model = models.Project
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id', 'Versions', 'Members')

    def get_Display(self, obj):
        return True

    def get_view_data(self, obj):
        result = dict()
        result["case_count"] = self.get_case_count(obj)
        return result

    def get_case_count(self, obj):
        case_list = models.ProjectTestCase.objects.all().filter(Project=obj.id).filter(IsGroup=0)
        return len(case_list)

    def versions(self, obj):
        result = list()
        versions = models.Version.objects.get_versions(obj.id).order_by('-id')
        for version in versions:
            temp = ProjectVersionSerializer(instance=version)
            result.append(temp.data)
        return result

    def get_LatestVersion(self, obj):
        result = 0
        versions = models.Version.objects.get_versions(obj.id).order_by('-id')
        if len(versions) > 0:
            result = versions[0].id
        return result

    def members(self, obj):
        result = list()
        members = models.ProjectMember.objects.get_members(obj.id)
        for member in members:
            temp = ProjectMemberSerializer(instance=member)
            result.append(temp.data)
        return result

    def modules(self, obj):
        result = list()
        modules = models.ProjectModule.objects.project_modules(obj.id)
        for module in modules:
            temp = ProjectModuleSerializer(instance=module)
            result.append(temp.data)
        return result


class ProjectListSerializer(serializers.ModelSerializer):
    Display = serializers.SerializerMethodField()

    class Meta:
        model = models.Project
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id', 'Versions', 'Members')

    def get_Display(self, obj):
        return True


class ProjectFortestingStatusSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()

    class Meta:
        model = models.Project
        exclude = ('CreationTime', 'IsActive', 'PBLead', 'PBDescription', 'PBCreator', 'PBVisiableLevel', 'PBHttpUrl', 'PBKey')
        read_only_fields = ('id', 'Versions', 'Members')

    def get_IssueCount(self, obj):
        result = dict()
        version_ids = obj.fortestings.values_list('VersionID', flat=True)
        all_issues = models.ProjectIssue.objects.get_project_issue(obj.id).filter(Version__in=version_ids)
        opened_issues = all_issues.filter(Status__in=[2, 5])
        result['opened'] = len(opened_issues)
        result['total'] = len(all_issues)
        return result

    def get_FortestingsFormat(self, obj):
        result = list()
        for fortesting in obj.fortestings:
            temp_serializer = ProjectForTestingListSerializer(instance=fortesting)
            result.append(temp_serializer.data)
        return result

    def get_ViewData(self, obj):
        result = dict()
        result['Display'] = True
        result['Fortestings'] = self.get_FortestingsFormat(obj)
        result['IssueCount'] = self.get_IssueCount(obj)
        return result


class ProjectTaskListSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()

    class Meta:
        model = models.Project
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id', 'Versions', 'Members')

    def get_Tasks(self, obj):
        result = list()
        my_tasks = self.get_TaskList(obj)
        for task in my_tasks:
            temp_serializer = ProjectTaskSerializer(instance=task)
            result.append(temp_serializer.data)
        return result

    def get_pending_task_count(self, obj):
        result = list()
        my_tasks = self.get_TaskList(obj).filter(Status=0)
        return len(my_tasks)

    def get_TaskList(self, obj):
        my_tasks = models.Task.objects.get_tasks(int(obj.id))
        if obj.date_range is not None:
            start_date = DateTimeHelper.add_day(DateTimeHelper.getcnow()[0:10], int(obj.date_range))
            my_tasks = my_tasks.filter(CreationTime__gte=start_date)
        if obj.status is not None:
            if str(obj.status) == "-1":
                my_tasks = my_tasks.filter(Status__in=[1, 2, 3, 0])
            else:
                my_tasks = my_tasks.filter(Status=int(obj.status))
        return my_tasks.order_by("DeadLine")

    def get_ViewData(self, obj):
        result = dict()
        result['tasks'] = self.get_Tasks(obj)
        result['pending_task_count'] = self.get_pending_task_count(obj)
        return result


class ProjectTestCaseFileSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()

    class Meta:
        model = models.Project
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id', 'Versions', 'Members')

    def get_ViewData(self, obj):
        result = dict()
        result['MindFiles'] = self.get_MindFileFormat(obj)
        return result

    def get_MindFileFormat(self, obj):
        result = list()
        my_mind_files = self.get_MindFileList(obj)
        for mind_file in my_mind_files:
            temp_serializer = ProjectMindFileSerializer(instance=mind_file)
            result.append(temp_serializer.data)
        return result

    def get_MindFileList(self, obj):
        my_mind_files = models.ProjectXmindFile.objects.get_by_project(obj.id)
        if str(obj.file_type) != "0":
            my_mind_files = my_mind_files.filter(FileType=int(obj.file_type))
        return my_mind_files


class ProjectIssueSerializer(serializers.ModelSerializer):
    project_title = serializers.SerializerMethodField(required=False, read_only=True)
    default_title = serializers.SerializerMethodField(read_only=True)
    os_name = serializers.SerializerMethodField(read_only=True)
    team_name = serializers.SerializerMethodField(read_only=True)
    severity_name = serializers.SerializerMethodField(required=False, read_only=True)
    status_name = serializers.SerializerMethodField(required=False, read_only=True)
    solution_name = serializers.SerializerMethodField(required=False, read_only=True)
    # category_name = serializers.SerializerMethodField(required=False, read_only=True)
    category_name = serializers.CharField(required=False, read_only=True)
    project_phrase_name = serializers.SerializerMethodField(required=False, read_only=True)
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_date = serializers.SerializerMethodField(required=False)
    # priority_name = serializers.SerializerMethodField(required=False, read_only=True)
    priority_name = serializers.CharField(required=False, read_only=True)
    attachments_detail = serializers.SerializerMethodField()
    _discover_way = serializers.SerializerMethodField(read_only=True)
    requirement_detail = serializers.SerializerMethodField(read_only=True)
    view_data = serializers.SerializerMethodField(read_only=True)

    def get_view_data(self, obj):
        result = dict()
        result['version_name'] = self.get_version_name(obj)
        result['module_name'] = self.get_module_name(obj)
        result['creator_name'] = self.get_creator_name(obj)
        result['processor_name'] = self.get_processor_name(obj)
        return result

    def get_project_title(self, obj):
        project_title = ProjectService.get_project_name_by_id(obj.Project)
        if project_title is None:
            return "-"
        return project_title

    def get_default_title(self, obj):
        if str(obj.Team):
            default_title = "[" + obj.priority_name + " " + self.get_team_name(
                obj) + " " + self.get_project_title(obj) + " " + self.get_version_name(
                obj) + " " + self.get_module_name(obj) + "]"
        return default_title

    def get_os_name(self, obj):
        result = " "
        try:
            if obj.DeviceOS:
                result = models.ProjectOS.objects.get_byvalue(obj.DeviceOS).Name
        except Exception as ex:
            SimpleLogger.exception(ex)

        return result

    def get_team_name(self, obj):
        result = ""
        if obj.Team:
            dm_team = Team.objects.get(obj.Team)
            result = dm_team.Name
        return result

    def get_creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def get_processor_name(self, obj):
        return UserService.get_name_by_id(obj.Processor)

    def get_version_name(self, obj):
        version = VersionService.get_version_name(obj.Version)
        return version

    def get_module_name(self, obj):
        module_name = ModuleService.get_module_name_by_id(obj.Module)
        return module_name

    def get_severity_name(self, obj):
        result = dict()
        result["Name"] = obj.severity_name
        result["Label"] = obj.severity_label
        result["LabelStyle"] = obj.severity_label_style
        return result

    def get_status_name(self, obj):
        result = dict()
        result["Name"] = obj.status_name
        result["Label"] = obj.status_label
        result["LabelStyle"] = obj.status_label_style
        return result

    def get_solution_name(self, obj):
        result = dict()
        result["Name"] = obj.solution_name
        result["Label"] = obj.solution_label
        result["LabelStyle"] = obj.solution_label_style
        return result

    def get_project_phrase_name(self, obj):
        project_phrase_name = models.ProjectPhase.objects.get_byvalue(obj.ProjectPhase)
        if project_phrase_name:
            return project_phrase_name.Name
        else:
            return '-'

    def creator_avatar(self, obj):
        result = "/static/global/images/fruit-avatar/Fruit-1.png"
        try:
            creator = User.objects.get(id=obj.Creator)
            if creator.extend_info:
                result = AccountService.get_avatar_url(creator)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_update_date(self, obj):
        result = "--"
        if obj.UpdateTime:
            result = DateTimeHelper.how_long_ago((datetime.datetime.now().replace(
                tzinfo=pytz.timezone(TIME_ZONE)) - obj.UpdateTime).seconds)
        return result

    def get_attachments_detail(self, obj):
        result = list()
        if obj.Attachments is not None:
            if obj.Attachments != "":
                for file_id in eval(obj.Attachments):
                    file = FileInfo.objects.get(int(file_id))
                    if file.IsActive != 0:
                        file_serializer = FileSerializer(file)
                        result.append(file_serializer.data)
        return result

    def get__discover_way(self, obj):
        if obj.discover_way == 1:
            return "手工"
        if obj.discover_way == 2:
            return "自动化"
        if obj.discover_way == 3:
            return "流量回放"
        if obj.discover_way == 4:
            return "告警"
        if obj.discover_way == 5:
            return "其他"
        if obj.discover_way == 6:
            return "混动工程"

    def get_requirement_detail(self, obj):
        requirements = models.Requirement.objects.filter(id=obj.Requirement)
        if requirements.exists():
            return requirements[0].Title
        else:
            return None

    class Meta:
        model = models.ProjectIssue
        exclude = ('IsActive', 'UpdateTime')
        extra_kwargs = {'Creator': {'required': False}, 'Solver': {'required': False}, 'Status': {'required': False},
                        'Solution': {'required': False}, 'Project': {'required': False}, 'Version': {'required': False}}
        read_only_fields = ('id',)


class ProjectIssueListReadOnlySerializer(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    Title = serializers.CharField(read_only=True)
    status_name = serializers.SerializerMethodField(required=False, read_only=True)
    severity_name = serializers.CharField(read_only=True)
    severity_label = serializers.CharField(read_only=True)
    severity_label_style = serializers.CharField(read_only=True)
    category_name = serializers.CharField(read_only=True)
    solution_name = serializers.SerializerMethodField(required=False, read_only=True)
    priority_name = serializers.CharField(read_only=True)
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    default_title = serializers.SerializerMethodField(required=False, read_only=True)
    view_data = serializers.SerializerMethodField(required=False, read_only=True)

    def get_view_data(self, obj):
        result = dict()
        result['version_name'] = self.get_version_name(obj)
        result['module_name'] = self.get_module_name(obj)
        result['creator_name'] = self.get_creator_name(obj)
        result['processor_name'] = self.get_processor_name(obj)
        return result

    def get_default_title(self, obj):
        if str(obj.Team):
            default_title = "[" + obj.priority_name + " " + self.get_project_title(
                obj) + " " + self.get_version_name(obj) + " " + self.get_module_name(obj) + "]"
        return default_title

    def get_status_name(self, obj):
        result = dict()
        result["Name"] = obj.status_name
        result["Label"] = obj.status_label
        result["LabelStyle"] = obj.status_label_style
        return result

    def get_solution_name(self, obj):
        result = dict()
        result["Name"] = obj.solution_name
        result["Label"] = obj.solution_label
        result["LabelStyle"] = obj.solution_label_style
        return result

    def get_project_title(self, obj):
        project_title = ProjectService.get_project_name_by_id(obj.Project)
        if project_title is None:
            return "-"
        return project_title

    def get_creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def get_processor_name(self, obj):
        return UserService.get_name_by_id(obj.Processor)

    def get_module_name(self, obj):
        module_name = ModuleService.get_module_name_by_id(obj.Module)
        if module_name is None:
            return "-"
        return module_name

    def get_version_name(self, obj):
        version = VersionService.get_version_name(obj.Version)
        return version


class ProjectIssueSampleSerializer(serializers.ModelSerializer):
    project_title = serializers.SerializerMethodField()
    default_title = serializers.SerializerMethodField()
    os_name = serializers.SerializerMethodField()
    version_name = serializers.SerializerMethodField()
    project_phrase_name = serializers.SerializerMethodField()
    creator_name = serializers.SerializerMethodField()
    processor_name = serializers.SerializerMethodField()
    view_data = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()
        result['status_name'] = obj.status_name
        result['severity_name'] = obj.severity_name
        result['solution_name'] = obj.solution_name
        result['category_name'] = obj.category_name
        result['priority_name'] = obj.priority_name
        return result

    def get_default_title(self, issue):
        if str(issue.Team):
            default_title = "[" + issue.priority_name + " " + self.get_project_title(issue) + "]"
        return default_title

    def get_project_title(self, issue):
        project_title = ProjectService.get_project_name_by_id(issue.Project)
        return project_title

    def get_os_name(self, issue):
        result = " "
        try:
            if issue.DeviceOS:
                result = models.ProjectOS.objects.get_byvalue(issue.DeviceOS).Name
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_version_name(self, issue):
        result = VersionService.get_version_name(issue.Version)
        return result

    def get_project_phrase_name(self, issue):
        issue_phrase = models.ProjectPhase.objects.get_byvalue(issue.ProjectPhase)
        if issue_phrase is None:
            return '--'
        else:
            return issue_phrase.Name

    def get_creator_name(self, issue):
        return UserService.get_name_by_id(issue.Creator)

    def get_processor_name(self, issue):
        return UserService.get_name_by_id(issue.Processor)

    class Meta:
        model = models.ProjectIssue
        exclude = ('IsActive', 'Attachments')
        extra_kwargs = {'Creator': {'required': False}, 'Solver': {'required': False}, 'Status': {'required': False},
                        'Solution': {'required': False}, 'Project': {'required': False}, 'Version': {'required': False}}
        read_only_fields = ('id',)


class ProjectTestReportIssueSerializer(serializers.ModelSerializer):
    status_name = serializers.SerializerMethodField()
    creator_name = serializers.SerializerMethodField()
    processor_name = serializers.SerializerMethodField()
    priority_name = serializers.CharField(required=False, read_only=True)
    category_name = serializers.CharField(required=False, read_only=True)

    def get_status_name(self, obj):
        result = dict()
        result['Name'] = obj.status_name
        result['Label'] = obj.status_label
        result['LabelStyle'] = obj.status_label_style

        return result

    def get_creator_name(self, issue):
        return UserService.get_name_by_id(issue.Creator)

    def get_processor_name(self, issue):
        return UserService.get_name_by_id(issue.Processor)

    class Meta:
        model = models.ProjectIssue
        fields = ['id', 'Title', 'status_name', 'priority_name', 'creator_name', 'processor_name', 'category_name']


class ProjectIssueStatisticsSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectIssue
        exclude = ('IsActive', 'CreationTime', 'Title', 'Desc')
        extra_kwargs = {'Creator': {'required': False}, 'Solver': {'required': False}, 'Status': {'required': False},
                        'Solution': {'required': False}, 'Project': {'required': False}, 'Version': {'required': False}}
        read_only_fields = ('id',)


class ProjectIssueCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectIssueCategory
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectOSSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectOS
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectIssueStatuserializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectIssueStatus
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectIssueResolvedResultSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectIssueResolvedResult
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectIssueSeveritySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='Value')

    class Meta:
        model = models.ProjectIssueSeverity
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectPhraseSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectPhase
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectTaskTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTaskType
        exclude = ('IsActive', 'CreationTime')
        read_only_fields = ('id',)


class ProjectIssuePrioritySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='Value')
    label = serializers.CharField(source='Name')

    class Meta:
        model = models.ProjectIssuePriority
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectModuleSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["Progerss"] = self.get_Progress(obj)
        result["Requirements"] = list()
        return result

    def get_Progress(self, obj):
        return 0

    class Meta:
        model = models.ProjectModule
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectTaskStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTaskStatus
        exclude = ('IsActive', 'CreationTime')
        read_only_fields = ('id',)


class ProjectDocumentSerializer(serializers.ModelSerializer):
    FileName = serializers.SerializerMethodField()
    HasChild = serializers.SerializerMethodField()
    ChildCount = serializers.SerializerMethodField()
    LockerName = serializers.SerializerMethodField()
    ExcelContent = serializers.SerializerMethodField()

    def get_FileName(self, obj):
        result = '新建文件'
        file = FileInfo.objects.get(obj.FileID)
        if file:
            result = file.FileName
        return result

    def get_HasChild(self, obj):
        result = False
        child_document = models.ProjectDocument.objects.get_child_documents(obj.id)
        if len(child_document) > 0:
            result = True
        return result

    def get_ChildCount(self, obj):
        child_document = models.ProjectDocument.objects.get_child_documents(obj.id)
        return len(child_document)

    def get_LockerName(self, obj):
        result = "--"
        if obj.LockBy:
            user = UserService.get_user(obj.LockBy)
            if user:
                result = user.last_name + user.first_name
        return result

    def get_ExcelContent(self, obj):
        result = ''
        if obj.FileID:
            file = FileInfo.objects.get(obj.FileID)
            if file:
                excel_mongo = mongo_models.ProjectExcelDocumentMongoFile()
                if file.FilePath:
                    mongo_content = excel_mongo.objects.get_value(file.FilePath)
                    if mongo_content:
                        result = mongo_content['excel_content']
        return result

    class Meta:
        model = models.ProjectDocument
        exclude = ('IsActive',)
        read_only_fields = ('id',)


class ProjectTaskSerializer(serializers.ModelSerializer):
    OwnerName = serializers.SerializerMethodField(method_name='get_owner_name')
    DeadLineFormat = serializers.SerializerMethodField(method_name='deadline_format')
    StartDate = serializers.SerializerMethodField(method_name='startdate_format')
    PriorityFormator = serializers.SerializerMethodField(method_name='priority_formator')
    ChildStatus = serializers.SerializerMethodField(method_name='child_status')
    Childs = serializers.SerializerMethodField(method_name='child_tasks')
    HasChild = serializers.SerializerMethodField(method_name='has_child')
    ProjectName = serializers.SerializerMethodField()
    VersionName = serializers.SerializerMethodField()
    ViewData = serializers.SerializerMethodField()
    Owner = serializers.SerializerMethodField(method_name='owner')
    Expandend = serializers.SerializerMethodField(method_name='has_child')
    CreationTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)

    class Meta:
        model = models.Task
        fields = '__all__'
        read_only_fields = ('id', 'IsActive', 'OwnerName', 'ShowDeadLine', 'PriorityFormator')
        extra_kwargs = {'Creator': {'required': False},
                        'Description': {'required': False}, 'ProjectID': {'required': False},
                        'Child': {'required': False}}
        validators = []
        depth = 1

    def get_ViewData(self, obj):
        result = dict()
        result['Status'] = self.get_status(obj)
        result['Owner'] = self.get_owner(obj)
        result['TaskType'] = self.get_task_type(obj)
        return result

    def get_status(self, obj):
        result = "待处理"
        task_status = ProjectTaskStatusService.get_status_name(1, obj.Status)
        if task_status is not None:
            result = task_status
        return result

    def get_task_type(self, obj):
        result = dict()
        result["label"] = '默认'
        result["label_color"] = '#cccccc'
        task_type = models.ProjectTaskType.objects.get(obj.TaskType)
        if task_type is not None:
            result["label"] = task_type.Label
            result["label_color"] = task_type.LabelColor
        return result

    def get_owner(self, obj):
        result = list()
        color_list = ['#c47131', '#7265e6', '#d9a200', '#00a2ae']
        owners = models.ProjectTaskOwner.objects.get_owners(obj.id)
        index = 0;
        if len(owners) > 0:
            for owner in owners:
                user = UserService.get_user(int(owner.Owner))
                if user:
                    temp = dict()
                    temp["id"] = user.id
                    temp["full_name"] = user.last_name + user.first_name
                    temp["first_name"] = user.first_name
                    temp["color"] = color_list[index % 4]
                    result.append(temp)
                    index = index + 1
        return result

    def has_child(self, obj):
        result = False
        try:
            child_tasks = models.Task.objects.get_child_tasks(obj.id)
            if len(child_tasks) > 0:
                result = True
            else:
                result = False
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def owner(self, obj):
        result = list()
        owners = models.ProjectTaskOwner.objects.get_owners(obj.id)
        if len(owners) > 0:
            for owner in owners:
                result.append(owner.Owner)
        return result

    def get_ProjectName(self, obj):
        return ProjectService.get_project_name_by_id(obj.ProjectID)

    def get_VersionName(self, obj):
        result = '--'
        if obj.Version:
            result = models.Version.objects.get(obj.Version).VVersion
        return result

    def get_owner_name(self, obj):
        result = "--"
        owners = models.ProjectTaskOwner.objects.get_owners(obj.id)
        if len(owners) > 0:
            for owner in owners:
                user = UserService.get_user(int(owner.Owner))
                if user:
                    result = user.email[:1]
                    user_name = user.last_name + user.first_name
                    result = user_name[-2:]
        return result

    def priority_formator(self, obj):
        result = "#ccd7e5"

        if str(obj.Priority) == '3':
            result = "#db1537"

        if str(obj.Priority) == '2':
            result = "#7793bb"
        return result

    def child_status(self, obj):
        child_tasks = models.Task.objects.get_child_tasks(obj.id)
        child_finish_tasks = models.Task.objects.get_child_tasks(obj.id, 1)
        result = ""
        if len(child_tasks) > 0:
            result = str(len(child_finish_tasks)) + "/" + str(len(child_tasks))
        return result

    def child_tasks(self, obj):
        result = list()
        child_tasks = models.Task.objects.get_child_tasks(obj.id)
        for task in child_tasks:
            temp = dict()
            temp['id'] = task.id
            temp['Title'] = task.Title
            temp['Status'] = task.Status
            temp['IsActive'] = task.IsActive
            result.append(temp)
        return result

    def startdate_format(self, obj):
        result = obj.CreationTime
        if obj.StartDate and obj.StartDate != "":
            result = obj.StartDate
        return result

    def deadline_format(self, obj):
        '''
        :param obj:
        :return: 30天前
        remain_days<-30，return 30天前
        remain_days>-30，return 具体天数
        '''

        result = {"label": "", "color": ""}
        if obj.Status != 2 and obj.Status != 3:
            task = models.Task.objects.get(int(obj.id))
            remain_days = (task.DeadLine.replace(tzinfo=None) - datetime.datetime.utcnow()).days
            if remain_days < -100:
                label = '3个月前'
                result = {"label": label, "color": "#db1537"}

            if remain_days < 0 and remain_days > -100:
                label = str(abs(remain_days)) + '天前'
                result = {"label": label, "color": "error"}
            if remain_days > 0 and remain_days <= 7:
                label = str(remain_days) + '天后'
                result = {"label": label, "color": "volcano"}
            if remain_days > 7:
                label = str(remain_days) + '天后'
                result = {"label": label, "color": "default"}

            if remain_days == 0:
                result = {"label": "今天", "color": "#5578aa"}
        return result

    def validate_ProjectID(self, value):
        """

        """
        return value


class ProductSerializer(serializers.ModelSerializer):
    ViewData = serializers.SerializerMethodField()

    def get_ViewData(self, obj):
        result = dict()
        result["SpaceAdmin"] = self.get_SpaceAdmin(obj)
        return result

    def get_SpaceAdmin(self, obj):
        result = "--"
        if obj.SpaceAdmin is not None:
            user = UserService.get_user(obj.SpaceAdmin)
            if user is not None:
                result = (user.last_name + user.first_name)[-2:]
        return result

    class Meta:
        model = models.ProductSpace
        exclude = ('IsActive', 'CreationTime')
        read_only_fields = ('id',)


class ProjectForTestingSerializer(serializers.ModelSerializer):
    DeadLineFormat = serializers.SerializerMethodField(method_name='deadline')
    Attachments = serializers.SerializerMethodField()
    VersionName = serializers.SerializerMethodField()
    ProjectName = serializers.SerializerMethodField()
    FortestingFeature = serializers.SerializerMethodField()
    # CodeRepertory = serializers.SerializerMethodField()
    ReleaseDate = serializers.SerializerMethodField()
    ViewData = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)
    CommitTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)

    class Meta:
        model = models.TestApplication
        exclude = ('IsActive',)
        validators = []
        read_only_fields = ('id', 'IsActive')
        extra_kwargs = {'Status': {'required': False}, 'Testers': {'required': False}}

    def get_ViewData(self, obj):
        result = dict()
        result["duration"] = self.get_duration(obj)
        result['requirements'] = self.get_requirements(obj)
        result['status_name'] = self.get_status_name(obj)
        result['tester_name'] = self.get_tester_name(obj)
        result['owner_name'] = self.get_owner_name(obj)
        result['commitor_name'] = self.get_commitor_name(obj)
        result['creator_name'] = self.get_creator_name(obj)
        return result

    def get_duration(self, obj):
        result = ''
        if obj.Status == 2 or obj.Status == 3:
            if obj.CommitTime:
                result = (datetime.datetime.today() - obj.CommitTime.replace(tzinfo=None)).days

        if obj.Status == 4 or obj.Status == 5:
            if obj.CommitTime and obj.TestingFinishedDate:
                result = (obj.TestingFinishedDate.replace(tzinfo=None) - obj.CommitTime.replace(tzinfo=None)).days

        return result

    def get_ReleaseDate(self, obj):
        result = ""
        if obj.TestingDeadLineDate:
            result = str(DateTimeHelper.add_day(str(obj.TestingDeadLineDate)[:10], 1))[0:10]
        return result

    def get_status_name(self, obj):
        return ProjectTaskStatusService.get_status_name(2, obj.Status)

    def get_creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def get_tester_name(self, obj):
        return UserService.get_name_by_id(obj.Testers)

    def get_owner_name(self, obj):
        if obj.Commitor:
            return UserService.get_name_by_id(obj.Commitor)
        else:
            return UserService.get_name_by_id(obj.Creator)

    def get_commitor_name(self, obj):
        return UserService.get_name_by_id(obj.Commitor)

    def get_VersionName(self, obj):
        result = '--'
        if obj.VersionID:
            result = models.Version.objects.get(obj.VersionID).VVersion
        return result

    def get_FortestingFeature(self, obj):
        result = list()
        manual_reqs = obj.TestingFeature.split("{;}")
        for req in manual_reqs:
            temp = dict()
            temp['id'] = 0
            temp['name'] = req
            result.append(temp)
        requirements = models.Requirement.objects.filter(FortestingID=obj.id)
        for req in requirements:
            temp = dict()
            temp['id'] = req.id
            temp['name'] = req.Title
            result.append(temp)
        return result

    def get_ProjectName(self, obj):
        result = '--'
        if obj.ProjectID:
            result = ProjectService.get_project_name_by_id(obj.ProjectID)
        return result

    def get_Attachments(self, obj):
        result = list()
        if obj.Attachment:
            for file_id in eval(obj.Attachment):
                temp = dict()
                file = FileInfo.objects.get(int(file_id))
                if file:
                    temp['id'] = file_id
                    temp['name'] = file.FileName
                    temp['url'] = WEB_HOST + '/api/project/fortesting/download_file/' + str(file_id)
                    result.append(temp)
        return result

    def deadline(self, obj):
        '''
        :param obj:
        :return: 30天前
        remain_days<-30，return 30天前
        remain_days>-30，return 具体天数
        '''

        result = ''
        if obj.Status == 2 or obj.Status == 3:
            if obj.CommitTime:
                remain_days = (obj.CommitTime.replace(tzinfo=None) - datetime.datetime.today()).days
                if remain_days < -30:
                    result = '30天前'
                if remain_days > -30 and remain_days < 0:
                    result = str(abs(remain_days)) + '天前'
                if remain_days >= 0:
                    result = ''
        return result

    def get_requirements(self, obj):
        require_ids = models.RequirementTaskMap.objects.filter(TaskType=2).filter(TaskID=obj.id).values_list(
            'RequirementID', flat=True)
        requirement_ins = models.Requirement.objects.filter(id__in=require_ids)
        requirements = ProjectRequirementListSerializer(instance=requirement_ins, many=True)
        return requirements.data


class ProjectForTestingListSerializer(serializers.ModelSerializer):
    ProjectName = serializers.SerializerMethodField()
    ReleaseDate = serializers.SerializerMethodField()
    StatusName = serializers.SerializerMethodField()
    ViewData = serializers.SerializerMethodField()
    OwnerName = serializers.SerializerMethodField(method_name='get_owner_name')
    DeadLineFormat = serializers.SerializerMethodField(method_name='deadline')
    CreationTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)
    CommitTime = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)
    TestingStartDate = serializers.DateTimeField(format="%Y-%m-%d", required=False, read_only=True)
    TestingFinishedDate = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True)

    class Meta:
        model = models.TestApplication
        exclude = ('IsActive', 'Attachment', 'Description', 'EmailNotificationStatus', 'ProjectCode', 'TestingAdvice', 'TestingFeature')
        validators = []
        read_only_fields = ('id', 'IsActive', 'CreationTime', 'CommitTime')
        extra_kwargs = {'Status': {'required': False}, 'Testers': {'required': False}}

    def get_ViewData(self, obj):
        result = dict()
        result['duration'] = self.get_duration(obj)
        result['test_days'] = self.get_test_days(obj)
        result['version_name'] = self.get_VersionName(obj)
        result['commitor_name'] = self.get_CommitorName(obj)
        result['owner_name'] = self.get_owner_name(obj)
        return result

    def get_test_days(self, obj):
        result = 0
        if obj.Status == 3:
            if obj.TestingStartDate:
                result = (datetime.datetime.today() - obj.TestingStartDate.replace(tzinfo=None)).days
                if result < 1:
                    result = 1
            else:
                result = 0

        if obj.Status == 4 or obj.Status == 5:
            if obj.TestingFinishedDate and obj.TestingStartDate:
                result = (obj.TestingFinishedDate.replace(tzinfo=None) - obj.TestingStartDate.replace(tzinfo=None)).days
                if result < 1:
                    result = 1
        return result

    def get_duration(self, obj):
        result = 0
        if obj.Status == 2 or obj.Status == 3:
            if obj.CommitTime:
                result = (datetime.datetime.today() - obj.CommitTime.replace(tzinfo=None)).days
            else:
                result = (datetime.datetime.today() - obj.CreationTime.replace(tzinfo=None)).days

        if obj.Status == 4 or obj.Status == 5 or obj.Status == 6:
            if obj.TestingFinishedDate and obj.CommitTime:
                result = (obj.TestingFinishedDate.replace(tzinfo=None) - obj.CommitTime.replace(tzinfo=None)).days
            elif obj.TestingFinishedDate and obj.CreationTime:
                result = (obj.TestingFinishedDate.replace(tzinfo=None) - obj.CreationTime.replace(tzinfo=None)).days
        return result

    def get_ReleaseDate(self, obj):
        result = "0"
        if obj.TestingDeadLineDate:
            result = str(DateTimeHelper.add_day(str(obj.TestingDeadLineDate)[:10], 1))[0:10]
        return result

    def get_StatusName(self, obj):
        return TaskService.get_status_name(2, obj.Status)

    def get_VersionName(self, obj):
        return VersionService.get_version_name(obj.VersionID)

    def get_ProjectName(self, obj):
        return ProjectService.get_project_name_by_id(obj.ProjectID)

    def get_CommitorName(self, obj):
        return UserService.get_name_by_id(obj.Commitor)

    def get_TesterName(self, obj):
        return UserService.get_name_by_id(obj.Testers)

    def get_owner_name(self, obj):
        if obj.Commitor:
            return UserService.get_name_by_id(obj.Commitor)
        else:
            return UserService.get_name_by_id(obj.Creator)

    def deadline(self, obj):
        '''
        :param obj:
        :return: 30天前
        remain_days<-30，return 30天前
        remain_days>-30，return 具体天数
        '''

        result = ''
        if obj.Status == 2 or obj.Status == 3:
            if obj.CommitTime:
                remain_days = (obj.CommitTime.replace(tzinfo=None) - datetime.datetime.today()).days
                if remain_days < -30:
                    result = '30天前'
                if remain_days > -30 and remain_days < 0:
                    result = str(abs(remain_days)) + '天前'
                if remain_days >= 0:
                    result = ''
        return result


class VM_ProjectIssueSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    ProjectID = serializers.IntegerField()
    Project = serializers.CharField()
    Version = serializers.CharField()
    ModuleName = serializers.CharField()
    CreateTime = serializers.DateTimeField()
    Title = serializers.CharField()
    TeamName = serializers.CharField()
    ServerityLabel = serializers.CharField()
    ServerityLabelStyle = serializers.CharField()
    ServerityName = serializers.CharField()
    PriorityName = serializers.CharField()
    CategoryName = serializers.CharField()
    StatusLabel = serializers.CharField()
    StatusLabelStyle = serializers.CharField()
    StatusName = serializers.CharField()
    SolutionLabel = serializers.CharField()
    SolutionLabelStyle = serializers.CharField()
    SolutionName = serializers.CharField()
    Creator = serializers.CharField()
    Processor = serializers.CharField()
    Requirement = serializers.IntegerField()

    class QueryInfo:
        model = models.ProjectIssue
        serializer_sql = "select t2.PBTitle as Project,t2.id as ProjectID,t3.VVersion as Version,t5.`Name` as ModuleName,t1.id, " \
                         " DATE_FORMAT(date_add(t1.CreationTime, interval 8 hour),'%%Y-%%m-%%d %%T') as CreateTime, " \
                         " t1.Title,t4.`Name` as TeamName,t6.Label as ServerityLabel,t6.LabelStyle as ServerityLabelStyle, " \
                         " t6.`Name` as ServerityName ,t7.`Name` as PriorityName,t9.`Name` as CategoryName, " \
                         " t10.`Name` as PhraseName,t8.Label as StatusLabel,t8.LabelStyle as StatusLabelStyle,t8.`Name` as StatusName ," \
                         " t11.Label as SolutionLabel,t11.LabelStyle as SolutionLabelStyle,t11.`Name` as SolutionName," \
                         " CONCAT(t12.last_name,t12.first_name) as Creator,CONCAT(t13.last_name,t13.first_name) as Processor from project_issue t1" \
                         " inner join project t2 on t1.Project =t2.id " \
                         " inner join project_version t3 on t1.Version =t3.id " \
                         " inner join team_role t4 on t1.Team = t4.id " \
                         " inner join project_module t5 on t1.Module = t5.id " \
                         " inner join project_issue_severity t6 on t1.Severity = t6.`Value` " \
                         " inner join project_issue_priority t7 on t1.Priority = t7.`Value` " \
                         " inner join project_issue_status t8 on t1.`Status` = t8.`Value` " \
                         " inner join project_issue_category t9 on t1.IssueCategory = t9.`Value` " \
                         " inner join project_phase t10 on t1.ProjectPhase = t10.`Value` " \
                         " inner join project_issue_resolved_result t11 on t1.Solution = t11.`Value` " \
                         " inner join auth_user t12 on t1.Creator = t12.id " \
                         " inner join auth_user t13 on t1.Processor = t13.id " \
                         " where t1.Project in %(Project)s "
        required_parameters = ["projectids"]
        optional_parameters = ["versions", "status", "serverity", "priority", "team", "processor", "create_time",
                               "project_phrase", "category", "solution"]

        @staticmethod
        def get_sql(optional_parameters):
            result = VM_ProjectIssueSerializer.QueryInfo.serializer_sql
            optional_filters = VM_ProjectIssueSerializer.QueryInfo.get_filters()
            for parameter in optional_parameters.keys():
                temp = optional_filters.get(parameter)
                if temp is not None:
                    result = result + temp
            return result

        @staticmethod
        def get_filters():
            optional_filters = dict()
            optional_filters.keys()
            optional_filters["Version"] = "and  t1.Version in %(Version)s"
            optional_filters["Module"] = "and  t1.Module in %(Module)s"
            optional_filters["Team"] = "and  t1.Team in %s"
            optional_filters["Severity"] = "and  t1.Severity in %(Severity)s"
            optional_filters["Priority"] = "and  t1.Priority in %(Priority)s"
            optional_filters["Status"] = "and  t1.Status in %(Status)s"
            optional_filters["IssueCategory"] = "and  t1.IssueCategory in %(IssueCategory)s"
            optional_filters["ProjectPhase"] = "and  t1.ProjectPhase in %(ProjectPhase)s"
            optional_filters["Solution"] = "and  t1.Solution in %(Solution)s"
            optional_filters["Creator"] = "and  t1.Creator in %(Creator)s"
            optional_filters["Processor"] = "and  t1.Processor in %(Processor)s"
            optional_filters["Title"] = "and  t1.Title like %(Title)s"
            optional_filters["CreationTime"] = "and  t1.CreationTime > %(CreationTime)s"
            for item in optional_filters.values():
                item = " " + item + " "
            return optional_filters
