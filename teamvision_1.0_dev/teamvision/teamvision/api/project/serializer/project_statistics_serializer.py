# coding=utf-8
"""
Created on 2016-12-24

@author: lusi<PERSON>
"""

from rest_framework import serializers
from teamvision.project import models


class ProjectTestApplicationTimeStatisticsSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTestApplicationTimeStatistics
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id',)


class ProjectTestApplicationNumberStatisticsSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTestApplicationNumberStatistics
        exclude = ('CreationTime', 'IsActive')
        read_only_fields = ('id',)
