# coding=utf-8
"""
Created on 2016-10-12
@author: <PERSON><PERSON><PERSON><PERSON>
"""
from rest_framework import serializers
from business.project.project_service import ProjectService
from business.project.testreport_service import TestReportService
from teamvision.api.project.serializer.project_serializer import ProjectTestReportIssueSerializer, \
    ProjectForTestingSerializer
from teamvision.api.project.viewmodel import vm_project_testreport
from teamvision.project import models
from business.auth_user.user_service import UserService
from teamvision.api.project.serializer.project_testplan_serializer import ProjectTestPlanSimpleSerializer, \
    ProjectTestPlanCaseListSerializer
from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.home.models import FileInfo
from teamvision.api.common.serializer.file_serializer import FileSerializer


class ProjectTestReportSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    view_data = serializers.SerializerMethodField()
    attachments_detail = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        result["creator_name"] = self.creator_name(obj)
        result["requirements"], result["issue_count"] = self.requirements(obj)
        result["test_plans"], result["testcase_count"], result["fortesting"] = self.test_plans(obj)
        result["project_title"] = self.project_title(obj)
        result["requirement_count"] = len(result["requirements"])
        result["test_cases"] = self.get_test_cases(obj)
        result["issues"] = self.get_issues(obj)
        result["status_name"] = self.status_name(obj)
        result["members"] = self.get_members(obj)
        return result

    def project_title(self, obj):
        return ProjectService.get_project_name_by_id(obj.Project)

    def creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def status_name(self, obj):
        """
        0: 正在生成，1 发送完毕
        """
        if obj.Status == 0:
            return "正在生成"
        elif obj.Status == 1:
            return "发送完毕"
        elif obj.Status == 2:
            return "已绑定"
        else:
            return "--"

    def get_members(self, obj):
        result = dict()
        return result

    def test_plans(self, obj):
        result = list()
        for_testing_list = list()
        case_count = 0
        report_plans = models.ProjectTestReportTestPlan.objects.get_report_plans(obj.CopyID)
        for report_plan in report_plans:
            plan = models.ProjectTestPlan.objects.get(report_plan.TestPlan)
            if plan is not None:
                temp = ProjectTestPlanSimpleSerializer(instance=plan)
                result.append(temp.data)
                if plan.CaseCount is None:
                    plan.CaseCount = 0
                case_count = case_count + plan.CaseCount
            plan_fortesting = models.ProjectTestPlanForTesting.objects.get_plan_fortesting(report_plan.TestPlan).values_list('Fortesting',
                                                                                                                             flat=True)
            for_testing = models.TestApplication.objects.filter(id__in=plan_fortesting)
            if for_testing:
                # for_testing_list.append(ProjectForTestingSerializer(instance=for_testing, many=True).data)
                for_testing_list = for_testing_list + ProjectForTestingSerializer(instance=for_testing, many=True).data
        return result, case_count, for_testing_list

    def requirements(self, obj):
        report_requirements = TestReportService.get_test_report_requirements(obj.id)
        result = list()
        for report_req in report_requirements:
            temp = {
                "Requirement": report_req.Requirement,
                "Title": report_req.Title,
                "Status": report_req.Status,
                "BVTResult": report_req.BVTResult
            }
            result.append(temp)
        requirement_ids = report_requirements.values_list('id', flat=True)
        issue_count = models.ProjectIssue.objects.filter(Requirement__in=requirement_ids).count()
        return result, issue_count

    def get_attachments_detail(self, obj):
        result = list()
        if obj.Attachments is not None:
            for file_id in eval(obj.Attachments):
                file = FileInfo.objects.get(int(file_id))
                if file.IsActive != 0:
                    file_serializer = FileSerializer(file)
                    result.append(file_serializer.data)
        return result

    def get_test_cases(self, obj):
        testplan_cases = TestReportService.get_test_report_test_cases(obj.id)
        test_plan_case = ProjectTestPlanCaseListSerializer(testplan_cases, many=True)
        return test_plan_case.data

    def get_issues(self, obj):
        issues = TestReportService.get_test_report_issues(obj.id)
        test_plan_issues = ProjectTestReportIssueSerializer(issues, many=True)
        return test_plan_issues.data

    class Meta:
        model = models.ProjectTestReport
        exclude = ('IsActive',)
        read_only_fields = ('id', 'CreationTime')
        extra_kwargs = {'IsActive': {'required': False}, 'CopyID': {'required': False}}


class ProjectTestReportListViewSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        result["creator_name"] = self.creator_name(obj)
        result["status_name"] = self.status_name(obj)
        return result

    def project_title(self, obj):
        project_title = ProjectService.get_project_name_by_id(obj.Project)
        return project_title

    def creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def status_name(self, obj):
        """
        0: 正在生成
        1: 发送完毕
        """
        if obj.Status == 0:
            return "正在生成"
        elif obj.Status == 1:
            return "发送完毕"
        else:
            return "--"

    class Meta:
        model = models.ProjectTestReport
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestReportPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTestReportTestPlan
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestReportMemberListSerializer(serializers.Serializer):
    """
    测试报告参与人
    """
    id = serializers.IntegerField()
    PM = serializers.CharField()
    Tester = serializers.CharField()
    Dev = serializers.CharField()

    class QueryInfo:
        model = models.ProjectTestReport

        serializer_sql = "select DISTINCT t8.id," \
                         " GROUP_CONCAT( DISTINCT CONCAT_WS('', t10.last_name,t10.first_name )) as Tester," \
                         " GROUP_CONCAT( DISTINCT CONCAT_WS('',t11.last_name, t11.first_name)) as Dev," \
                         " GROUP_CONCAT( DISTINCT CONCAT_WS('',t12.last_name, t12.first_name)) as PM" \
                         " from (select  t1.id, t3.`Owner` as Tester, t5.`Creator` as Dev, t7.`Creator` as PM" \
                         " from project_test_report t1" \
                         " inner join project_test_report_test_plan t2 on t1.id =t2.Report" \
                         " inner join project_test_plan_case t3 on t2.TestPlan = t3.TestPlan" \
                         " inner join project_test_plan_fortesting t4 on t2.TestPlan = t4.TestPlan" \
                         " inner join project_test_application t5 on t5.id = t4.Fortesting" \
                         " inner join project_requirement_task t6 on t6.TaskID = t5.id" \
                         " inner join project_requirement t7 on t7.id = t6.RequirementID where t1.id = %s) t8" \
                         " inner join auth_user t10 on t8.Tester = t10.id" \
                         " inner join auth_user t11 on t8.Dev = t11.id" \
                         " inner join auth_user t12 on t8.PM=t12.id group by t8.id"

        required_parameters = ["report_id"]
        optional_parameters = []

        @staticmethod
        def get_sql(report_id):
            result = ProjectTestReportMemberListSerializer.QueryInfo.serializer_sql
            return result


class ProjectTestReportCaseResultStatisticsSerializer(serializers.Serializer):
    """
    测试计划需求数
    """
    id = serializers.IntegerField()
    # TestPlan = serializers.IntegerField()
    TestResult = serializers.IntegerField()
    ResultName = serializers.CharField()
    CaseCount = serializers.IntegerField()

    class QueryInfo:
        model = models.ProjectTestReport
        serializer_sql = "select t1.id,t1.TestResult,t2.Desc as ResultName, count(1) as CaseCount from project_test_plan_case t1 " \
                         " inner join project_task_status t2 on t1.TestResult = t2.`Status` and t2.Type=7 " \
                         " where t1.TestPlan in %s and t1.IsGroup=0 group by t1.TestResult"
        required_parameters = ["plan_ids"]
        optional_parameters = []

        @staticmethod
        def get_sql():
            result = ProjectTestReportCaseResultStatisticsSerializer.QueryInfo.serializer_sql
            return result


class ProjectTestReportWebPartListSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTestReportWebPart
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestReportRequirementSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = models.ProjectTestReportRequirements
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}
