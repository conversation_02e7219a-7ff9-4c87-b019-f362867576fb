# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views import project_issue_view, issue_statistics_view

api_issue_router = [
    re_path(r"issue$", project_issue_view.IssueView.as_view()),
    re_path(r"issue/(?P<issue_id>[\d]+)$", project_issue_view.IssueView.as_view()),
    re_path(r"issue/status$", project_issue_view.IssueStatusList.as_view()),
    re_path(r"issue/severities", project_issue_view.IssueSeverityList.as_view()),
    re_path(r"issue/resolve_results", project_issue_view.IssueResolveResultList.as_view()),
    re_path(r"issue/project_phrase", project_issue_view.IssueProjectPhraseList.as_view()),
    re_path(r"issue/phrase/(?P<id>[\d]+)", project_issue_view.IssueProjectPhraseView.as_view()),
    re_path(r"issue/categories", project_issue_view.IssueCategoryList.as_view()),
    re_path(r"issue/category/(?P<id>[\d]+)", project_issue_view.IssueCategoryView.as_view()),
    re_path(r"issue/priority", project_issue_view.IssuePriorityList.as_view()),
    re_path(r"issue/os", project_issue_view.ProjectOSList.as_view()),
    re_path(r"issue/attachments", project_issue_view.ProjectIssueAttachementListView.as_view()),
    re_path(r"issue/(?P<issue_id>.+)/attachment/(?P<file_id>.+)",
            project_issue_view.ProjectIssueAttachementView.as_view()),
    re_path(r"issue/export$", project_issue_view.ProjectIssueExportView.as_view()),
    re_path(r"issue/(?P<issue_id>.+)/activities$", project_issue_view.IssueActivityList.as_view()),
    re_path(r"issue/daily_statistics$", issue_statistics_view.IssueDailyStatisticsListView.as_view()),
    re_path(r"issue/version_statistics$", issue_statistics_view.IssueVersionStatisticsListView.as_view()),
    re_path(r"issue/daily_statistics/(?P<id>.+)$", issue_statistics_view.IssueDailyStatisticsView.as_view()),
    re_path(r"issue/version_statistics/(?P<id>.+)$", issue_statistics_view.IssueVersionStatisticsView.as_view()),
    # re_path(r"issue/list$", project_issue_view.VM_ProjectIssueReadOnlyView.as_view()),
    re_path(r"issue/list$", project_issue_view.IssueListSimpleView.as_view()),
    re_path(r"(?P<project_id>.+)/project_modules$", project_issue_view.ProjectModuleList.as_view()),
    re_path(r"(?P<project_id>[\d]+)/version/(?P<version_id>[\d]+)/issues$", project_issue_view.IssueListView.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/issue_trend_new$",
            issue_statistics_view.IssueTrendNew.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/issue_trend_total$",
            issue_statistics_view.IssueTrendTotal.as_view()),
    re_path(r"(?P<project_id>.+)/statistics/version_total_issue$", issue_statistics_view.IssueTotalByVersion.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/unclosed_issue",
            issue_statistics_view.UnclosedIssueByPeople.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/issue_count_per_module",
            issue_statistics_view.IssueCountPerModule.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/issue_count_by_severity$",
            issue_statistics_view.IssueCountBySeverity.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/issue_count_by_category$",
            issue_statistics_view.IssueCountByCategory.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/issue_count_by_resolveresult",
            issue_statistics_view.IssueCountByResolveResult.as_view()),
    re_path(r"(?P<project_id>.+)/(?P<version_id>.+)/statistics/status_summary",
            issue_statistics_view.IssueStatusCountStatisticsView.as_view()),
]
