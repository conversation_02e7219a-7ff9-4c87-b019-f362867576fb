# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views import product_space_view


api_product_space_router=[
                         re_path(r"products$", product_space_view.ProducSpacetListView.as_view()),
                         re_path(r"product/(?P<id>.+)$", product_space_view.ProductSpaceView.as_view()),
                         re_path(r"product_spaces$", product_space_view.ProducSpacetListView.as_view()),
                         re_path(r"product_space/(?P<id>.+)$", product_space_view.ProductSpaceView.as_view()),
                         re_path(r"space/user/add", product_space_view.ProductSpaceUserAddView.as_view()),
                         ]

