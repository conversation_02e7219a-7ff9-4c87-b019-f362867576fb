# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views.project_document_view import ProjectDocumentView, ProjectDocumentListView, \
    ProjectDocumentUploadView

api_document_router = [
    re_path(r"document/(?P<document_id>.+)/$", ProjectDocumentView.as_view()),
    re_path(r"(?P<project_id>.+)/documents/$", ProjectDocumentListView.as_view()),
    re_path(r"document/upload_document$", ProjectDocumentUploadView.as_view()),
    re_path(r"document/(?P<file_id>.+)/download_document$", ProjectDocumentUploadView.as_view()),
]
