# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views import project_fortesting_view

api_fortesting_router = [
    re_path(r"fortesting/(?P<fortesting_id>.+)/$", project_fortesting_view.ProjectFortestingView.as_view()),
    re_path(r"fortesting/(?P<fortesting_id>.+)/update_property$", project_fortesting_view.ProjectFortestingUpdateView.as_view()),
    re_path(r"fortesting/(?P<fortesting_id>.+)/task/(?P<task_id>.+)$", project_fortesting_view.ProjectFortestingTaskView.as_view()),
    re_path(r"fortesting/(?P<fortesting_id>.+)/project_tasks$", project_fortesting_view.ProjectFortestingTaskListView.as_view()),
    re_path(r"fortesting/(?P<fortesting_id>.+)/update_status", project_fortesting_view.ProjectFortestingUpdateStatusView.as_view()),
    re_path(r"fortesting/upload_files", project_fortesting_view.ProjectFortestingAttachementView.as_view()),
    re_path(r"fortesting/delete_file/(?P<file_id>.+)", project_fortesting_view.ProjectFortestingAttachementView.as_view()),
    re_path(r"fortesting/download_file/(?P<file_id>.+)", project_fortesting_view.ProjectFortestingAttachementView.as_view()),
    re_path(r"(?P<project_id>.+)/version/(?P<version_id>.+)/fortestings", project_fortesting_view.ProjectFortestingListView.as_view()),
    re_path(r"(?P<project_id>.+)/fortestings", project_fortesting_view.ProjectFortestingListView.as_view()),
    re_path(r"fortesting/galaxy$", project_fortesting_view.ProjectFortestingGalaxy.as_view()),
]
