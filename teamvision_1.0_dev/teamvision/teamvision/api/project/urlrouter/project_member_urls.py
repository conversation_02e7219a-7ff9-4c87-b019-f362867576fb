# coding=utf-8
"""
Created on 2014-1-5
@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import path, re_path
from teamvision.api.project.views import project_memeber_view, project_tag_view

api_member_router = [
    re_path(r"project_member/(?P<id>.+)/$", project_memeber_view.PorjectMemberView.as_view()),
    re_path(r"project_members/(?P<project_id>.+)/$", project_memeber_view.ProjectMemberListView.as_view()),
    re_path(r"(?P<project_id>.+)/project_members$", project_memeber_view.ProjectMemberListView.as_view()),
    re_path(r"(?P<project_id>.+)/project_roles$", project_memeber_view.ProjectRoleListView.as_view()),
    re_path(r"(?P<project_id>.+)/fortestings/dashboard$", project_memeber_view.ProjectFortestingStatusDashboardView.as_view()),
    re_path(r"(?P<project_id>.+)/tasks/dashboard$", project_memeber_view.ProjectTaskStatusDashboardView.as_view()),
    re_path(r"(?P<project_id>.+)/mindfiles/dashboard$", project_memeber_view.ProjectMindFileDashboardView.as_view()),
    re_path(r"members$", project_memeber_view.ProjectMemberListView.as_view()),
    re_path(r"list$", project_memeber_view.ProjectListView.as_view()),
    re_path(r"list/(?P<my>.+)$", project_memeber_view.ProjectListView.as_view()),
    re_path(r"(?P<id>.+)/detail$", project_memeber_view.ProjectView.as_view()),
    re_path(r"tags$", project_tag_view.ProjectTagListView.as_view()),
    re_path(r"^tag/(?P<id>.+)$", project_tag_view.PorjectTagView.as_view()),
]
