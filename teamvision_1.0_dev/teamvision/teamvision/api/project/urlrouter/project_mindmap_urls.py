# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views import project_mindmap_view



api_mindmap_router=[
                         re_path(r"(?P<project_id>.+)/version/(?P<version_id>.+)/mindmap_files$",project_mindmap_view.ProjectMindFileListView.as_view()),
                         re_path(r"mindmap_file/(?P<mindfile_id>.+)/mindmap_topic/(?P<id>.+)/$",project_mindmap_view.PorjectMindTopicView.as_view()),
                         re_path(r"mindmap_topic/create$",project_mindmap_view.ProjectMindTopicCreateView.as_view()),
                         re_path(r"mindmap_file/(?P<id>.+)/save$",project_mindmap_view.ProjectMindFileSaveView.as_view()),
                         re_path(r"mindmap_file/(?P<id>.+)/export$",project_mindmap_view.ProjectMindFileExportView.as_view()),
                         re_path(r"(?P<project_id>.+)/version/(?P<version_id>.+)/mind_file/import$",project_mindmap_view.ProjectMindFileImportView.as_view()),
                         re_path(r"kitymind_file/(?P<id>.+)/$",project_mindmap_view.PorjectKityMindFileView.as_view()),
                         re_path(r"mindmap_topic_tag/create$",project_mindmap_view.ProjectMindTopicTagMapCreateView.as_view()),
                         re_path(r"mindmap_topic/(?P<topic_id>.+)/tag/(?P<tag_id>.+)/delete$",project_mindmap_view.ProjectMindTopicTagMapView.as_view()),
                         re_path(r"mindmap_file/(?P<id>.+)/$",project_mindmap_view.PorjectMindFileView.as_view()),
                         re_path(r"mindmap_file/(?P<id>.+)/copy$",project_mindmap_view.PorjectMindFileView.as_view()),
                         ]

