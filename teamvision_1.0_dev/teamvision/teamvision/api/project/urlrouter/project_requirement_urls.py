# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views.project_requirement_view import ProjectRequirementView, ProjectRequirementTaskView, \
    ProjectRequirementListView, ProjectRequirementDashboardView, ProjectRequirementTaskListView, \
    ProjectRequirementListSimpleView

requirement_router = [
    re_path(r"(?P<project_id>.+)/requirements$", ProjectRequirementListView.as_view()),
    re_path(r"(?P<project_id>.+)/requirement$", ProjectRequirementListSimpleView.as_view()),
    re_path(r"requirement/(?P<req_id>.+)/$", ProjectRequirementView.as_view()),
    re_path(r"requirement/(?P<req_id>.+)/task/(?P<task_id>.+)$", ProjectRequirementTaskView.as_view()),
    re_path(r"(?P<project_id>.+)/version/(?P<version_id>.+)/project_requirements$", ProjectRequirementDashboardView.as_view()),
    re_path(r"requirement/(?P<req_id>.+)/project_tasks$", ProjectRequirementTaskListView.as_view()),
]
