# coding=utf-8
"""
Created on 2014-1-5
@author: <PERSON><PERSON><PERSON><PERSON>
"""
from django.urls import re_path
from teamvision.api.project.views import project_task_view, task_statistics_view

api_task_router = [
    re_path(r"task/(?P<task_id>\d{1,6})/$", project_task_view.PorjectTaskView.as_view()),
    re_path(r"task/(?P<task_id>\d{1,6})/child_tasks$", project_task_view.ProjectChildTaskView.as_view()),
    re_path(r"task/(?P<task_id>\d{1,6})/owner/(?P<owner_id>\d{1,6})$", project_task_view.PorjectTaskOwnerView.as_view()),
    re_path(r"task/create/$", project_task_view.ProjectTaskListView.as_view()),
    re_path(r"task/update/$", project_task_view.PorjectTaskView.as_view()),
    re_path(r"task/delete/$", project_task_view.PorjectTaskView.as_view()),
    re_path(r"(?P<project_id>.+)/version/(?P<version_id>.+)/project_tasks$", project_task_view.ProjectTaskListView.as_view()),
    re_path(r"(?P<project_id>.+)/version/(?P<version_id>.+)/statistics/task_status_pie$", task_statistics_view.TaskStatusPie.as_view()),
    re_path(r"(?P<project_id>.+)/version/(?P<version_id>.+)/statistics/task_summary_count$", task_statistics_view.TaskSummaryCount.as_view()),
    re_path(r"task/(?P<task_id>\d{1,6})/task_owners", project_task_view.ProjectTaskOwnerListView.as_view()),
    re_path(r"task_owner/(?P<id>\d{1,6})/$", project_task_view.PorjectTaskOwnerView.as_view()),
    re_path(r"task_owner/create/$", project_task_view.ProjectTaskOwnerListView.as_view()),
    re_path(r"task_owner/update/$", project_task_view.PorjectTaskOwnerView.as_view()),
    re_path(r"task_owner/delete/$", project_task_view.PorjectTaskOwnerView.as_view()),
    re_path(r"task/task_owners", project_task_view.ProjectTaskOwnerListView.as_view()),
    re_path(r"task/task_status", project_task_view.ProjectTaskStatusListView.as_view()),
    re_path(r"task_dependency/create/$", project_task_view.ProjectTaskDependencyListView.as_view()),
    re_path(r"task_dependency/update/$", project_task_view.PorjectTaskDependencyView.as_view()),
    re_path(r"task_dependency/delete/$", project_task_view.PorjectTaskDependencyView.as_view()),
    re_path(r"task_dependencies", project_task_view.ProjectTaskDependencyListView.as_view()),
    re_path(r"task_type", project_task_view.ProjectTaskTypeListView.as_view()),
]
