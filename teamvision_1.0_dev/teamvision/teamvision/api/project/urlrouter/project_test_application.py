# coding=utf-8
"""
Created on 2014-12-24

@author: lus<PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views import project_test_application_number_statistics_view, \
    project_test_application_time_statistics_view

api_test_application_router = [
    re_path(r"project_test_application_number_statistics/(?P<id>\d{1,6})",
            project_test_application_number_statistics_view.ProjectTestApplicationNumberStatisticsView.as_view()),
    re_path(r"project_test_application_number_statistics/create",
            project_test_application_number_statistics_view.ProjectTestApplicationNumberStatisticsCreateView.as_view()),
    re_path(r"project_test_application_time_statistics/(?P<id>\d{1,6})",
            project_test_application_time_statistics_view.ProjectTestApplicationTimeStatisticsView.as_view()),
    re_path(r"project_test_application_time_statistics/create",
            project_test_application_time_statistics_view.ProjectTestApplicationTimeStatisticsCreateView.as_view()),
]
