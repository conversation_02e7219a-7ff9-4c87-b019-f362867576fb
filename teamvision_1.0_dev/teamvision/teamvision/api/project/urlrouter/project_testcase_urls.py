# encoding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views import project_testcase_view, project_scenes_view
from teamvision.api.project.views.project_testcase_view import ProjectTestCaseUploadXmindFileView, TestCaseCountView, \
    PorjectTestCaseGroupUpdateStatusView, ProjectTestCaseExportPytestView, ProjectMindMapCaseSaveDiffView, \
    ProjectMindMapCaseOpenedCheckView, ProjectMindMapCaseCloseOpenedView

testcase_router = [
    re_path(r"(?P<project_id>.+)/testcase/upload/xmindfile$", ProjectTestCaseUploadXmindFileView.as_view()),
    re_path(r"(?P<project_id>.+)/alltestcase/tree$", project_testcase_view.PorjectAllTestCaseTreeView.as_view()),
    re_path(r"(?P<project_id>.+)/testcase/lazyload$", project_testcase_view.PorjectTestCaseTreeLazyLoadView.as_view()),
    re_path(r"(?P<project_id>.+)/testcase/lazygrouptree$", project_testcase_view.PorjectTestCaseTreeLazyView.as_view()),
    re_path(r"(?P<project_id>.+)/testcase/tree$", project_testcase_view.PorjectTestCaseTreeView.as_view()),
    re_path(r"(?P<project_id>.+)/testcase/create$", project_testcase_view.ProjectTestCaseCreateView.as_view()),
    re_path(r"(?P<project_id>.+)/testcase/statistics$", TestCaseCountView.as_view()),
    re_path(r"testcase/group/(?P<group_id>.+)/export$", project_testcase_view.ProjectTestCaseExportView.as_view()),
    re_path(r"testcase/group/(?P<group_id>.+)/exportpytest$", ProjectTestCaseExportPytestView.as_view()),
    re_path(r"testcase/(?P<case_id>.+)/copy$", project_testcase_view.ProjectTestCaseCopyView.as_view()),
    re_path(r"testcase/(?P<group_id>.+)/mindmap/save$", project_testcase_view.ProjectMindMapCaseSaveView.as_view()),
    re_path(r"testcase/(?P<group_id>.+)/mindmap/savediff$", ProjectMindMapCaseSaveDiffView.as_view()),
    re_path(r"testcase/(?P<group_id>.+)/mindmap/checkopened$", ProjectMindMapCaseOpenedCheckView.as_view()),
    re_path(r"testcase/(?P<group_id>.+)/mindmap/closeopened", ProjectMindMapCaseCloseOpenedView.as_view()),
    re_path(r"testcase/(?P<group_id>.+)/childcount$", project_testcase_view.PorjectTestCaseChildCount.as_view()),
    re_path(r"testcase/(?P<case_id>.+)/mindmap$", project_testcase_view.PorjectTestCaseMindMapView.as_view()),
    re_path(r"testcase/(?P<case_id>.+)/updatestatus$", PorjectTestCaseGroupUpdateStatusView.as_view()),
    re_path(r"testcase/(?P<case_id>.+)$", project_testcase_view.PorjectTestCaseView.as_view()),
    re_path(r"testcases$", project_testcase_view.PorjectTestCaseListView.as_view()),
    re_path(r"(?P<project_id>.+)/scenes/info$", project_scenes_view.PorjectScenesListView.as_view()),
]
