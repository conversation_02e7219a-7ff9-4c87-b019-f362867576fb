# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from teamvision.api.project.urlrouter.project_fortesting_urls import api_fortesting_router
from teamvision.api.project.urlrouter.project_member_urls import api_member_router
from teamvision.api.project.urlrouter.product_space_urls import api_product_space_router
from teamvision.api.project.urlrouter.project_module_urls import api_module_router
from teamvision.api.project.urlrouter.project_version_urls import api_version_router
from teamvision.api.project.urlrouter.issue_urls import api_issue_router
from teamvision.api.project.urlrouter.project_task_urls import api_task_router
from teamvision.api.project.urlrouter.project_report_urls import api_report_router
from teamvision.api.project.urlrouter.project_document_urls import api_document_router
from teamvision.api.project.urlrouter.project_mindmap_urls import api_mindmap_router
from teamvision.api.project.urlrouter.project_requirement_urls import requirement_router
from teamvision.api.project.urlrouter.project_test_application import api_test_application_router
from teamvision.api.project.urlrouter.project_testcase_urls import testcase_router
from teamvision.api.project.urlrouter.testplan_urls import testplan_router
from teamvision.api.project.urlrouter.testreport_urls import testreport_router
from teamvision.api.project.urlrouter.project_autocase_urls import autocase_router
from teamvision.api.project.urlrouter.test_case_review_urls import case_review_router

urlpatterns = api_fortesting_router + api_version_router + api_member_router + api_issue_router
urlpatterns = urlpatterns + api_task_router + api_module_router + api_report_router + api_document_router
urlpatterns = urlpatterns + api_mindmap_router + requirement_router + api_product_space_router
urlpatterns = urlpatterns + api_test_application_router + testcase_router
urlpatterns = urlpatterns + testplan_router + testreport_router + autocase_router + case_review_router
