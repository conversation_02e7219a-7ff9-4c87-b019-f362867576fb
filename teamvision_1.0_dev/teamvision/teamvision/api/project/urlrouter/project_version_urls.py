# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views import project_version_view

api_version_router = [
    re_path(r"project_version/(?P<id>.+)/$", project_version_view.ProjectVersionView.as_view()),
    re_path(r"project_versions/(?P<project_id>.+)/$", project_version_view.ProjectVersionListView.as_view()),
    re_path(r"(?P<project_id>.+)/versions$", project_version_view.ProjectVersionListView.as_view()),
]
