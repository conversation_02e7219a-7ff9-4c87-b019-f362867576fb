# coding=utf-8
"""
测试用例评审
"""

from django.urls import re_path
from teamvision.api.project.views.case_review_view import \
    PorjectCaseReviewMindMapView, TestCaseReviewCaseTreeView, TestCaseReviewView, TestCaseReviewMindMapSaveView, \
    TestCaseReviewListView

case_review_router = [
    re_path(r"(?P<project_id>.+)/casereview$", TestCaseReviewListView.as_view()),
    re_path(r"casereview/mindmap/(?P<casereview_id>.+)$", PorjectCaseReviewMindMapView.as_view()),
    re_path(r"casereview/case_tree/(?P<casereview_id>.+)$", TestCaseReviewCaseTreeView.as_view()),
    re_path(r"casereview/(?P<review_id>.+)/mindmap/save$", TestCaseReviewMindMapSaveView.as_view()),
    re_path(r"(?P<project_id>.+)/casereview/(?P<casereview_id>.+)", TestCaseReviewView.as_view()),
]
