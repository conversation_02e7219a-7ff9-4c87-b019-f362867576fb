# coding=utf-8
"""
Created on 2014-1-5

@author: <PERSON><PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views.project_testplan_view import ProjectTestPlanCaseView, \
    ProjectTestPlanUpdateTestCaseView, ProjectTestPlanCaseResultView, ProjectTestPlanCreateView, \
    ProjectVersionTestPlanView, ProjectTestPlanCaseTreeTypeView, ProjectTestPlanListInfoListView, \
    ProjectTestPlanOwnerListView, ProjectTestPlanUpdateStatusView, ProjectTestPlanUpdateView, \
    ProjectTestPlanCaseTreeView, ProjectTestPlanView, ProjectTestPlanCaseResultListView, \
    ProjectVersionTestPlanListView, ProjectTestPlanVersionsView, ProjectTestPlanSimpleListView, ProjectTestPlansView, \
    ProjectTestPlanCaseTreeLazyLoadView

testplan_router = [
    re_path(r"testplan/testplancases/(?P<plan_id>.+)$", ProjectTestPlanUpdateTestCaseView.as_view()),
    re_path(r"testplan/case/(?P<case_id>.+)$", ProjectTestPlanCaseView.as_view()),
    re_path(r"testplan/case_result/(?P<plan_case_id>.+)$", ProjectTestPlanCaseResultView.as_view()),
    re_path(r"testplan/create$", ProjectTestPlanCreateView.as_view()),
    re_path(r"testplan/version/(?P<version_id>.+)", ProjectVersionTestPlanView.as_view()),
    re_path(r"testplan/(?P<plan_id>.+)/case_tree_info/(?P<type>.+)/$", ProjectTestPlanCaseTreeTypeView.as_view()),
    re_path(r"testplan/(?P<plan_id>.+)/update_status", ProjectTestPlanUpdateStatusView.as_view()),
    re_path(r"testplan/(?P<plan_id>.+)/update_owners", ProjectTestPlanUpdateView.as_view()),
    re_path(r"testplan/(?P<plan_id>.+)/case_tree$", ProjectTestPlanCaseTreeView.as_view()),
    re_path(r"testplan/(?P<plan_id>.+)/case_tree/lazyload", ProjectTestPlanCaseTreeLazyLoadView.as_view()),
    re_path(r"testplan/(?P<plan_id>.+)$", ProjectTestPlanView.as_view()),
    re_path(r"testplan/case_results$", ProjectTestPlanCaseResultListView.as_view()),
    re_path(r"(?P<project_id>.+)/version/testplans$", ProjectVersionTestPlanListView.as_view()),
    re_path(r"(?P<project_id>.+)/testplan/versions", ProjectTestPlanVersionsView.as_view()),
    re_path(r"(?P<project_id>.+)/testplans$", ProjectTestPlanSimpleListView.as_view()),
    re_path(r"(?P<project_id>.+)/testplans/(?P<count>.+)$", ProjectTestPlansView.as_view()),
    # re_path(r"(?P<project_id>.+)/testplan_list_info/(?P<type>.+)/$", ProjectTestPlanListInfoListView.as_view()),
    re_path(r"(?P<project_id>.+)/testplan_list_info?", ProjectTestPlanOwnerListView.as_view()),
]
