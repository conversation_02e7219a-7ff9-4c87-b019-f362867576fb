# coding=utf-8
"""
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
"""

from django.urls import re_path
from teamvision.api.project.views.project_testreport_view import ProjectTestReportCreateView, \
    ProjectTestReportCopyView, ProjectTestReportListView, ProjectTestReportWebPartListView, \
    ProjectTestReportMemberListView, ProjectTestReportCaseResultStatisticsView, TestReportView, \
    TestReportIssueListView, TestReportCaseListView, TestReportSend, TestReportAttachementListView

testreport_router = [
    re_path(r"testreport/create$", ProjectTestReportCreateView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)/attachments", TestReportAttachementListView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)/cases$", TestReportCaseListView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)/issues$", TestReportIssueListView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)/copy$", ProjectTestReportCopyView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)/send$", TestReportSend.as_view()),
    re_path(r"(?P<project_id>.+)/testreports$", ProjectTestReportListView.as_view()),
    re_path(r"(?P<project_id>.+)/testreport/webparts$", ProjectTestReportWebPartListView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)/teamates$", ProjectTestReportMemberListView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)/caseresult/statistics$", ProjectTestReportCaseResultStatisticsView.as_view()),
    re_path(r"testreport/(?P<report_id>.+)$", TestReportView.as_view()),
]
