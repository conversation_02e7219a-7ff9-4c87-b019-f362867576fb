<div id="autoagentlist">
	<ul id="autoagentlisturl">
		<li id="autoagenttitlerow">
			<!-- <input id="autoagentselectall" type="checkbox" disabled=""/> -->
			<span class="autoagentidheader">ID</span>
			<span class="autoagentnameheader">名称</span>
			<span class="autoagentipheader">IP</span>
			<span class="autoagentosheader">操作系统</span>
			<span class="autoagentbrowsersheader">浏览器</span>
			<span class="autoagentstatusheader">状态</span>
			<span class="autoagenoperationheader">操作</span>
		</li>
		{% for autoagent in autoagentlist %}
		  <li class="autoagentdatarow">
		  	<!-- <input name="selectautoagent" type="checkbox" /> -->
		    <span class="autoagentidcontent">
		    	{{ autoagent.autoagent.id }}
		    </span>
		    <span class="autoagentnamecontent">{{ autoagent.get_autoagent_name }}</span>
		    <span class="autoagentipcontent">{{ autoagent.get_ip }}</span>
		    <span class="autoagentoscontent">{{ autoagent.get_os }}</span>
		    <span class="autoagentbrowserscontent">{{ autoagent.get_browsers }}</span>
		    <span class="autoagentstatuscontent">{{ autoagent.get_status }}</span>
		    <span class="autoagentoperationcontent">  
		    	<button name="autoagentrowedit" class="button gray operationbutton">编辑</button> 
			</span>
		  </li>
       {% endfor %}
	</ul>
	
</div>