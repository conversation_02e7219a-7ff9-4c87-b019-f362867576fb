# coding=utf-8
'''
Created on 2014-1-5

@author: ETHAN
'''

from django.urls import re_path
from teamvision.automationtesting.views.autorunresultview import create_add
from teamvision.automationtesting.views.autorunresultview import update_edit
from teamvision.automationtesting.views.autorunresultview import index_list
from teamvision.automationtesting.views.autorunresultview import get_list, init_autorunresult_formcontrol, \
    get_autorunresult_page_counts
from teamvision.automationtesting.views.autorunresultview import copy_autorunresult, delete_autorunresult

urlpatterns = [
    re_path(r"create", create_add),
    re_path(r"edit", update_edit),
    re_path(r"index", index_list),
    re_path(r"getlist", get_list),
    re_path(r"init_autorunresult_formcontrol", init_autorunresult_formcontrol),
    re_path(r"get_autorunresult_page_counts", get_autorunresult_page_counts),
    re_path(r"copyautorunresult", copy_autorunresult),
    re_path(r"deleteautorunresult", delete_autorunresult)
]
