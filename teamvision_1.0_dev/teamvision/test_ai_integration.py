#!/usr/bin/env python
# coding=utf-8
"""
AI集成测试脚本
用于测试RAGFlow集成和AI功能
"""

import os
import sys
import django
from datetime import timedelta
from django.conf import settings

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
django.setup()

from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone
from teamvision.project.models import Project
from teamvision.ai.services import AITestCaseService
from teamvision.ai.ragflow_client import ragflow_client, TestCaseGenerationRequest


def test_ragflow_connection():
    """测试RAGFlow连接"""
    print("🔗 测试RAGFlow连接...")
    
    try:
        # 创建测试请求
        request = TestCaseGenerationRequest(
            requirement_description="测试用户登录功能",
            module_name="用户管理",
            project_context="Web应用用户管理系统",
            generation_type="functional",
            case_count=3
        )
        
        # 调用RAGFlow
        response = ragflow_client.generate_test_cases(request)
        
        if response.success:
            print("✅ RAGFlow连接成功")
            print(f"   生成了 {len(response.test_cases)} 个测试用例")
            for i, case in enumerate(response.test_cases[:2], 1):
                print(f"   用例{i}: {case.title}")
        else:
            print(f"❌ RAGFlow调用失败: {response.message}")
            
    except Exception as e:
        print(f"❌ RAGFlow连接异常: {str(e)}")
        print("   请检查RAGFlow配置是否正确")


def test_ai_service():
    """测试AI服务"""
    print("\n🤖 测试AI服务...")
    
    try:
        # 获取测试用户和项目
        user = User.objects.first()
        project = Project.objects.first()
        
        if not user or not project:
            print("❌ 需要至少一个用户和项目来进行测试")
            return
        
        print(f"   使用用户: {user.username}")
        print(f"   使用项目: {project.Name}")
        
        # 创建AI生成会话
        session = AITestCaseService.create_generation_session(
            user=user,
            project=project,
            requirement_description="用户登录功能，包括用户名密码验证、记住密码、忘记密码等功能",
            module_name="用户认证",
            project_context="Web应用的用户管理系统",
            generation_type="functional",
            case_count=5
        )
        
        print(f"✅ 创建AI会话成功: {session.session_id}")
        
        # 生成测试用例
        response = AITestCaseService.generate_test_cases(session)
        
        if response.success:
            print(f"✅ 生成测试用例成功: {len(response.test_cases)} 个")
            
            # 显示生成的测试用例
            for i, case in enumerate(response.test_cases[:3], 1):
                print(f"\n   📋 测试用例 {i}:")
                print(f"      标题: {case.title}")
                print(f"      描述: {case.description[:100]}...")
                print(f"      优先级: {case.priority}")
                print(f"      类型: {case.test_type}")
        else:
            print(f"❌ 生成测试用例失败: {response.message}")
            
    except Exception as e:
        print(f"❌ AI服务测试异常: {str(e)}")


def test_database_models():
    """测试数据库模型"""
    print("\n💾 测试数据库模型...")
    
    try:
        from teamvision.ai.models import (
            AIGenerationSession, AIGeneratedTestCase, 
            AIChatHistory, AIConfiguration
        )
        
        # 测试模型查询
        sessions_count = AIGenerationSession.objects.count()
        cases_count = AIGeneratedTestCase.objects.count()
        history_count = AIChatHistory.objects.count()
        config_count = AIConfiguration.objects.count()
        
        print(f"✅ 数据库模型正常")
        print(f"   AI会话数量: {sessions_count}")
        print(f"   生成用例数量: {cases_count}")
        print(f"   对话历史数量: {history_count}")
        print(f"   配置数量: {config_count}")
        
    except Exception as e:
        print(f"❌ 数据库模型测试失败: {str(e)}")


def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        from django.test import Client
        from django.urls import reverse
        
        client = Client()
        
        # 测试AI会话列表API
        try:
            response = client.get('/api/ai/sessions/')
            print(f"✅ AI会话列表API: {response.status_code}")
        except Exception as e:
            print(f"❌ AI会话列表API失败: {str(e)}")
        
        # 测试AI统计API
        try:
            response = client.get('/api/ai/statistics/')
            print(f"✅ AI统计API: {response.status_code}")
        except Exception as e:
            print(f"❌ AI统计API失败: {str(e)}")
            
    except Exception as e:
        print(f"❌ API端点测试失败: {str(e)}")


def check_configuration():
    """检查配置"""
    print("\n⚙️  检查配置...")
    
    # 检查RAGFlow配置
    ragflow_url = getattr(settings, 'RAGFLOW_BASE_URL', None)
    ragflow_key = getattr(settings, 'RAGFLOW_API_KEY', None)
    ragflow_agent = getattr(settings, 'RAGFLOW_AGENT_ID', None)
    
    print(f"   RAGFlow URL: {ragflow_url or '未配置'}")
    print(f"   RAGFlow API Key: {'已配置' if ragflow_key else '未配置'}")
    print(f"   RAGFlow Agent ID: {'已配置' if ragflow_agent else '未配置'}")
    
    # 检查数据库配置
    databases = settings.DATABASES
    print(f"   数据库配置: {len(databases)} 个数据库")
    
    # 检查AI应用是否在INSTALLED_APPS中
    ai_installed = 'teamvision.ai' in settings.INSTALLED_APPS
    print(f"   AI应用安装: {'✅' if ai_installed else '❌'}")
    
    if not ai_installed:
        print("   ⚠️  请将 'teamvision.ai' 添加到 INSTALLED_APPS 中")


def test_ai_health_check():
    """测试AI健康检查"""
    print("\n🏥 测试AI健康检查...")

    try:
        from teamvision.ai.health_check import perform_ai_health_check

        health_status = perform_ai_health_check()

        print(f"✅ 健康检查完成")
        print(f"   总体状态: {health_status.get('overall', 'unknown')}")
        print(f"   RAGFlow: {health_status.get('ragflow', 'unknown')}")
        print(f"   数据库: {health_status.get('database', 'unknown')}")
        print(f"   缓存: {health_status.get('cache', 'unknown')}")
        print(f"   检查耗时: {health_status.get('check_duration', 0)}秒")

        if health_status.get('overall') == 'healthy':
            print("✅ 所有组件运行正常")
        else:
            print("⚠️  部分组件存在问题，请检查详细信息")

    except Exception as e:
        print(f"❌ 健康检查失败: {str(e)}")


def test_ai_configuration():
    """测试AI配置管理"""
    print("\n⚙️  测试AI配置管理...")

    try:
        from teamvision.ai.models import AIConfiguration

        # 创建测试配置
        test_config = AIConfiguration.objects.create(
            config_type='test',
            config_key='integration_test',
            config_value={
                'test_mode': True,
                'created_at': timezone.now().isoformat()
            },
            description='集成测试配置',
            is_active=True
        )

        print(f"✅ 创建测试配置成功: {test_config.id}")

        # 查询配置
        configs = AIConfiguration.objects.filter(config_type='test')
        print(f"✅ 查询配置成功: 找到 {configs.count()} 个测试配置")

        # 清理测试配置
        test_config.delete()
        print("✅ 清理测试配置成功")

    except Exception as e:
        print(f"❌ 配置管理测试失败: {str(e)}")


def test_ai_statistics():
    """测试AI统计功能"""
    print("\n📊 测试AI统计功能...")

    try:
        from teamvision.ai.models import AIUsageStatistics
        from django.contrib.auth.models import User
        from teamvision.project.models import Project

        user = User.objects.first()
        project = Project.objects.first()

        if not user or not project:
            print("⚠️  需要用户和项目数据来测试统计功能")
            return

        # 创建测试统计数据
        today = timezone.now().date()
        stats, created = AIUsageStatistics.objects.get_or_create(
            user=user,
            project=project,
            date=today,
            defaults={
                'generation_count': 5,
                'generated_cases_count': 25,
                'accepted_cases_count': 20,
                'optimization_count': 3,
                'chat_message_count': 15
            }
        )

        if created:
            print("✅ 创建测试统计数据成功")
        else:
            print("✅ 统计数据已存在")

        # 查询统计数据
        total_stats = AIUsageStatistics.objects.filter(
            project=project,
            date=today
        ).aggregate(
            total_generations=models.Sum('generation_count'),
            total_cases=models.Sum('generated_cases_count'),
            total_accepted=models.Sum('accepted_cases_count')
        )

        print(f"✅ 统计查询成功:")
        print(f"   总生成次数: {total_stats['total_generations'] or 0}")
        print(f"   总生成用例: {total_stats['total_cases'] or 0}")
        print(f"   总采纳用例: {total_stats['total_accepted'] or 0}")

    except Exception as e:
        print(f"❌ 统计功能测试失败: {str(e)}")


def test_performance():
    """测试性能"""
    print("\n⚡ 测试性能...")

    try:
        import time
        from teamvision.ai.models import AIGenerationSession

        # 测试数据库查询性能
        start_time = time.time()
        session_count = AIGenerationSession.objects.count()
        db_time = time.time() - start_time

        print(f"✅ 数据库查询性能: {db_time:.3f}秒 (查询 {session_count} 条记录)")

        # 测试缓存性能
        start_time = time.time()
        from django.core.cache import cache
        cache.set('performance_test', {'test': True}, timeout=60)
        cached_value = cache.get('performance_test')
        cache_time = time.time() - start_time

        if cached_value:
            print(f"✅ 缓存性能: {cache_time:.3f}秒")
        else:
            print("❌ 缓存测试失败")

        # 性能建议
        if db_time > 1.0:
            print("⚠️  数据库查询较慢，建议检查索引")
        if cache_time > 0.1:
            print("⚠️  缓存响应较慢，建议检查缓存配置")

    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")


def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告...")

    try:
        from teamvision.ai.models import (
            AIGenerationSession, AIGeneratedTestCase,
            AIChatHistory, AIConfiguration, AIUsageStatistics
        )

        report = {
            'test_time': timezone.now().isoformat(),
            'database_status': 'healthy',
            'model_counts': {
                'sessions': AIGenerationSession.objects.count(),
                'generated_cases': AIGeneratedTestCase.objects.count(),
                'chat_history': AIChatHistory.objects.count(),
                'configurations': AIConfiguration.objects.count(),
                'usage_statistics': AIUsageStatistics.objects.count()
            },
            'recent_activity': {
                'recent_sessions': AIGenerationSession.objects.filter(
                    created_time__gte=timezone.now() - timedelta(days=7)
                ).count(),
                'recent_cases': AIGeneratedTestCase.objects.filter(
                    created_time__gte=timezone.now() - timedelta(days=7)
                ).count()
            }
        }

        print("✅ 测试报告生成成功:")
        print(f"   测试时间: {report['test_time']}")
        print(f"   数据库状态: {report['database_status']}")
        print(f"   AI会话总数: {report['model_counts']['sessions']}")
        print(f"   生成用例总数: {report['model_counts']['generated_cases']}")
        print(f"   对话记录总数: {report['model_counts']['chat_history']}")
        print(f"   配置总数: {report['model_counts']['configurations']}")
        print(f"   最近7天会话: {report['recent_activity']['recent_sessions']}")
        print(f"   最近7天用例: {report['recent_activity']['recent_cases']}")

        return report

    except Exception as e:
        print(f"❌ 生成测试报告失败: {str(e)}")
        return None


def main():
    """主函数"""
    print("🚀 TeamVision AI集成测试")
    print("=" * 50)

    # 检查配置
    check_configuration()

    # 测试数据库模型
    test_database_models()

    # 测试AI配置管理
    test_ai_configuration()

    # 测试AI统计功能
    test_ai_statistics()

    # 测试API端点
    test_api_endpoints()

    # 测试RAGFlow连接
    test_ragflow_connection()

    # 测试AI服务
    test_ai_service()

    # 测试AI健康检查
    test_ai_health_check()

    # 测试性能
    test_performance()

    # 生成测试报告
    report = generate_test_report()

    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n💡 提示:")
    print("   1. 如果RAGFlow连接失败，请检查配置和服务状态")
    print("   2. 确保数据库迁移已完成: python manage.py migrate")
    print("   3. 运行初始化命令: python manage.py init_ai_data --create-default-config")
    print("   4. 查看健康检查: python manage.py shell -c \"from teamvision.ai.health_check import perform_ai_health_check; print(perform_ai_health_check())\"")

    if report:
        print(f"\n📊 系统概况:")
        print(f"   • 总计 {report['model_counts']['sessions']} 个AI会话")
        print(f"   • 总计 {report['model_counts']['generated_cases']} 个生成用例")
        print(f"   • 最近7天活跃度: {report['recent_activity']['recent_sessions']} 会话, {report['recent_activity']['recent_cases']} 用例")


if __name__ == '__main__':
    main()
