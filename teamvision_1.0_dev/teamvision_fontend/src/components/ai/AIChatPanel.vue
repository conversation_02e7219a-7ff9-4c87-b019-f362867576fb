<template>
  <div class="ai-chat-panel">
    <div class="chat-header">
      <div class="header-info">
        <Icon type="ios-chatbubbles" size="20" />
        <span class="title">AI测试助手</span>
        <Tag v-if="sessionId" color="blue" size="small">{{ sessionId.slice(-8) }}</Tag>
      </div>
      <div class="header-actions">
        <Tooltip content="清空对话">
          <Button @click="clearChat" size="small" type="text">
            <Icon type="ios-trash" />
          </Button>
        </Tooltip>
        <Tooltip content="导出对话">
          <Button @click="exportChat" size="small" type="text" :disabled="!sessionId">
            <Icon type="ios-download" />
          </Button>
        </Tooltip>
        <Tooltip content="收起面板">
          <Button @click="$emit('close')" size="small" type="text">
            <Icon type="ios-close" />
          </Button>
        </Tooltip>
      </div>
    </div>
    
    <div class="chat-messages" ref="messagesContainer">
      <!-- 欢迎消息 -->
      <div class="message-item assistant" v-if="messages.length === 0">
        <div class="message-avatar">
          <Icon type="ios-chatbubbles" />
        </div>
        <div class="message-content">
          <div class="message-bubble">
            <div class="welcome-content">
              <h4>👋 您好！我是AI测试助手</h4>
              <p>我可以帮助您：</p>
              <ul>
                <li>🎯 生成高质量测试用例</li>
                <li>🔍 分析测试覆盖度</li>
                <li>✨ 优化现有测试用例</li>
                <li>💡 提供测试建议</li>
              </ul>
              <p>请告诉我您需要什么帮助？</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 对话消息 -->
      <div 
        v-for="(message, index) in messages" 
        :key="index"
        :class="['message-item', message.type]"
      >
        <div class="message-avatar" v-if="message.type === 'assistant'">
          <Icon type="ios-chatbubbles" />
        </div>
        <div class="message-content">
          <div class="message-bubble">
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div class="message-time">{{ message.time }}</div>
          </div>
        </div>
        <div class="message-avatar" v-if="message.type === 'user'">
          <Icon type="ios-person" />
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div class="message-item assistant" v-if="isLoading">
        <div class="message-avatar">
          <Icon type="ios-chatbubbles" />
        </div>
        <div class="message-content">
          <div class="message-bubble loading">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div class="loading-text">AI正在思考中...</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="quick-actions" v-if="!isLoading">
      <div class="action-chips">
        <Tag 
          v-for="action in quickActions" 
          :key="action.key"
          @click="sendQuickMessage(action.message)"
          class="action-chip"
          color="blue"
        >
          {{ action.label }}
        </Tag>
      </div>
    </div>
    
    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-container">
        <Input
          v-model="inputMessage"
          type="textarea"
          :rows="2"
          placeholder="请输入您的问题或需求..."
          @on-enter="sendMessage"
          :disabled="isLoading"
          ref="messageInput"
        />
        <div class="input-actions">
          <Tooltip content="发送消息 (Ctrl+Enter)">
            <Button 
              @click="sendMessage" 
              type="primary" 
              size="small"
              :loading="isLoading"
              :disabled="!inputMessage.trim()"
            >
              <Icon type="ios-send" />
            </Button>
          </Tooltip>
        </div>
      </div>
    </div>
    
    <!-- 功能提示 -->
    <div class="chat-tips" v-if="showTips">
      <div class="tips-content">
        <Icon type="ios-bulb" color="#faad14" />
        <span>提示：您可以直接描述需求，我会帮您生成相应的测试用例</span>
        <Button @click="showTips = false" size="small" type="text">
          <Icon type="ios-close" />
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import aiApi from '../../api/ai'

export default {
  name: 'AIChatPanel',
  props: {
    sessionId: {
      type: String,
      default: null
    },
    projectId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      messages: [],
      inputMessage: '',
      isLoading: false,
      showTips: true,
      
      quickActions: [
        { key: 'generate', label: '生成测试用例', message: '请帮我生成测试用例' },
        { key: 'analyze', label: '分析覆盖度', message: '请分析当前的测试覆盖度' },
        { key: 'optimize', label: '优化建议', message: '请给出测试用例优化建议' },
        { key: 'help', label: '使用帮助', message: '请介绍一下您的功能' }
      ]
    }
  },
  
  mounted() {
    this.loadChatHistory()
    this.focusInput()
  },
  
  watch: {
    sessionId(newSessionId) {
      if (newSessionId) {
        this.loadChatHistory()
      }
    }
  },
  
  methods: {
    async loadChatHistory() {
      if (!this.sessionId) return
      
      try {
        const response = await aiApi.getChatHistoryApi(this.sessionId)
        
        if (response.data && response.data.length > 0) {
          this.messages = response.data.map(msg => ({
            type: msg.message_type === 'user' ? 'user' : 'assistant',
            content: msg.content,
            time: this.formatTime(new Date(msg.created_time))
          }))
          
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      } catch (error) {
        console.warn('加载对话历史失败:', error)
      }
    },
    
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return
      
      const userMessage = {
        type: 'user',
        content: this.inputMessage,
        time: this.formatTime(new Date())
      }
      
      this.messages.push(userMessage)
      const currentMessage = this.inputMessage
      this.inputMessage = ''
      this.isLoading = true
      this.showTips = false
      
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      
      try {
        let response
        
        if (this.sessionId) {
          // 使用会话对话
          const apiResponse = await aiApi.sendMessageToAIApi(this.sessionId, currentMessage)
          response = apiResponse.data.response
        } else {
          // 通用AI对话
          response = await this.handleGeneralChat(currentMessage)
        }
        
        const aiMessage = {
          type: 'assistant',
          content: response,
          time: this.formatTime(new Date())
        }
        
        this.messages.push(aiMessage)
        
        // 触发消息事件
        this.$emit('message-sent', {
          userMessage: currentMessage,
          aiResponse: response
        })
        
      } catch (error) {
        console.error('AI对话失败:', error)
        const errorMessage = {
          type: 'assistant',
          content: '抱歉，我暂时无法回应。请稍后再试或联系技术支持。',
          time: this.formatTime(new Date())
        }
        this.messages.push(errorMessage)
      } finally {
        this.isLoading = false
        this.$nextTick(() => {
          this.scrollToBottom()
          this.focusInput()
        })
      }
    },
    
    async handleGeneralChat(message) {
      // 处理通用对话，分析用户意图
      const lowerMessage = message.toLowerCase()
      
      if (lowerMessage.includes('生成') && (lowerMessage.includes('测试用例') || lowerMessage.includes('用例'))) {
        return `我可以帮您生成测试用例！请提供以下信息：
        
📋 **需要的信息：**
1. 功能需求描述
2. 模块名称
3. 测试类型（功能/边界/异常等）

💡 **示例：**
"请为用户登录功能生成功能测试用例，包括正常登录、密码错误、用户名不存在等场景"

您可以直接描述需求，我会为您生成相应的测试用例。`
      } else if (lowerMessage.includes('分析') && lowerMessage.includes('覆盖')) {
        return `我可以分析测试覆盖度！请提供：

🔍 **分析内容：**
- 需求文档或功能描述
- 现有测试用例情况

📊 **分析结果包括：**
- 功能覆盖度百分比
- 缺失的测试场景
- 改进建议

请描述您要分析的功能需求。`
      } else if (lowerMessage.includes('优化') || lowerMessage.includes('改进')) {
        return `我可以帮您优化测试用例！

✨ **优化方向：**
- 提高用例质量
- 增强可执行性
- 完善测试步骤
- 优化预期结果

请提供需要优化的测试用例内容，我会给出具体的改进建议。`
      } else if (lowerMessage.includes('帮助') || lowerMessage.includes('功能')) {
        return `我是AI测试助手，具备以下能力：

🎯 **核心功能：**
- **智能生成测试用例** - 基于需求描述自动生成
- **测试覆盖度分析** - 评估测试完整性
- **用例质量优化** - 提升测试用例质量
- **测试建议咨询** - 提供专业测试建议

💬 **使用方式：**
- 直接描述您的需求
- 使用快捷操作按钮
- 上传需求文档进行分析

有什么具体需要帮助的吗？`
      } else {
        return `我理解您的问题。作为AI测试助手，我主要专注于：

🔧 **测试相关服务：**
- 测试用例生成
- 测试策略建议  
- 质量分析评估
- 测试流程优化

如果您有测试相关的需求，请详细描述，我会尽力帮助您！`
      }
    },
    
    sendQuickMessage(message) {
      this.inputMessage = message
      this.sendMessage()
    },
    
    formatMessage(content) {
      // 格式化消息内容，支持简单的markdown
      return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>')
    },
    
    formatTime(date) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    
    focusInput() {
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.focus()
        }
      })
    },
    
    clearChat() {
      this.$Modal.confirm({
        title: '确认清空',
        content: '确定要清空所有对话记录吗？此操作不可恢复。',
        onOk: () => {
          this.messages = []
          this.showTips = true
          this.$Message.success('对话记录已清空')
        }
      })
    },
    
    async exportChat() {
      if (!this.sessionId) {
        this.$Message.warning('没有可导出的对话记录')
        return
      }
      
      try {
        await aiApi.exportChatHistoryApi(this.sessionId, 'txt')
        this.$Message.success('对话记录导出成功')
      } catch (error) {
        console.error('导出对话记录失败:', error)
        this.$Message.error('导出失败: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style scoped lang="less">
.ai-chat-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .header-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .title {
        font-weight: 600;
        font-size: 16px;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 4px;
      
      /deep/ .ivu-btn {
        color: white;
        border: none;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
  
  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    
    .message-item {
      display: flex;
      margin-bottom: 16px;
      
      &.user {
        justify-content: flex-end;
        
        .message-bubble {
          background: #1890ff;
          color: white;
          margin-left: 40px;
        }
      }
      
      &.assistant {
        justify-content: flex-start;
        
        .message-bubble {
          background: #f6f7f9;
          color: #333;
          margin-right: 40px;
        }
      }
      
      .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #e8f4fd;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        i {
          color: #1890ff;
        }
      }
      
      .message-content {
        flex: 1;
        margin: 0 12px;
        
        .message-bubble {
          padding: 12px 16px;
          border-radius: 12px;
          max-width: 100%;
          word-wrap: break-word;
          
          &.loading {
            display: flex;
            align-items: center;
            gap: 12px;
          }
          
          .message-text {
            line-height: 1.5;
            
            /deep/ {
              h4 {
                margin: 0 0 8px 0;
                color: inherit;
              }
              
              p {
                margin: 8px 0;
              }
              
              ul {
                margin: 8px 0;
                padding-left: 20px;
              }
              
              li {
                margin: 4px 0;
              }
              
              strong {
                font-weight: 600;
              }
              
              code {
                background: rgba(0, 0, 0, 0.1);
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'Monaco', 'Consolas', monospace;
              }
            }
          }
          
          .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 8px;
          }
          
          .welcome-content {
            h4 {
              margin: 0 0 12px 0;
              color: #1f2937;
            }
            
            p {
              margin: 8px 0;
              color: #4b5563;
            }
            
            ul {
              margin: 12px 0;
              padding-left: 0;
              list-style: none;
              
              li {
                margin: 8px 0;
                color: #4b5563;
                display: flex;
                align-items: center;
                gap: 8px;
              }
            }
          }
        }
      }
    }
  }
  
  .quick-actions {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    
    .action-chips {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      
      .action-chip {
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .chat-input {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    
    .input-container {
      display: flex;
      gap: 8px;
      align-items: flex-end;
      
      /deep/ .ivu-input-wrapper {
        flex: 1;
      }
      
      .input-actions {
        display: flex;
        gap: 4px;
      }
    }
  }
  
  .chat-tips {
    padding: 12px 16px;
    background: #fffbf0;
    border-top: 1px solid #ffeaa7;
    
    .tips-content {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      color: #d68910;
      
      span {
        flex: 1;
      }
    }
  }
}

// 打字指示器动画
.typing-indicator {
  display: flex;
  gap: 4px;
  
  span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #bbb;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-text {
  font-size: 13px;
  color: #666;
}
</style>
