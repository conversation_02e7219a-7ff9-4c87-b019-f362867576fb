<template>
  <div class="ai-test-case-generator">
    <!-- 生成配置面板 -->
    <div class="generator-config">
      <div class="config-header">
        <h3>AI测试用例生成器</h3>
        <p>基于需求描述智能生成高质量测试用例</p>
      </div>
      
      <div class="config-form">
        <div class="form-row">
          <label>需求描述 *</label>
          <Input 
            v-model="config.requirement_description" 
            type="textarea" 
            :rows="4" 
            placeholder="请详细描述功能需求，包括业务场景、用户操作流程等"
          />
        </div>
        
        <div class="form-row">
          <label>模块名称 *</label>
          <Input v-model="config.module_name" placeholder="如：用户管理、订单处理等" />
        </div>
        
        <div class="form-row-group">
          <div class="form-col">
            <label>生成类型</label>
            <Select v-model="config.generation_type">
              <Option value="functional">功能测试</Option>
              <Option value="boundary">边界测试</Option>
              <Option value="exception">异常测试</Option>
              <Option value="integration">集成测试</Option>
              <Option value="performance">性能测试</Option>
              <Option value="security">安全测试</Option>
            </Select>
          </div>
          
          <div class="form-col">
            <label>生成数量</label>
            <InputNumber v-model="config.case_count" :min="1" :max="50" />
          </div>
          
          <div class="form-col">
            <label>优先级</label>
            <Select v-model="config.priority_level">
              <Option value="high">高</Option>
              <Option value="medium">中</Option>
              <Option value="low">低</Option>
            </Select>
          </div>
        </div>
        
        <div class="form-row" v-if="showAdvanced">
          <label>项目上下文</label>
          <Input 
            v-model="config.project_context" 
            type="textarea" 
            :rows="2" 
            placeholder="项目背景信息，有助于生成更准确的测试用例"
          />
        </div>
        
        <div class="form-actions">
          <Button @click="showAdvanced = !showAdvanced" type="text">
            <Icon :type="showAdvanced ? 'ios-arrow-up' : 'ios-arrow-down'" />
            {{ showAdvanced ? '收起' : '高级选项' }}
          </Button>
          
          <div class="action-buttons">
            <Button @click="resetConfig">重置</Button>
            <Button type="primary" @click="generateTestCases" :loading="generating">
              <Icon type="ios-flash" />
              生成测试用例
            </Button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 生成结果 -->
    <div class="generation-results" v-if="generatedCases.length > 0">
      <div class="results-header">
        <h4>生成结果 ({{ generatedCases.length }}个)</h4>
        <div class="result-actions">
          <Button @click="selectAll" size="small">全选</Button>
          <Button @click="selectNone" size="small">取消全选</Button>
          <Button @click="optimizeSelected" :disabled="selectedCases.length === 0" size="small">
            <Icon type="ios-construct" />
            优化选中
          </Button>
          <Button @click="exportCases" :disabled="selectedCases.length === 0" size="small">
            <Icon type="ios-download" />
            导出
          </Button>
        </div>
      </div>
      
      <div class="cases-list">
        <div 
          v-for="(testCase, index) in generatedCases" 
          :key="index"
          :class="['case-item', { selected: selectedCases.includes(index) }]"
          @click="toggleCaseSelection(index)"
        >
          <div class="case-header">
            <Checkbox 
              :value="selectedCases.includes(index)"
              @on-change="toggleCaseSelection(index)"
              @click.stop
            />
            <span class="case-title">{{ testCase.title }}</span>
            <Tag :color="getPriorityColor(testCase.priority)">
              {{ getPriorityText(testCase.priority) }}
            </Tag>
            <Tag>{{ getTypeText(testCase.test_type) }}</Tag>
          </div>
          
          <div class="case-content">
            <div class="case-section">
              <label>描述:</label>
              <p>{{ testCase.description }}</p>
            </div>
            
            <div class="case-section" v-if="testCase.precondition">
              <label>前置条件:</label>
              <p>{{ testCase.precondition }}</p>
            </div>
            
            <div class="case-section" v-if="testCase.test_steps && testCase.test_steps.length > 0">
              <label>测试步骤:</label>
              <ol>
                <li v-for="(step, stepIndex) in testCase.test_steps" :key="stepIndex">
                  {{ step }}
                </li>
              </ol>
            </div>
            
            <div class="case-section">
              <label>预期结果:</label>
              <p>{{ testCase.expected_result }}</p>
            </div>
            
            <div class="case-section" v-if="testCase.tags && testCase.tags.length > 0">
              <label>标签:</label>
              <div class="tags">
                <Tag v-for="tag in testCase.tags" :key="tag" size="small">{{ tag }}</Tag>
              </div>
            </div>
          </div>
          
          <div class="case-actions">
            <Button @click.stop="editCase(index)" size="small" type="text">
              <Icon type="ios-create" />
              编辑
            </Button>
            <Button @click.stop="optimizeCase(index)" size="small" type="text">
              <Icon type="ios-construct" />
              优化
            </Button>
            <Button @click.stop="previewCase(index)" size="small" type="text">
              <Icon type="ios-eye" />
              预览
            </Button>
          </div>
        </div>
      </div>
      
      <div class="results-footer">
        <div class="selection-info">
          已选择 {{ selectedCases.length }} / {{ generatedCases.length }} 个测试用例
        </div>
        <div class="footer-actions">
          <Button @click="addToTestLibrary" :disabled="selectedCases.length === 0" type="primary">
            <Icon type="ios-add-circle" />
            添加到测试库 ({{ selectedCases.length }})
          </Button>
        </div>
      </div>
    </div>
    
    <!-- 生成进度 -->
    <div class="generation-progress" v-if="generating">
      <div class="progress-content">
        <Spin size="large" />
        <p>AI正在分析需求并生成测试用例...</p>
        <p class="progress-tip">这可能需要几秒钟时间，请耐心等待</p>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div class="empty-state" v-if="!generating && generatedCases.length === 0 && hasGenerated">
      <div class="empty-content">
        <Icon type="ios-document-outline" size="48" color="#c5c8ce" />
        <h4>暂无生成结果</h4>
        <p>请检查需求描述是否清晰，或尝试调整生成参数</p>
        <Button @click="generateTestCases" type="primary">重新生成</Button>
      </div>
    </div>
  </div>
</template>

<script>
import aiApi from '../../api/ai'

export default {
  name: 'AITestCaseGenerator',
  props: {
    projectId: {
      type: Number,
      required: true
    },
    defaultConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showAdvanced: false,
      generating: false,
      hasGenerated: false,
      
      config: {
        requirement_description: '',
        module_name: '',
        project_context: '',
        generation_type: 'functional',
        case_count: 10,
        priority_level: 'medium'
      },
      
      generatedCases: [],
      selectedCases: [],
      currentSessionId: null
    }
  },
  
  mounted() {
    // 应用默认配置
    Object.assign(this.config, this.defaultConfig)
  },
  
  methods: {
    async generateTestCases() {
      if (!this.config.requirement_description.trim()) {
        this.$Message.warning('请输入需求描述')
        return
      }
      
      if (!this.config.module_name.trim()) {
        this.$Message.warning('请输入模块名称')
        return
      }
      
      this.generating = true
      this.hasGenerated = false
      
      try {
        const response = await aiApi.quickGenerateTestCasesApi(
          this.projectId,
          this.config.requirement_description,
          {
            module_name: this.config.module_name,
            generation_type: this.config.generation_type,
            case_count: this.config.case_count,
            priority_level: this.config.priority_level,
            project_context: this.config.project_context
          }
        )
        
        if (response.data.success) {
          this.generatedCases = response.data.test_cases
          this.currentSessionId = response.data.session_id
          this.selectedCases = []
          
          this.$Message.success(`成功生成 ${response.data.generated_count} 个测试用例`)
          this.$emit('generated', {
            cases: this.generatedCases,
            sessionId: this.currentSessionId
          })
        } else {
          throw new Error(response.data.message || '生成失败')
        }
      } catch (error) {
        console.error('生成测试用例失败:', error)
        this.$Message.error('生成失败: ' + (error.message || '未知错误'))
      } finally {
        this.generating = false
        this.hasGenerated = true
      }
    },
    
    resetConfig() {
      this.config = {
        requirement_description: '',
        module_name: '',
        project_context: '',
        generation_type: 'functional',
        case_count: 10,
        priority_level: 'medium'
      }
      this.generatedCases = []
      this.selectedCases = []
      this.hasGenerated = false
    },
    
    toggleCaseSelection(index) {
      const selectedIndex = this.selectedCases.indexOf(index)
      if (selectedIndex > -1) {
        this.selectedCases.splice(selectedIndex, 1)
      } else {
        this.selectedCases.push(index)
      }
    },
    
    selectAll() {
      this.selectedCases = this.generatedCases.map((_, index) => index)
    },
    
    selectNone() {
      this.selectedCases = []
    },
    
    async optimizeCase(index) {
      // 优化单个测试用例
      this.$Message.info('优化功能开发中...')
    },
    
    async optimizeSelected() {
      // 优化选中的测试用例
      this.$Message.info('批量优化功能开发中...')
    },
    
    editCase(index) {
      // 编辑测试用例
      this.$emit('edit-case', {
        case: this.generatedCases[index],
        index: index
      })
    },
    
    previewCase(index) {
      // 预览测试用例
      this.$emit('preview-case', {
        case: this.generatedCases[index],
        index: index
      })
    },
    
    async exportCases() {
      if (!this.currentSessionId) {
        this.$Message.warning('没有可导出的测试用例')
        return
      }
      
      try {
        await aiApi.exportAIGeneratedCasesApi(this.currentSessionId, 'excel')
        this.$Message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$Message.error('导出失败: ' + (error.message || '未知错误'))
      }
    },
    
    addToTestLibrary() {
      if (this.selectedCases.length === 0) {
        this.$Message.warning('请先选择要添加的测试用例')
        return
      }
      
      const selectedTestCases = this.selectedCases.map(index => this.generatedCases[index])
      
      this.$emit('add-to-library', {
        cases: selectedTestCases,
        sessionId: this.currentSessionId
      })
    },
    
    getPriorityColor(priority) {
      const colorMap = {
        1: 'red',
        2: 'orange', 
        3: 'green'
      }
      return colorMap[priority] || 'default'
    },
    
    getPriorityText(priority) {
      const textMap = {
        1: '高',
        2: '中',
        3: '低'
      }
      return textMap[priority] || '中'
    },
    
    getTypeText(type) {
      const typeMap = {
        'functional': '功能',
        'boundary': '边界',
        'exception': '异常',
        'integration': '集成',
        'performance': '性能',
        'security': '安全'
      }
      return typeMap[type] || '功能'
    }
  }
}
</script>

<style scoped lang="less">
.ai-test-case-generator {
  .generator-config {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .config-header {
      margin-bottom: 24px;
      
      h3 {
        margin: 0 0 8px 0;
        color: #1f2937;
        font-size: 20px;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
      }
    }
    
    .config-form {
      .form-row {
        margin-bottom: 20px;
        
        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #374151;
        }
      }
      
      .form-row-group {
        display: grid;
        grid-template-columns: 1fr 120px 100px;
        gap: 16px;
        margin-bottom: 20px;
        
        .form-col {
          label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
          }
        }
      }
      
      .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24px;
        
        .action-buttons {
          display: flex;
          gap: 12px;
        }
      }
    }
  }
  
  .generation-results {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #e5e7eb;
      background: #f9fafb;
      
      h4 {
        margin: 0;
        color: #1f2937;
        font-size: 16px;
        font-weight: 600;
      }
      
      .result-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .cases-list {
      max-height: 600px;
      overflow-y: auto;
      
      .case-item {
        border-bottom: 1px solid #e5e7eb;
        cursor: pointer;
        transition: background-color 0.2s;
        
        &:hover {
          background: #f9fafb;
        }
        
        &.selected {
          background: #eff6ff;
          border-left: 3px solid #3b82f6;
        }
        
        .case-header {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px 24px 8px;
          
          .case-title {
            flex: 1;
            font-weight: 500;
            color: #1f2937;
          }
        }
        
        .case-content {
          padding: 0 24px 8px 60px;
          
          .case-section {
            margin-bottom: 12px;
            
            label {
              display: block;
              font-weight: 500;
              color: #6b7280;
              font-size: 13px;
              margin-bottom: 4px;
            }
            
            p {
              margin: 0;
              color: #374151;
              font-size: 14px;
              line-height: 1.5;
            }
            
            ol {
              margin: 0;
              padding-left: 20px;
              
              li {
                color: #374151;
                font-size: 14px;
                line-height: 1.5;
                margin-bottom: 4px;
              }
            }
            
            .tags {
              display: flex;
              gap: 4px;
              flex-wrap: wrap;
            }
          }
        }
        
        .case-actions {
          display: flex;
          gap: 8px;
          padding: 8px 24px 16px 60px;
        }
      }
    }
    
    .results-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background: #f9fafb;
      border-top: 1px solid #e5e7eb;
      
      .selection-info {
        color: #6b7280;
        font-size: 14px;
      }
    }
  }
  
  .generation-progress {
    background: white;
    border-radius: 8px;
    padding: 60px 24px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .progress-content {
      p {
        margin: 16px 0 8px;
        color: #374151;
        font-size: 16px;
      }
      
      .progress-tip {
        color: #6b7280;
        font-size: 14px;
      }
    }
  }
  
  .empty-state {
    background: white;
    border-radius: 8px;
    padding: 60px 24px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .empty-content {
      h4 {
        margin: 16px 0 8px;
        color: #6b7280;
        font-size: 16px;
      }
      
      p {
        margin: 0 0 24px;
        color: #9ca3af;
        font-size: 14px;
      }
    }
  }
}
</style>
