<template>
  <div class="ai-workspace">
    <!-- 工作区头部 -->
    <div class="workspace-header">
      <div class="header-left">
        <h2>
          <Icon type="ios-flash" />
          AI测试助手工作台
        </h2>
        <p>智能化测试用例生成与管理平台</p>
      </div>
      <div class="header-right">
        <div class="status-indicator" :class="aiServiceStatus">
          <Icon :type="getStatusIcon()" />
          <span>{{ getStatusText() }}</span>
        </div>
        <Button @click="refreshStatus" size="small" :loading="checkingStatus">
          <Icon type="ios-refresh" />
          刷新状态
        </Button>
      </div>
    </div>
    
    <!-- 功能导航 -->
    <div class="function-nav">
      <div class="nav-tabs">
        <div 
          v-for="tab in tabs" 
          :key="tab.key"
          :class="['nav-tab', { active: activeTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          <Icon :type="tab.icon" />
          <span>{{ tab.label }}</span>
          <Badge v-if="tab.badge" :count="tab.badge" />
        </div>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="workspace-content">
      <!-- 测试用例生成器 -->
      <div v-show="activeTab === 'generator'" class="content-panel">
        <AITestCaseGenerator
          :project-id="projectId"
          :default-config="defaultGeneratorConfig"
          @generated="onCasesGenerated"
          @add-to-library="onAddToLibrary"
          @edit-case="onEditCase"
          @preview-case="onPreviewCase"
        />
      </div>
      
      <!-- AI对话面板 -->
      <div v-show="activeTab === 'chat'" class="content-panel">
        <AIChatPanel
          :session-id="currentSessionId"
          :project-id="projectId"
          @message-sent="onMessageSent"
          @close="switchTab('generator')"
        />
      </div>
      
      <!-- 历史记录 -->
      <div v-show="activeTab === 'history'" class="content-panel">
        <AIHistoryPanel
          :project-id="projectId"
          @session-selected="onSessionSelected"
          @session-deleted="onSessionDeleted"
        />
      </div>
      
      <!-- 使用统计 -->
      <div v-show="activeTab === 'statistics'" class="content-panel">
        <AIStatisticsPanel
          :project-id="projectId"
        />
      </div>
      
      <!-- 设置面板 -->
      <div v-show="activeTab === 'settings'" class="content-panel">
        <AISettingsPanel
          :project-id="projectId"
          @config-updated="onConfigUpdated"
        />
      </div>
    </div>
    
    <!-- 浮动操作按钮 -->
    <div class="floating-actions" v-if="activeTab !== 'chat'">
      <Tooltip content="快速对话" placement="left">
        <Button 
          @click="switchTab('chat')" 
          type="primary" 
          shape="circle" 
          size="large"
          class="chat-fab"
        >
          <Icon type="ios-chatbubbles" size="20" />
        </Button>
      </Tooltip>
    </div>
    
    <!-- 快速生成浮窗 -->
    <Modal
      v-model="showQuickGenerator"
      title="快速生成测试用例"
      width="600"
      :mask-closable="false"
    >
      <div class="quick-generator">
        <div class="form-item">
          <label>需求描述 *</label>
          <Input 
            v-model="quickConfig.requirement" 
            type="textarea" 
            :rows="3"
            placeholder="请简要描述功能需求..."
          />
        </div>
        <div class="form-row">
          <div class="form-col">
            <label>模块名称 *</label>
            <Input v-model="quickConfig.module" placeholder="模块名称" />
          </div>
          <div class="form-col">
            <label>生成数量</label>
            <InputNumber v-model="quickConfig.count" :min="1" :max="20" />
          </div>
        </div>
      </div>
      
      <div slot="footer">
        <Button @click="showQuickGenerator = false">取消</Button>
        <Button 
          @click="quickGenerate" 
          type="primary" 
          :loading="quickGenerating"
          :disabled="!quickConfig.requirement || !quickConfig.module"
        >
          立即生成
        </Button>
      </div>
    </Modal>
    
    <!-- 用例预览模态框 -->
    <Modal
      v-model="showCasePreview"
      title="测试用例预览"
      width="800"
      :footer-hide="true"
    >
      <TestCasePreview
        v-if="previewCase"
        :test-case="previewCase"
        @edit="onEditPreviewCase"
        @accept="onAcceptPreviewCase"
      />
    </Modal>
  </div>
</template>

<script>
import AITestCaseGenerator from './AITestCaseGenerator.vue'
import AIChatPanel from './AIChatPanel.vue'
import AIHistoryPanel from './AIHistoryPanel.vue'
import AIStatisticsPanel from './AIStatisticsPanel.vue'
import AISettingsPanel from './AISettingsPanel.vue'
import TestCasePreview from './TestCasePreview.vue'
import aiApi from '../../api/ai'

export default {
  name: 'AIWorkspace',
  components: {
    AITestCaseGenerator,
    AIChatPanel,
    AIHistoryPanel,
    AIStatisticsPanel,
    AISettingsPanel,
    TestCasePreview
  },
  props: {
    projectId: {
      type: Number,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: 'generator',
      aiServiceStatus: 'unknown', // unknown, available, unavailable
      checkingStatus: false,
      currentSessionId: null,
      
      // 快速生成
      showQuickGenerator: false,
      quickGenerating: false,
      quickConfig: {
        requirement: '',
        module: '',
        count: 10
      },
      
      // 用例预览
      showCasePreview: false,
      previewCase: null,
      
      // 默认配置
      defaultGeneratorConfig: {},
      
      tabs: [
        { key: 'generator', label: '用例生成', icon: 'ios-flash', badge: 0 },
        { key: 'chat', label: 'AI对话', icon: 'ios-chatbubbles', badge: 0 },
        { key: 'history', label: '历史记录', icon: 'ios-time', badge: 0 },
        { key: 'statistics', label: '使用统计', icon: 'ios-analytics', badge: 0 },
        { key: 'settings', label: '设置', icon: 'ios-settings', badge: 0 }
      ]
    }
  },
  
  mounted() {
    this.initializeWorkspace()
  },
  
  watch: {
    visible(newVal) {
      if (newVal) {
        this.refreshStatus()
      }
    }
  },
  
  methods: {
    async initializeWorkspace() {
      await this.refreshStatus()
      this.loadDefaultConfig()
      this.updateTabBadges()
    },
    
    async refreshStatus() {
      this.checkingStatus = true
      try {
        const response = await aiApi.checkAIServiceStatusApi()
        this.aiServiceStatus = response.data.status || 'available'
      } catch (error) {
        console.warn('AI服务状态检查失败:', error)
        this.aiServiceStatus = 'unavailable'
      } finally {
        this.checkingStatus = false
      }
    },
    
    async loadDefaultConfig() {
      try {
        const response = await aiApi.getAIConfigApi('prompt', this.projectId)
        if (response.data && response.data.length > 0) {
          // 应用项目配置
          this.defaultGeneratorConfig = response.data[0].config_value || {}
        }
      } catch (error) {
        console.warn('加载默认配置失败:', error)
      }
    },
    
    async updateTabBadges() {
      try {
        // 更新历史记录数量
        const historyResponse = await aiApi.getAISessionListApi({ project_id: this.projectId })
        const historyTab = this.tabs.find(tab => tab.key === 'history')
        if (historyTab) {
          historyTab.badge = historyResponse.data.length || 0
        }
        
        // 更新今日使用统计
        const statsResponse = await aiApi.getAIUsageStatisticsApi({
          project_id: this.projectId,
          date_from: new Date().toISOString().split('T')[0]
        })
        const statsTab = this.tabs.find(tab => tab.key === 'statistics')
        if (statsTab && statsResponse.data.length > 0) {
          statsTab.badge = statsResponse.data[0].generation_count || 0
        }
      } catch (error) {
        console.warn('更新标签徽章失败:', error)
      }
    },
    
    switchTab(tabKey) {
      this.activeTab = tabKey
      this.$emit('tab-changed', tabKey)
    },
    
    getStatusIcon() {
      const iconMap = {
        available: 'ios-checkmark-circle',
        unavailable: 'ios-close-circle',
        unknown: 'ios-help-circle'
      }
      return iconMap[this.aiServiceStatus] || 'ios-help-circle'
    },
    
    getStatusText() {
      const textMap = {
        available: 'AI服务正常',
        unavailable: 'AI服务不可用',
        unknown: '检查中...'
      }
      return textMap[this.aiServiceStatus] || '未知状态'
    },
    
    // 事件处理
    onCasesGenerated(data) {
      this.currentSessionId = data.sessionId
      this.$Message.success(`成功生成 ${data.cases.length} 个测试用例`)
      this.updateTabBadges()
      
      // 自动切换到对话面板
      if (data.cases.length > 0) {
        setTimeout(() => {
          this.switchTab('chat')
        }, 1000)
      }
    },
    
    onAddToLibrary(data) {
      this.$emit('add-to-library', data)
    },
    
    onEditCase(data) {
      this.$emit('edit-case', data)
    },
    
    onPreviewCase(data) {
      this.previewCase = data.case
      this.showCasePreview = true
    },
    
    onEditPreviewCase() {
      this.showCasePreview = false
      this.$emit('edit-case', { case: this.previewCase })
    },
    
    onAcceptPreviewCase() {
      this.showCasePreview = false
      this.$emit('accept-case', { case: this.previewCase })
    },
    
    onMessageSent(data) {
      // 处理对话消息
      console.log('AI对话:', data)
    },
    
    onSessionSelected(sessionId) {
      this.currentSessionId = sessionId
      this.switchTab('chat')
    },
    
    onSessionDeleted() {
      this.updateTabBadges()
    },
    
    onConfigUpdated(config) {
      this.defaultGeneratorConfig = { ...this.defaultGeneratorConfig, ...config }
      this.$Message.success('配置已更新')
    },
    
    // 快速生成
    async quickGenerate() {
      this.quickGenerating = true
      try {
        const response = await aiApi.quickGenerateTestCasesApi(
          this.projectId,
          this.quickConfig.requirement,
          {
            module_name: this.quickConfig.module,
            case_count: this.quickConfig.count
          }
        )
        
        if (response.data.success) {
          this.currentSessionId = response.data.session_id
          this.showQuickGenerator = false
          this.switchTab('generator')
          this.$Message.success(`快速生成 ${response.data.generated_count} 个测试用例`)
        }
      } catch (error) {
        this.$Message.error('快速生成失败: ' + (error.message || '未知错误'))
      } finally {
        this.quickGenerating = false
      }
    }
  }
}
</script>

<style scoped lang="less">
.ai-workspace {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  
  .workspace-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: white;
    border-bottom: 1px solid #e8eaec;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #1f2937;
        font-size: 24px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #3b82f6;
        }
      }
      
      p {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 13px;
        font-weight: 500;
        
        &.available {
          background: #f0f9ff;
          color: #0369a1;
          border: 1px solid #bae6fd;
        }
        
        &.unavailable {
          background: #fef2f2;
          color: #dc2626;
          border: 1px solid #fecaca;
        }
        
        &.unknown {
          background: #fffbeb;
          color: #d97706;
          border: 1px solid #fed7aa;
        }
      }
    }
  }
  
  .function-nav {
    background: white;
    border-bottom: 1px solid #e8eaec;
    
    .nav-tabs {
      display: flex;
      padding: 0 24px;
      
      .nav-tab {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px 20px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.2s;
        color: #6b7280;
        font-weight: 500;
        position: relative;
        
        &:hover {
          color: #3b82f6;
          background: #f8fafc;
        }
        
        &.active {
          color: #3b82f6;
          border-bottom-color: #3b82f6;
          background: #f8fafc;
        }
        
        /deep/ .ivu-badge {
          .ivu-badge-count {
            font-size: 10px;
            min-width: 16px;
            height: 16px;
            line-height: 16px;
            padding: 0 4px;
          }
        }
      }
    }
  }
  
  .workspace-content {
    flex: 1;
    overflow: hidden;
    
    .content-panel {
      height: 100%;
      overflow: auto;
      padding: 20px;
    }
  }
  
  .floating-actions {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 1000;
    
    .chat-fab {
      width: 56px;
      height: 56px;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
      }
    }
  }
  
  .quick-generator {
    .form-item {
      margin-bottom: 20px;
      
      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #374151;
      }
    }
    
    .form-row {
      display: grid;
      grid-template-columns: 1fr 120px;
      gap: 16px;
      
      .form-col {
        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #374151;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
